{"row_number": 6088, "expected_answer": "\\( f(x) = \\frac{1}{x} \\)", "problem": "Find all functions \\( f: \\mathbb{R}^* \\to \\mathbb{R}^* \\) satisfying:\n\\[ f(x + f(y)) = \\frac{y}{xy + 1} \\]", "generated_solution": "<think>\nOkay, so I need to find all functions \\( f: \\mathbb{R}^* \\to \\mathbb{R}^* \\) that satisfy the functional equation \\( f(x + f(y)) = \\frac{y}{xy + 1} \\). Hmm, let's start by understanding the problem. The domain and codomain are both the set of nonzero real numbers, which is important because division by zero is a concern here. The equation relates the function's value at \\( x + f(y) \\) to the expression \\( \\frac{y}{xy + 1} \\). \n\nFirst, maybe I can try plugging in some specific values for \\( x \\) or \\( y \\) to see if I can find a pattern or figure out the form of \\( f \\). Let me see... If I set \\( x = 0 \\), but wait, the domain is \\( \\mathbb{R}^* \\), which is all real numbers except zero. So I can't set \\( x = 0 \\). How about setting \\( y \\) to a specific value? Let's try \\( y = 1 \\). Then the equation becomes \\( f(x + f(1)) = \\frac{1}{x \\cdot 1 + 1} = \\frac{1}{x + 1} \\). Hmm, maybe this suggests that \\( f \\) has some reciprocal relationship?\n\nAlternatively, maybe I can find an expression for \\( f(y) \\). Let's try to see if we can express \\( f(y) \\) in terms of other variables. Suppose I let \\( x = t - f(y) \\), but then \\( x \\) must be in \\( \\mathbb{R}^* \\), so \\( t - f(y) \\neq 0 \\). Then substituting into the equation, we get \\( f(t) = \\frac{y}{(t - f(y))y + 1} = \\frac{y}{ty - f(y)y + 1} \\). Not sure if that helps yet. Maybe if I can relate this to another substitution.\n\nAlternatively, maybe suppose that the function \\( f \\) is injective or surjective. Since the codomain is \\( \\mathbb{R}^* \\), which is all nonzero reals, perhaps surjective. Wait, but functional equations often use injectivity or surjectivity. Let me check if \\( f \\) is injective. Suppose \\( f(a) = f(b) \\). Then for any \\( x \\), \\( f(x + f(a)) = f(x + f(b)) \\), which implies \\( \\frac{a}{xa + 1} = \\frac{b}{xb + 1} \\). Let's see: cross-multiplying, \\( a(xb + 1) = b(xa + 1) \\). Expanding both sides: \\( axb + a = bxa + b \\). The \\( axb \\) terms cancel, so \\( a = b \\). Therefore, \\( f \\) is injective.\n\nOkay, so injectivity is established. Now, maybe try to find \\( f \\) in terms of itself. Let's see, can we substitute a particular value for \\( x \\) or \\( y \\) to get an equation involving \\( f(f(y)) \\) or something like that?\n\nLet me try setting \\( x = 1 \\). Then the equation becomes \\( f(1 + f(y)) = \\frac{y}{y + 1} \\). Hmm, not immediately helpful. Maybe if I can compose functions or iterate. Alternatively, consider substituting \\( y \\) such that \\( xy + 1 \\) is something. Wait, let's see.\n\nSuppose I set \\( x = \\frac{1 - f(y)}{y} \\). Wait, but \\( x \\) has to be in \\( \\mathbb{R}^* \\), so \\( \\frac{1 - f(y)}{y} \\neq 0 \\). That would mean \\( 1 - f(y) \\neq 0 \\times y \\), but this might complicate things. Alternatively, maybe set \\( x \\) such that the denominator becomes something. Let's think differently.\n\nSince the right-hand side is \\( \\frac{y}{xy + 1} \\), maybe try to express this as a function of \\( x + f(y) \\). Let me denote \\( z = x + f(y) \\), so \\( x = z - f(y) \\). Then substituting into RHS, we get \\( \\frac{y}{(z - f(y))y + 1} = \\frac{y}{zy - y f(y) + 1} \\). Therefore, \\( f(z) = \\frac{y}{zy - y f(y) + 1} \\). Hmm, but this must hold for some \\( y \\). Wait, but for a given \\( z \\), there should be some \\( y \\) such that \\( z = x + f(y) \\). Since the function is from \\( \\mathbb{R}^* \\) to \\( \\mathbb{R}^* \\), perhaps we can choose \\( y \\) such that for each \\( z \\), there's a corresponding \\( y \\). However, this might not be straightforward.\n\nAlternatively, perhaps consider setting \\( y \\) to a specific function of \\( x \\). Wait, but we need to relate the variables. Let me try to see if there's a way to express \\( f(y) \\) in terms of \\( y \\). Let's suppose that \\( f(y) = \\frac{c}{y} + d \\), a possible reciprocal function. Maybe this is a linear fractional function. Let's test this hypothesis. Suppose \\( f(y) = \\frac{c}{y} + d \\). Then substitute into the original equation:\n\nLeft-hand side: \\( f(x + f(y)) = f\\left(x + \\frac{c}{y} + d\\right) = \\frac{c}{x + \\frac{c}{y} + d} + d \\).\n\nRight-hand side: \\( \\frac{y}{xy + 1} \\).\n\nSet these equal:\n\n\\( \\frac{c}{x + \\frac{c}{y} + d} + d = \\frac{y}{xy + 1} \\).\n\nSimplify the left-hand side:\n\n\\( \\frac{c + d(x + \\frac{c}{y} + d)}{x + \\frac{c}{y} + d} = \\frac{y}{xy + 1} \\).\n\nLet me multiply numerator and denominator by \\( y \\):\n\nLeft-hand side becomes:\n\n\\( \\frac{c y + d y (x y + c + d y)}{y (x + \\frac{c}{y} + d)} = \\frac{c y + d x y^2 + c d y + d^2 y^2}{x y + c + d y} \\).\n\nDenominator after multiplying by y: \\( x y + c + d y \\).\n\nNumerator: \\( c y + d x y^2 + c d y + d^2 y^2 \\).\n\nSo LHS is \\( \\frac{d x y^2 + (c + c d + d^2) y}{x y + c + d y} \\).\n\nThis is supposed to equal RHS \\( \\frac{y}{x y + 1} \\).\n\nTherefore, equate numerators and denominators:\n\nNumerator: \\( d x y^2 + (c + c d + d^2) y = y \\).\n\nDenominator: \\( x y + c + d y = x y + 1 \\).\n\nSo equate denominators first:\n\n\\( x y + c + d y = x y + 1 \\). Therefore, subtracting \\( x y \\) from both sides, we get \\( c + d y = 1 \\).\n\nBut this must hold for all \\( x \\) and \\( y \\). Wait, but \\( c + d y = 1 \\) is supposed to be true for all \\( y \\)? That can't be unless \\( d = 0 \\) and \\( c = 1 \\). So if \\( d = 0 \\), then \\( c = 1 \\).\n\nNow check the numerator with \\( d = 0 \\) and \\( c = 1 \\):\n\nNumerator becomes \\( 0 \\cdot x y^2 + (1 + 0 + 0) y = y \\), which matches the RHS numerator. So denominator is \\( x y + 1 \\), which also matches. Therefore, if \\( f(y) = \\frac{1}{y} + 0 = \\frac{1}{y} \\), then the equation holds.\n\nWait, so this suggests that \\( f(y) = \\frac{1}{y} \\) is a solution. Let's verify this. If \\( f(y) = \\frac{1}{y} \\), then the left-hand side becomes \\( f(x + f(y)) = f\\left(x + \\frac{1}{y}\\right) = \\frac{1}{x + \\frac{1}{y}} = \\frac{y}{xy + 1} \\), which is exactly the right-hand side. So that works!\n\nBut the problem says \"find all functions\". So is this the only solution? We need to check if there are other possible solutions. Since the functional equation is quite restrictive, maybe this is the only one.\n\nAlternatively, perhaps there's another approach. Let's consider the substitution \\( y = f(z) \\). Wait, but since \\( f \\) is injective, for every \\( y \\), there is a unique \\( z \\) such that \\( y = f(z) \\). Let me substitute \\( y = f(z) \\) into the original equation:\n\n\\( f(x + f(f(z))) = \\frac{f(z)}{x f(z) + 1} \\).\n\nBut we don't know what \\( f(f(z)) \\) is. Maybe we can find an expression for \\( f(f(z)) \\). Let's set \\( x = 0 \\)... but wait, \\( x \\) can't be zero. Alternatively, set \\( x = t - f(f(z)) \\), but that might not be helpful. Let me think of another approach.\n\nAlternatively, let's try to find \\( f \\) by assuming it's invertible. Since \\( f \\) is injective and maps \\( \\mathbb{R}^* \\) to \\( \\mathbb{R}^* \\), it's bijective if it's surjective. Wait, but we haven't established surjectivity. Wait, if for every \\( w \\in \\mathbb{R}^* \\), there exists a \\( y \\) such that \\( f(y) = w \\). Let's check.\n\nTake any \\( w \\in \\mathbb{R}^* \\). Let me set \\( x + f(y) = w \\), so \\( x = w - f(y) \\). Then substituting into the original equation:\n\n\\( f(w) = \\frac{y}{(w - f(y)) y + 1} \\).\n\nBut this equation must hold for some \\( y \\). Since \\( x = w - f(y) \\neq 0 \\), we need \\( w - f(y) \\neq 0 \\), so \\( f(y) \\neq w \\). But if \\( f \\) is surjective, then there exists a \\( y \\) such that \\( f(y) = w \\), but that would make \\( x = 0 \\), which is not allowed. Wait, this might be a problem. Hmm. Maybe this line of thought isn't the right path.\n\nAlternatively, maybe set \\( x = \\frac{1}{y} - f(y) \\), as long as \\( \\frac{1}{y} - f(y) \\neq 0 \\). Then substituting into the original equation:\n\n\\( f\\left( \\frac{1}{y} - f(y) + f(y) \\right ) = f\\left( \\frac{1}{y} \\right ) = \\frac{y}{ \\left( \\frac{1}{y} - f(y) \\right ) y + 1 } \\).\n\nSimplify the RHS:\n\n\\( \\frac{y}{1 - f(y) y + 1} = \\frac{y}{2 - y f(y)} \\).\n\nBut LHS is \\( f\\left( \\frac{1}{y} \\right ) \\). So,\n\n\\( f\\left( \\frac{1}{y} \\right ) = \\frac{y}{2 - y f(y)} \\).\n\nThis is a relation between \\( f(1/y) \\) and \\( f(y) \\). Maybe we can use this. Let me denote \\( z = y \\), so \\( f(1/z) = \\frac{z}{2 - z f(z)} \\). Not sure if that's helpful. Let's substitute \\( f(1/z) \\) into itself. Wait, maybe let me define \\( g(z) = z f(z) \\). Then, \\( f(1/z) = \\frac{z}{2 - g(z)} \\). But \\( g(z) = z f(z) \\), so if we write \\( f(1/z) = \\frac{z}{2 - g(z)} \\), then \\( f(1/z) = \\frac{z}{2 - g(z)} \\). Let's compute \\( g(1/z) \\):\n\n\\( g(1/z) = \\frac{1}{z} f(1/z) = \\frac{1}{z} \\cdot \\frac{z}{2 - g(z)} = \\frac{1}{2 - g(z)} \\).\n\nSo, we have \\( g(1/z) = \\frac{1}{2 - g(z)} \\). Let me denote \\( h(z) = g(z) \\), so that equation becomes \\( h(1/z) = \\frac{1}{2 - h(z)} \\). Hmm, interesting. Let's suppose that \\( h(z) = 1 \\). Then, \\( h(1/z) = 1 = \\frac{1}{2 - 1} = 1 \\), so that works. So if \\( h(z) = 1 \\), then \\( g(z) = 1 \\), which implies \\( z f(z) = 1 \\), so \\( f(z) = \\frac{1}{z} \\). Which is the solution we found earlier. Are there other solutions?\n\nSuppose there's another function \\( h(z) \\) satisfying \\( h(1/z) = \\frac{1}{2 - h(z)} \\). Let's solve this functional equation. Let's set \\( w = 1/z \\), then \\( h(w) = \\frac{1}{2 - h(1/w)} \\). Substituting recursively:\n\n\\( h(w) = \\frac{1}{2 - h(1/w)} \\), but \\( h(1/w) = \\frac{1}{2 - h(w)} \\). Therefore, substituting back:\n\n\\( h(w) = \\frac{1}{2 - \\frac{1}{2 - h(w)}} \\).\n\nLet me compute this:\n\nLet \\( h = h(w) \\).\n\nThen,\n\n\\( h = \\frac{1}{2 - \\frac{1}{2 - h}} \\).\n\nMultiply numerator and denominator by \\( 2 - h \\):\n\n\\( h = \\frac{2 - h}{(2)(2 - h) - 1} \\).\n\nCompute the denominator:\n\n\\( 2(2 - h) - 1 = 4 - 2h - 1 = 3 - 2h \\).\n\nSo,\n\n\\( h = \\frac{2 - h}{3 - 2h} \\).\n\nCross-multiplying:\n\n\\( h (3 - 2h) = 2 - h \\).\n\nExpand:\n\n\\( 3h - 2h^2 = 2 - h \\).\n\nBring all terms to left side:\n\n\\( 3h - 2h^2 - 2 + h = 0 \\).\n\nCombine like terms:\n\n\\( 4h - 2h^2 - 2 = 0 \\).\n\nMultiply both sides by -1:\n\n\\( 2h^2 - 4h + 2 = 0 \\).\n\nDivide by 2:\n\n\\( h^2 - 2h + 1 = 0 \\).\n\nThis factors as:\n\n\\( (h - 1)^2 = 0 \\).\n\nThus, \\( h = 1 \\).\n\nSo the only solution is \\( h(w) = 1 \\). Therefore, \\( g(z) = 1 \\), so \\( z f(z) = 1 \\implies f(z) = \\frac{1}{z} \\).\n\nTherefore, the only solution is \\( f(y) = \\frac{1}{y} \\).\n\nBut let's check if this is the only solution. Suppose there exists another function \\( f \\) different from \\( 1/y \\) that satisfies the equation. But according to the steps above, the functional equation for \\( h(z) \\) only allows \\( h(z) = 1 \\), so \\( f(z) = 1/z \\). Therefore, this must be the unique solution.\n\nWait, but to make sure, let's check if there's another approach. Let's consider the original equation again:\n\n\\( f(x + f(y)) = \\frac{y}{x y + 1} \\).\n\nSuppose we let \\( x = -f(y) \\). But then \\( x + f(y) = 0 \\), which is not allowed since the domain is \\( \\mathbb{R}^* \\). So that's invalid. Alternatively, let's try to set \\( x = t \\), and find \\( y \\) such that \\( t + f(y) = s \\), but not sure.\n\nAlternatively, perhaps express \\( f \\) as a function that inverts something. Let me think. If \\( f(x + f(y)) = \\frac{y}{x y + 1} \\), then if we take \\( x = \\frac{1 - f(y)}{y} \\), but as before, but \\( x \\neq 0 \\).\n\nAlternatively, let's try to find \\( f(f(y)) \\). Let me set \\( x = -f(y) + z \\), but again, \\( x \\) has to be non-zero. Maybe set \\( x = a \\), then \\( f(a + f(y)) = \\frac{y}{a y + 1} \\). Let's compute \\( f(f(y)) \\). Maybe set \\( a = -f(y) + w \\), but this might not lead anywhere. Wait, if we can set \\( a = -f(y) + f(z) \\), but I don't see the connection.\n\nAlternatively, take two instances of the equation. Let's say:\n\n1. For \\( x \\) and \\( y \\): \\( f(x + f(y)) = \\frac{y}{x y + 1} \\).\n2. For \\( x' \\) and \\( y' \\): \\( f(x' + f(y')) = \\frac{y'}{x' y' + 1} \\).\n\nBut unless we relate \\( x' \\) and \\( y' \\) to \\( x \\) and \\( y \\), this might not help.\n\nWait, another idea: since we know that \\( f \\) is injective, and if we can show it's surjective, then perhaps we can find an inverse function. Let me check surjectivity.\n\nTake any \\( w \\in \\mathbb{R}^* \\). We need to find a \\( z \\in \\mathbb{R}^* \\) such that \\( f(z) = w \\). Let's set \\( z = x + f(y) \\). Then from the original equation, \\( f(z) = \\frac{y}{x y + 1} \\). So given \\( w = \\frac{y}{x y + 1} \\), we can solve for \\( x \\) in terms of \\( y \\):\n\n\\( w (x y + 1) = y \\).\n\n\\( x y w + w = y \\).\n\n\\( x y w = y - w \\).\n\n\\( x = \\frac{y - w}{y w} \\).\n\nBut since \\( x \\neq 0 \\), \\( \\frac{y - w}{y w} \\neq 0 \\implies y \\neq w \\).\n\nTherefore, for any \\( w \\neq y \\), we can choose \\( y \\) such that \\( x \\) is defined. Wait, but we need to choose \\( y \\) such that \\( x = \\frac{y - w}{y w} \\neq 0 \\) and \\( y \\) can be selected accordingly. For example, fix \\( y \\) such that \\( y \\neq w \\), then \\( x \\) is given, and thus \\( z = x + f(y) \\). Therefore, every \\( w \\in \\mathbb{R}^* \\) can be achieved by choosing appropriate \\( y \\) and \\( x \\). Therefore, \\( f \\) is surjective.\n\nSince \\( f \\) is both injective and surjective, it's bijective. Therefore, it has an inverse function. Let's denote \\( f^{-1} \\) as the inverse function of \\( f \\). Then, since \\( f \\) is bijective, for every \\( w \\in \\mathbb{R}^* \\), there's a unique \\( z \\in \\mathbb{R}^* \\) such that \\( f(z) = w \\), which means \\( z = f^{-1}(w) \\).\n\nLet me use this in the original equation. Let's set \\( w = x + f(y) \\), so \\( f(w) = \\frac{y}{x y + 1} \\). Since \\( w = x + f(y) \\), we can write \\( x = w - f(y) \\). Substitute into the equation:\n\n\\( f(w) = \\frac{y}{(w - f(y)) y + 1} = \\frac{y}{w y - f(y) y + 1} \\).\n\nBut \\( f(w) \\) is expressed in terms of \\( y \\). Let me solve for \\( y \\). Let's rearrange the equation:\n\n\\( f(w) (w y - y f(y) + 1) = y \\).\n\nExpand:\n\n\\( f(w) w y - f(w) y f(y) + f(w) = y \\).\n\nBring all terms to one side:\n\n\\( f(w) w y - f(w) y f(y) + f(w) - y = 0 \\).\n\nFactor out \\( y \\):\n\n\\( y [f(w) w - f(w) f(y) - 1] + f(w) = 0 \\).\n\nThis equation must hold for some \\( y \\), given \\( w \\). But since \\( f \\) is surjective, for any \\( w \\), there exists a \\( y \\) such that \\( f(y) = something \\). Hmm, this is getting complicated. Maybe using the inverse function would help.\n\nSince \\( f \\) is invertible, let's set \\( y = f^{-1}(v) \\). Then, substituting into the original equation:\n\n\\( f(x + v) = \\frac{f^{-1}(v)}{x f^{-1}(v) + 1} \\).\n\nBut this might not directly help. Alternatively, let's try to find an expression for \\( f^{-1} \\).\n\nSince \\( f \\) is bijective, there exists \\( f^{-1} \\). Let me take the original equation and apply \\( f^{-1} \\) to both sides:\n\n\\( x + f(y) = f^{-1}\\left( \\frac{y}{x y + 1} \\right ) \\).\n\nLet me rearrange this equation:\n\n\\( f^{-1}\\left( \\frac{y}{x y + 1} \\right ) = x + f(y) \\).\n\nNow, let me set \\( u = \\frac{y}{x y + 1} \\). Then, \\( f^{-1}(u) = x + f(y) \\).\n\nBut \\( u = \\frac{y}{x y + 1} \\). Let's solve for \\( x \\):\n\nMultiply both sides by \\( x y + 1 \\):\n\n\\( u x y + u = y \\).\n\nRearrange:\n\n\\( u x y = y - u \\).\n\nThus,\n\n\\( x = \\frac{y - u}{u y} \\).\n\nThen, substituting into \\( f^{-1}(u) \\):\n\n\\( f^{-1}(u) = \\frac{y - u}{u y} + f(y) \\).\n\nSimplify:\n\n\\( f^{-1}(u) = \\frac{y - u + u y f(y)}{u y} \\).\n\nBut this must hold for some \\( y \\) (since \\( u \\) is in the image of \\( f \\), and \\( y \\) can be chosen accordingly). This seems too vague. Let me consider specific values. For example, take \\( u = f(z) \\). Then, \\( f^{-1}(f(z)) = z \\). So substituting \\( u = f(z) \\):\n\n\\( z = \\frac{y - f(z)}{f(z) y} + f(y) \\).\n\nMultiply both sides by \\( f(z) y \\):\n\n\\( z f(z) y = y - f(z) + f(z) y f(y) \\).\n\nBring all terms to left:\n\n\\( z f(z) y - y - f(z) y f(y) + f(z) = 0 \\).\n\nFactor \\( y \\):\n\n\\( y [ z f(z) - 1 - f(z) f(y) ] + f(z) = 0 \\).\n\nThis equation must hold for some \\( y \\), given \\( z \\). Not sure if this helps. Let's try plugging \\( f(y) = 1/y \\) into this equation to see if it holds. Let \\( f(z) = 1/z \\), then:\n\nLeft-hand side: \\( z \\cdot \\frac{1}{z} \\cdot y - y - \\frac{1}{z} \\cdot y \\cdot \\frac{1}{y} + \\frac{1}{z} \\).\n\nSimplify term by term:\n\nFirst term: \\( z*(1/z)*y = y \\).\n\nSecond term: -y.\n\nThird term: - (1/z)*y*(1/y) = -1/z.\n\nFourth term: +1/z.\n\nSo total:\n\ny - y - 1/z + 1/z = 0.\n\nWhich works. So this checks out.\n\nBut since the only solution for \\( h(z) = 1 \\) led us to \\( f(z) = 1/z \\), and we verified that this works, and other approaches either lead back to this solution or confirm its validity, it's likely that \\( f(z) = 1/z \\) is the unique solution.\n\nTherefore, after thorough analysis, I conclude that the only function satisfying the equation is \\( f(y) = \\frac{1}{y} \\).\n</think>To find all functions \\( f: \\mathbb{R}^* \\to \\mathbb{R}^* \\) satisfying the functional equation\n\\[ f(x + f(y)) = \\frac{y}{xy + 1}, \\]\nwe will proceed step-by-step.\n\n1. **Injectivity of \\( f \\):**\n   Suppose \\( f(a) = f(b) \\). Then for any \\( x \\),\n   \\[\n   f(x + f(a)) = f(x + f(b)) \\implies \\frac{a}{xa + 1} = \\frac{b}{xb + 1}.\n   \\]\n   Cross-multiplying gives\n   \\[\n   a(xb + 1) = b(xa + 1) \\implies axb + a = bxa + b \\implies a = b.\n   \\]\n   Therefore, \\( f \\) is injective.\n\n2. **Finding \\( f \\):**\n   Let's substitute \\( x = \\frac{1 - f(y)}{y} \\) (assuming \\( y \\neq 0 \\) and \\( \\frac{1 - f(y)}{y} \\neq 0 \\)):\n   \\[\n   f\\left( \\frac{1 - f(y)}{y} + f(y) \\right) = \\frac{y}{\\left( \\frac{1 - f(y)}{y} \\right) y + 1} = \\frac{y}{1 - f(y) + 1} = \\frac{y}{2 - f(y) y}.\n   \\]\n   Since \\( \\frac{1 - f(y)}{y} + f(y) = \\frac{1 - f(y) + y f(y)}{y} = \\frac{1 + y f(y) - f(y)}{y} = \\frac{1 + (y - 1)f(y)}{y} \\), we have\n   \\[\n   f\\left( \\frac{1 + (y - 1)f(y)}{y} \\right) = \\frac{y}{2 - f(y) y}.\n   \\]\n   For simplicity, let's try \\( f(y) = \\frac{1}{y} \\):\n   \\[\n   f(x + f(y)) = f\\left( x + \\frac{1}{y} \\right) = \\frac{1}{x + \\frac{1}{y}} = \\frac{y}{xy + 1}.\n   \\]\n   This matches the right-hand side of the original equation. Therefore, \\( f(y) = \\frac{1}{y} \\) is a solution.\n\n3. **Uniqueness of the solution:**\n   To show that \\( f(y) = \\frac{1}{y} \\) is the only solution, assume there is another function \\( g \\) satisfying the same equation. Since \\( f \\) is bijective (injective and surjective), for any \\( w \\in \\mathbb{R}^* \\), there exists a unique \\( z \\in \\mathbb{R}^* \\) such that \\( f(z) = w \\). Substituting \\( y = f^{-1}(v) \\) into the equation, we get\n   \\[\n   f(x + v) = \\frac{f^{-1}(v)}{x f^{-1}(v) + 1}.\n   \\]\n   If we assume \\( f(y) = \\frac{1}{y} \\), then \\( f^{-1}(v) = \\frac{1}{v} \\), and the equation becomes\n   \\[\n   f(x + v) = \\frac{\\frac{1}{v}}{x \\frac{1}{v} + 1} = \\frac{1}{x + v},\n   \\]\n   which is consistent with \\( f(y) = \\frac{1}{y} \\). Therefore, \\( f(y) = \\frac{1}{y} \\) is the unique solution.\n\nThus, the only function \\( f: \\mathbb{R}^* \\to \\mathbb{R}^* \\) that satisfies the given functional equation is\n\\[\n\\boxed{f(x) = \\frac{1}{x}}.\n\\]"}