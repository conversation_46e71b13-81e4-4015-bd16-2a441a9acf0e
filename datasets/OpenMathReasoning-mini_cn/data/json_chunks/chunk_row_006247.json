{"row_number": 6247, "expected_answer": "\\(\\frac{\\pi}{4}\\)", "problem": "Evaluate the limit \n\n\\[ \\lim_{n\\rightarrow \\infty}\\frac{1}{n^2} \\sum_{k=1}^{n} \\sqrt{n^2-k^2} \\]", "generated_solution": "<think>\nOkay, so I need to evaluate the limit as n approaches infinity of (1/n²) times the sum from k=1 to n of the square root of (n² - k²). Hmm, let me think about how to approach this. \n\nFirst, when I see a sum like this divided by n², maybe I can relate it to a Riemann sum. Riemann sums are used to approximate integrals, right? So maybe if I can express the sum in a form that looks like a Riemann sum, I can convert it into an integral and evaluate that.\n\nLet's write out the sum S = Σ√(n² - k²) from k=1 to n. Then the limit is lim_{n→∞} S / n². \n\nI want to see if I can factor out n from inside the square root. Let's try that. √(n² - k²) = √[n²(1 - (k/n)²)] = n√(1 - (k/n)²). \n\nSo substituting back into the sum, S becomes Σ n√(1 - (k/n)²) from k=1 to n. Then S/n² would be (1/n²) * Σ n√(1 - (k/n)²) = (1/n) * Σ √(1 - (k/n)²) from k=1 to n. \n\nAh! So now the expression is (1/n) times the sum from k=1 to n of √(1 - (k/n)²). That looks like a Riemann sum for the integral of √(1 - x²) from x=0 to x=1, because in a Riemann sum, Δx is 1/n, and the points x_k are k/n. So the sum (1/n) Σ f(k/n) approximates the integral from 0 to 1 of f(x) dx. \n\nWait, but here the sum starts at k=1, so x starts at 1/n instead of 0. But as n approaches infinity, the difference between starting at k=1 and k=0 becomes negligible, right? Because the term when k=0 would be √(1 - 0) = 1, multiplied by 1/n, which would go to zero as n→∞. So maybe we can extend the sum to start at k=0, which would make it a Riemann sum from x=0 to x=1 with x_k = k/n. \n\nTherefore, the limit should be equal to the integral of √(1 - x²) dx from 0 to 1. \n\nBut wait, the integral of √(1 - x²) from 0 to 1 is the area of a quarter of a circle with radius 1, right? Because the equation x² + y² =1 is a circle, and integrating y=√(1 - x²) from 0 to 1 gives the area of the first quadrant part. The area of a full circle is πr², so a quarter circle would be (π(1)²)/4 = π/4. \n\nTherefore, the integral from 0 to 1 of √(1 - x²) dx is π/4. So the limit should be π/4. \n\nWait, but let me double-check. Let's confirm the Riemann sum part. The sum from k=1 to n of √(1 - (k/n)^2) * (1/n) is a right Riemann sum for the integral from 0 to 1 of √(1 - x²) dx. However, Riemann sums usually approximate the integral as n approaches infinity, so as n becomes very large, the sum should approach the integral. Therefore, the limit is indeed the integral of √(1 - x²) from 0 to 1, which is π/4. \n\nBut wait, actually, the integral of √(1 - x²) from 0 to 1 is π/4. So the answer should be π/4. \n\nAlternatively, maybe there's another way to think about this problem. Let me see if there's another approach. \n\nAnother thought: Maybe using the substitution k = n sinθ? Let's see. If we set k = n sinθ, then when k=1, sinθ = 1/n, which approaches 0 as n approaches infinity, and when k=n, sinθ =1, so θ=π/2. Then the term √(n² -k²) becomes √(n² - n² sin²θ) = n√(1 - sin²θ) = n cosθ. \n\nBut the sum is over k from 1 to n, so θ would go from arcsin(1/n) to π/2. However, converting the sum into an integral might be tricky here. But perhaps we can approximate the sum as an integral. If we let θ = arcsin(k/n), then dθ would be related to dk. But maybe this approach is more complicated than necessary. Since the Riemann sum idea seems straightforward, maybe that's the right way. \n\nWait, let's also check if the original sum includes k from 1 to n. So when converting to the integral, the Riemann sum with right endpoints would be from x=1/n to x=1, but as n approaches infinity, the lower limit approaches 0. So the integral from 0 to 1 of √(1 -x²)dx is indeed the limit. \n\nTherefore, the answer should be π/4. \n\nLet me verify with a concrete example. Suppose n is large, say n=1000. Then the sum would be approximately the integral from 0 to1 times n, but wait no, the sum is (1/n) * sum f(k/n), which approximates the integral. So as n increases, it should get closer to π/4. \n\nAlternatively, maybe integrate from 0 to1. Let me compute the integral of sqrt(1 -x²) from 0 to1. Let x = sinθ, dx = cosθ dθ. Then the integral becomes ∫sqrt(1 - sin²θ) cosθ dθ from θ=0 to θ=π/2. That simplifies to ∫cosθ * cosθ dθ = ∫cos²θ dθ from 0 to π/2. \n\nThe integral of cos²θ is (θ/2 + (sin2θ)/4) evaluated from 0 to π/2. At π/2: (π/4 + (sinπ)/4) = π/4. At 0: (0 + 0) =0. So the integral is π/4. Yep, that's correct. \n\nSo the limit is π/4.\n</think>To evaluate the limit\n\n\\[\n\\lim_{n \\rightarrow \\infty} \\frac{1}{n^2} \\sum_{k=1}^{n} \\sqrt{n^2 - k^2},\n\\]\n\nwe start by rewriting the summand in a form that suggests a Riemann sum. Notice that\n\n\\[\n\\sqrt{n^2 - k^2} = \\sqrt{n^2 \\left(1 - \\left(\\frac{k}{n}\\right)^2\\right)} = n \\sqrt{1 - \\left(\\frac{k}{n}\\right)^2}.\n\\]\n\nThus, the given sum can be rewritten as\n\n\\[\n\\sum_{k=1}^{n} \\sqrt{n^2 - k^2} = \\sum_{k=1}^{n} n \\sqrt{1 - \\left(\\frac{k}{n}\\right)^2}.\n\\]\n\nDividing by \\( n^2 \\), we get\n\n\\[\n\\frac{1}{n^2} \\sum_{k=1}^{n} n \\sqrt{1 - \\left(\\frac{k}{n}\\right)^2} = \\frac{1}{n} \\sum_{k=1}^{n} \\sqrt{1 - \\left(\\frac{k}{n}\\right)^2}.\n\\]\n\nThe expression \\(\\frac{1}{n} \\sum_{k=1}^{n} \\sqrt{1 - \\left(\\frac{k}{n}\\right)^2}\\) is a Riemann sum for the function \\( f(x) = \\sqrt{1 - x^2} \\) over the interval \\([0, 1]\\). As \\( n \\to \\infty \\), this Riemann sum converges to the definite integral of \\( f(x) \\) from 0 to 1:\n\n\\[\n\\lim_{n \\to \\infty} \\frac{1}{n} \\sum_{k=1}^{n} \\sqrt{1 - \\left(\\frac{k}{n}\\right)^2} = \\int_{0}^{1} \\sqrt{1 - x^2} \\, dx.\n\\]\n\nTo evaluate this integral, we use the substitution \\( x = \\sin \\theta \\). Then \\( dx = \\cos \\theta \\, d\\theta \\), and the limits of integration change from \\( x = 0 \\) to \\( x = 1 \\) to \\( \\theta = 0 \\) to \\( \\theta = \\frac{\\pi}{2} \\). The integral becomes\n\n\\[\n\\int_{0}^{1} \\sqrt{1 - x^2} \\, dx = \\int_{0}^{\\frac{\\pi}{2}} \\sqrt{1 - \\sin^2 \\theta} \\cos \\theta \\, d\\theta = \\int_{0}^{\\frac{\\pi}{2}} \\cos^2 \\theta \\, d\\theta.\n\\]\n\nUsing the identity \\( \\cos^2 \\theta = \\frac{1 + \\cos 2\\theta}{2} \\), we have\n\n\\[\n\\int_{0}^{\\frac{\\pi}{2}} \\cos^2 \\theta \\, d\\theta = \\int_{0}^{\\frac{\\pi}{2}} \\frac{1 + \\cos 2\\theta}{2} \\, d\\theta = \\frac{1}{2} \\int_{0}^{\\frac{\\pi}{2}} (1 + \\cos 2\\theta) \\, d\\theta.\n\\]\n\nThis integral can be split into two simpler integrals:\n\n\\[\n\\frac{1}{2} \\int_{0}^{\\frac{\\pi}{2}} 1 \\, d\\theta + \\frac{1}{2} \\int_{0}^{\\frac{\\pi}{2}} \\cos 2\\theta \\, d\\theta.\n\\]\n\nThe first integral evaluates to\n\n\\[\n\\frac{1}{2} \\left[ \\theta \\right]_{0}^{\\frac{\\pi}{2}} = \\frac{1}{2} \\left( \\frac{\\pi}{2} - 0 \\right) = \\frac{\\pi}{4}.\n\\]\n\nThe second integral evaluates to\n\n\\[\n\\frac{1}{2} \\left[ \\frac{\\sin 2\\theta}{2} \\right]_{0}^{\\frac{\\pi}{2}} = \\frac{1}{4} \\left( \\sin \\pi - \\sin 0 \\right) = \\frac{1}{4} (0 - 0) = 0.\n\\]\n\nThus, the integral evaluates to\n\n\\[\n\\frac{\\pi}{4}.\n\\]\n\nTherefore, the limit is\n\n\\[\n\\boxed{\\frac{\\pi}{4}}.\n\\]"}