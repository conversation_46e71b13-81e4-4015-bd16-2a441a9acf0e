{"row_number": 6307, "expected_answer": "\\(100^\\circ\\)", "problem": "In triangle \\(ABC\\) with \\(AB = BC\\) and \\(\\angle B = 20^\\circ\\), point \\(M\\) on \\(AC\\) is such that \\(AM : MC = 1 : 2\\). Point \\(H\\) is the projection of \\(C\\) onto \\(BM\\). Find the measure of angle \\(AHB\\).", "generated_solution": "<think>\nOkay, so I have this geometry problem here. Let me try to parse it step by step. It's about triangle ABC where AB equals BC, and angle B is 20 degrees. Then there's a point M on AC such that the ratio of AM to MC is 1:2. Point H is the projection of C onto BM. We need to find the measure of angle AHB.\n\nFirst, let me visualize the triangle. Since AB equals BC, triangle ABC is isosceles with AB = BC. Wait, hold on, if AB = BC, then the triangle is isosceles with base AC and vertex at B. But angle B is given as 20 degrees. So in an isosceles triangle, the angles at the base are equal. Let me confirm: in triangle ABC, AB = BC, so the base is AC. Wait, actually, if AB = BC, then the two equal sides are AB and BC, so the base would be AC. Then the base angles at A and C would be equal. But angle B is 20 degrees. Since the sum of angles in a triangle is 180 degrees, each base angle would be (180 - 20)/2 = 80 degrees. So angle A and angle C are each 80 degrees.\n\nWait, but then if AB = BC, the sides opposite angles A and C. Wait, hold on, maybe I confused the sides. Wait, side opposite angle A is BC, and side opposite angle C is AB. Wait, in triangle ABC, side AB is opposite angle C, and side BC is opposite angle A. Since AB = BC, that would mean angles at C and A are equal. Which they are, since we found each is 80 degrees. So that's correct.\n\nSo, triangle ABC with AB = BC, angle B = 20°, angles at A and C are 80° each. Then, point M is on AC such that AM:MC = 1:2. So, AC is divided by M into segments AM and MC with AM being one part and MC being two parts. So, if AC is split into three equal parts, AM is one part and MC is two parts. So M is closer to A than to C.\n\nThen H is the projection of C onto BM. That means H is the foot of the perpendicular from C to BM. So, we need to find angle AHB.\n\nAlright. Let me think. Maybe I can use coordinate geometry here. Assign coordinates to the points and calculate the required angle. Alternatively, maybe there's a way to use triangle properties, similar triangles, or trigonometric identities. Hmm.\n\nLet me try coordinate geometry. Let's place point B at the origin (0, 0) for simplicity. Since angle B is 20°, and AB = BC, we need to place points A and C such that BA = BC. Let's choose coordinates so that BA and BC are of equal length. Let's set BA = BC = 1 unit for simplicity.\n\nSince angle at B is 20°, the coordinates of points A and C can be determined using polar coordinates. Let me set point B at (0, 0). Let's place point A at (1, 0). Then, since angle B is 20°, point C would be at an angle of 20° from the x-axis. Wait, but in which direction? Wait, since AB = BC = 1, and angle at B is 20°, triangle ABC has BA = BC = 1, angle between BA and BC is 20°, so points A and C are each 1 unit from B, separated by 20°. Therefore, if point A is at (1, 0), then point C would be at (cos 20°, sin 20°). Hmm, let me confirm.\n\nWait, actually, if we have BA and BC each of length 1, and the angle at B is 20°, then yes, points A and C are each 1 unit from B, and the angle between BA and BC is 20°. So, if point A is at (1, 0), point C would be at (cos 20°, sin 20°). Then, AC is the line connecting (1, 0) and (cos 20°, sin 20°). Then point M divides AC in the ratio 1:2, so AM:MC = 1:2, so M is closer to A.\n\nTo find coordinates of M, since it's dividing AC in ratio 1:2, we can use the section formula. The coordinates of M would be [(2*1 + 1*cos20°)/ (1 + 2), (2*0 + 1*sin20°)/ (1 + 2)]. So, M is [(2 + cos20°)/3, (sin20°)/3].\n\nThen, BM is the line from B (0,0) to M [(2 + cos20°)/3, (sin20°)/3]. The equation of line BM can be found. Since it passes through (0,0) and ((2 + cos20°)/3, (sin20°)/3), the slope would be [ (sin20°)/3 - 0 ] / [ (2 + cos20°)/3 - 0 ] = (sin20°)/(2 + cos20°). Therefore, the equation of BM is y = [sin20°/(2 + cos20°)] x.\n\nNow, point H is the projection of C onto BM. So, to find the projection of point C onto line BM. Let me recall the formula for projecting a point onto a line. The formula for the projection of a point (x0, y0) onto the line ax + by + c = 0 is:\n\nH = ( (b(bx0 - ay0) - ac ) / (a² + b²), (a(-bx0 + ay0) - bc ) / (a² + b²) )\n\nBut in our case, line BM passes through the origin, and its equation is y = [sin20°/(2 + cos20°)] x. Let's write this in standard form. Let me denote m = sin20°/(2 + cos20°). Then, the equation is y = m x, which can be written as m x - y = 0. So, a = m, b = -1, c = 0.\n\nSo, using the projection formula for point C (cos20°, sin20°), the projection H would be:\n\nH = ( ( (-1)(m*cos20° - (-1)*sin20° ) - m*0 ) / (m² + (-1)^2 ), ( m*(-(-1)*cos20° + m*sin20° ) - (-1)*0 ) / (m² + 1 ) )\n\nBut since c = 0, this simplifies. Let me compute the coordinates step by step.\n\nAlternatively, since line BM is y = m x, the direction vector of BM is (1, m). The vector from B to M is ((2 + cos20°)/3, (sin20°)/3). But maybe another approach is better.\n\nThe projection of point C onto line BM can be calculated using the formula for projection. Let me recall that if we have a line parametrized as t*d + p0, then the projection of a point q onto the line is given by t = ( (q - p0) · d ) / ||d||², and the projection point is p0 + t*d.\n\nSince BM is through the origin (0,0), p0 is (0,0), and direction vector d is ( (2 + cos20°)/3, (sin20°)/3 ). So direction vector d is M's coordinates. Then, the projection of point C (cos20°, sin20°) onto BM would be t*d, where t = (C · d) / ||d||².\n\nCompute t:\n\nC · d = cos20°*(2 + cos20°)/3 + sin20°*(sin20°)/3\n\n= [ (2 + cos20°)cos20° + sin²20° ] / 3\n\n= [ 2 cos20° + cos²20° + sin²20° ] / 3\n\nSince cos²θ + sin²θ = 1, this becomes [2 cos20° + 1]/3\n\n||d||² = [ ( (2 + cos20°)/3 )² + ( (sin20°)/3 )² ] = [ (4 + 4 cos20° + cos²20°) + sin²20° ] / 9\n\nAgain, cos²θ + sin²θ = 1, so this becomes [4 + 4 cos20° + 1 ] / 9 = (5 + 4 cos20°)/9\n\nTherefore, t = [2 cos20° + 1]/3 divided by (5 + 4 cos20°)/9 ) = [ (2 cos20° + 1)/3 ] * [ 9/(5 + 4 cos20°) ] = 3(2 cos20° + 1)/(5 + 4 cos20° )\n\nTherefore, the projection H is t*d = [ 3(2 cos20° + 1)/(5 + 4 cos20° ) ] * [ (2 + cos20°)/3, (sin20°)/3 ]\n\nSimplifying, the 3 in numerator and denominator cancels out:\n\nH = [ (2 cos20° + 1)(2 + cos20°)/(5 + 4 cos20° ), (2 cos20° + 1)(sin20°)/(5 + 4 cos20° ) ]\n\nSo, H has coordinates:\n\nx_H = (2 cos20° + 1)(2 + cos20°)/(5 + 4 cos20° )\n\ny_H = (2 cos20° + 1)(sin20°)/(5 + 4 cos20° )\n\nThis seems complicated. Maybe there's a better way to approach this. Alternatively, maybe using trigonometric identities or geometric properties could simplify things. Let me check if angle AHB is a standard angle. The problem seems to ask for a measure in degrees, likely an integer, so maybe 30°, 40°, 50°, etc. Let me see.\n\nAlternatively, maybe using the Law of Sines or Law of Cosines in triangle AHB once we have coordinates.\n\nBut first, let me compute coordinates of points A, B, M, H.\n\nPoints:\n\n- B: (0, 0)\n\n- A: (1, 0)\n\n- C: (cos20°, sin20°)\n\n- M: [(2 + cos20°)/3, (sin20°)/3]\n\n- H: as calculated above.\n\nOnce we have coordinates for A, H, and B, we can compute the angle at H, which is angle AHB. Wait, angle AHB is the angle at H between points A, H, B. Wait, no. Wait, angle AHB is the angle at H between points A, H, B. Wait, no, actually, in angle notation, angle AHB is the angle at H between points A, H, B. So vertex at H, with segments HA and HB.\n\nBut to compute angle AHB, we need the coordinates of A, H, and B. Then, we can compute vectors HA and HB and find the angle between them.\n\nAlternatively, compute the coordinates of H, then use the Law of Cosines in triangle AHB.\n\nAlternatively, compute the coordinates of H, compute vectors HA and HB, then use the dot product formula.\n\nLet me try this. So, points:\n\nA: (1, 0)\n\nB: (0, 0)\n\nH: (x_H, y_H) as above.\n\nThen vector HA is A - H = (1 - x_H, 0 - y_H)\n\nVector HB is B - H = (0 - x_H, 0 - y_H) = (-x_H, -y_H)\n\nThe angle between HA and HB is angle AHB. The formula for the angle between two vectors u and v is:\n\ncosθ = (u · v)/( ||u|| ||v|| )\n\nCompute u · v = (1 - x_H)(-x_H) + ( - y_H)(-y_H ) = -x_H + x_H² + y_H²\n\n||u|| = sqrt( (1 - x_H)^2 + y_H² )\n\n||v|| = sqrt( x_H² + y_H² )\n\nTherefore, cosθ = ( -x_H + x_H² + y_H² ) / ( sqrt( (1 - x_H)^2 + y_H² ) * sqrt( x_H² + y_H² ) )\n\nThis looks complicated. Maybe there's a better approach. Let me check if this angle is a known angle given the construction.\n\nAlternatively, perhaps there's a way to use the fact that triangle ABC is isosceles and point M divides AC in ratio 1:2, then by some geometric properties, maybe triangle AHB has some special properties.\n\nAlternatively, maybe using the Law of Sines in triangle AHB. However, without knowing the lengths, this might not be straightforward.\n\nAlternatively, perhaps considering reflecting points or constructing auxiliary lines.\n\nAlternatively, maybe using trigonometric identities to compute cosθ.\n\nAlternatively, let me compute numerical values. Since the angle is 20°, maybe converting everything into decimal values could help. Let me approximate cos20° and sin20°.\n\ncos20° ≈ 0.9396926\n\nsin20° ≈ 0.3420201\n\nSo, let's compute coordinates step by step.\n\nFirst, compute M:\n\nx_M = (2 + cos20°)/3 ≈ (2 + 0.9396926)/3 ≈ 2.9396926/3 ≈ 0.9798975\n\ny_M = sin20°/3 ≈ 0.3420201/3 ≈ 0.1140067\n\nSo, M ≈ (0.9799, 0.1140)\n\nThen, compute the direction vector of BM: from B(0,0) to M(0.9799, 0.1140). The slope is 0.1140 / 0.9799 ≈ 0.1163. So equation of BM is y ≈ 0.1163 x.\n\nNow, projection of C onto BM. Let's compute coordinates of C: (cos20°, sin20°) ≈ (0.9397, 0.3420)\n\nTo find the projection H of C onto BM, let's use the projection formula. Let's denote line BM as y = m x, where m ≈ 0.1163.\n\nThe formula for the projection H of point C (x0, y0) onto line y = m x is:\n\nH = ( (m(y0) + x0) / (m² + 1) , m*(m y0 + x0) / (m² + 1) )\n\nWait, is this correct? Wait, another formula: the projection of point (x0, y0) onto line y = m x is given by:\n\nThe formula can be derived by minimizing the distance. Let me recall that for a line y = m x, the projection of (x0, y0) is:\n\nx = (x0 + m y0)/(1 + m²)\n\ny = m(x0 + m y0)/(1 + m²)\n\nWait, actually, no. Let me check.\n\nGiven a line y = m x, the projection of point (x0, y0) can be found by solving for t where the point (t, m t) is the closest to (x0, y0). The squared distance is (t - x0)^2 + (m t - y0)^2. Taking derivative with respect to t and setting to zero:\n\n2(t - x0) + 2m(m t - y0) = 0\n\nSolving for t:\n\nt - x0 + m² t - m y0 = 0\n\nt(1 + m²) = x0 + m y0\n\nt = (x0 + m y0)/(1 + m²)\n\nTherefore, the projection point H is ( t, m t ) = ( (x0 + m y0)/(1 + m²), m(x0 + m y0)/(1 + m²) )\n\nSo, for point C (0.9397, 0.3420) and line BM with m ≈ 0.1163,\n\nCompute t:\n\nt = (0.9397 + 0.1163 * 0.3420)/(1 + (0.1163)^2 )\n\nFirst compute numerator:\n\n0.1163 * 0.3420 ≈ 0.0398\n\nSo, numerator ≈ 0.9397 + 0.0398 ≈ 0.9795\n\nDenominator: 1 + (0.1163)^2 ≈ 1 + 0.0135 ≈ 1.0135\n\nTherefore, t ≈ 0.9795 / 1.0135 ≈ 0.9667\n\nThen, y-coordinate of H is m * t ≈ 0.1163 * 0.9667 ≈ 0.1125\n\nTherefore, coordinates of H ≈ (0.9667, 0.1125)\n\nWait, but M is at (0.9799, 0.1140), so H is slightly before M along BM. That makes sense since H is the projection of C onto BM, which is a point on BM closest to C.\n\nNow, let's check the coordinates. If H is on BM, then its coordinates should satisfy y ≈ 0.1163 x. Let's check y ≈ 0.1125 vs 0.1163 * 0.9667 ≈ 0.1125, which matches. So that's correct.\n\nNow, with H at (0.9667, 0.1125), we can compute vectors HA and HB.\n\nPoint A is at (1, 0), H is at (0.9667, 0.1125), B is at (0,0).\n\nVector HA: A - H = (1 - 0.9667, 0 - 0.1125) ≈ (0.0333, -0.1125)\n\nVector HB: B - H = (0 - 0.9667, 0 - 0.1125) ≈ (-0.9667, -0.1125)\n\nNow, compute the angle between HA and HB.\n\nFirst, compute the dot product:\n\nHA · HB ≈ (0.0333)(-0.9667) + (-0.1125)(-0.1125) ≈ -0.0322 + 0.01266 ≈ -0.0195\n\nCompute ||HA||: sqrt(0.0333² + (-0.1125)^2 ) ≈ sqrt(0.00111 + 0.01266 ) ≈ sqrt(0.01377 ) ≈ 0.1174\n\nCompute ||HB||: sqrt( (-0.9667)^2 + (-0.1125)^2 ) ≈ sqrt(0.9346 + 0.01266 ) ≈ sqrt(0.9473 ) ≈ 0.9733\n\nThen, cosθ ≈ (-0.0195)/(0.1174 * 0.9733 ) ≈ (-0.0195)/(0.1143 ) ≈ -0.1705\n\nSo, θ ≈ arccos(-0.1705 ) ≈ 100 degrees. Wait, arccos(-0.1705) is 180° - arccos(0.1705). arccos(0.1705) ≈ 80°, so θ ≈ 100°.\n\nBut the problem is asking for angle AHB. Wait, but if the angle is 100°, that's a possible answer, but let me check if my calculation is correct.\n\nWait, but the answer might be 30°, 45°, 60°, or some other common angle. 100° seems high, but maybe that's correct. Wait, let me verify my calculations.\n\nWait, the approximate calculation gives angle AHB ≈ 100°, but perhaps due to the approximated values, maybe the exact value is 90°, but here it's 100°, which is more than 90°. Wait, but H is the foot of the perpendicular from C to BM. Let's check the position of H. Since BM is a line from B to M on AC. So, H is somewhere on BM, closer to B or M?\n\nGiven that the projection of C onto BM is H, which is the closest point on BM to C. So, depending on the position of BM, H could be inside or outside the triangle. Wait, in our coordinate system, point C is at (0.9397, 0.3420), and BM is from (0,0) to (0.9799, 0.1140). The projection of C onto BM is H at (0.9667, 0.1125), which is very close to M. Since M is at (0.9799, 0.1140), H is slightly before M. So, H is between B and M, but closer to M.\n\nTherefore, angle AHB is formed by points A(1,0), H(0.9667,0.1125), B(0,0). So, plotting these points, A is to the right, H is near M, which is near the midpoint of AC. So, the angle at H between A, H, B. Hmm, but the calculated angle was approximately 100°, which seems correct? But let's check.\n\nWait, maybe my calculation was a bit off due to approximations. Let me compute with more precise values.\n\nAlternatively, let's try to compute symbolically.\n\nRecall that in the coordinate system:\n\n- A = (1, 0)\n\n- B = (0, 0)\n\n- C = (cos20°, sin20°)\n\n- M = ((2 + cos20°)/3, sin20°/3 )\n\nThen, BM has direction vector ((2 + cos20°)/3, sin20°/3 )\n\nThe parametric equation of BM is t*( (2 + cos20°)/3, sin20°/3 ), t ∈ [0, 1]\n\nProjection of C onto BM: find t such that the vector from C to H is perpendicular to BM.\n\nSo, vector CH is perpendicular to BM's direction vector.\n\nTherefore, ( H - C ) · direction_vector = 0\n\nSince H is on BM, H = t*( (2 + cos20°)/3, sin20°/3 )\n\nThus, ( t*(2 + cos20°)/3 - cos20°, t*sin20°/3 - sin20° ) · ( (2 + cos20°)/3, sin20°/3 ) = 0\n\nCompute the dot product:\n\n[ t*(2 + cos20°)/3 - cos20° ] * (2 + cos20°)/3 + [ t*sin20°/3 - sin20° ] * sin20°/3 = 0\n\nLet's expand this:\n\nt*(2 + cos20°)^2 / 9 - cos20°*(2 + cos20°)/3 + t*sin²20° / 9 - sin²20° / 3 = 0\n\nFactor out t:\n\nt [ (2 + cos20°)^2 + sin²20° ] / 9 - [ cos20°(2 + cos20°) + sin²20° ] / 3 = 0\n\nMultiply both sides by 9:\n\nt [ (2 + cos20°)^2 + sin²20° ] - 3 [ cos20°(2 + cos20°) + sin²20° ] = 0\n\nSolve for t:\n\nt = [ 3( cos20°(2 + cos20°) + sin²20° ) ] / [ (2 + cos20°)^2 + sin²20° ]\n\nLet's compute numerator and denominator.\n\nFirst, expand numerator:\n\n3 [ 2 cos20° + cos²20° + sin²20° ]\n\n= 3 [ 2 cos20° + (cos²20° + sin²20° ) ]\n\n= 3 [ 2 cos20° + 1 ]\n\nDenominator:\n\n(2 + cos20°)^2 + sin²20°\n\nExpand (2 + cos20°)^2 = 4 + 4 cos20° + cos²20°\n\nSo denominator = 4 + 4 cos20° + cos²20° + sin²20° = 4 + 4 cos20° + 1 = 5 + 4 cos20°\n\nThus, t = 3(2 cos20° + 1 ) / (5 + 4 cos20° )\n\nSo, coordinates of H are:\n\nH_x = t*(2 + cos20°)/3 = [ 3(2 cos20° + 1 ) / (5 + 4 cos20° ) ]*(2 + cos20°)/3 = (2 cos20° + 1)(2 + cos20° ) / (5 + 4 cos20° )\n\nSimilarly, H_y = t*(sin20°)/3 = [ 3(2 cos20° + 1 ) / (5 + 4 cos20° ) ]*(sin20°)/3 = (2 cos20° + 1 ) sin20° / (5 + 4 cos20° )\n\nTherefore, coordinates of H:\n\nH = [ (2 cos20° + 1)(2 + cos20° ) / (5 + 4 cos20° ), (2 cos20° + 1 ) sin20° / (5 + 4 cos20° ) ]\n\nNow, let's compute vectors HA and HB.\n\nVector HA = A - H = (1, 0) - H = (1 - H_x, -H_y )\n\nVector HB = B - H = (0, 0) - H = (-H_x, -H_y )\n\nNow, the angle between HA and HB is angle AHB. Let's compute the cosine of this angle using the dot product formula:\n\ncosθ = (HA · HB) / ( |HA| |HB| )\n\nCompute HA · HB = (1 - H_x)(-H_x) + (-H_y)(-H_y ) = -H_x + H_x² + H_y²\n\nCompute |HA| = sqrt( (1 - H_x)^2 + H_y² )\n\nCompute |HB| = sqrt( H_x² + H_y² )\n\nTherefore, cosθ = ( -H_x + H_x² + H_y² ) / ( sqrt( (1 - H_x)^2 + H_y² ) sqrt( H_x² + H_y² ) )\n\nThis is quite involved. Let's try to simplify this expression.\n\nFirst, let's compute H_x and H_y in terms of cos20° and sin20°.\n\nH_x = (2 cos20° + 1)(2 + cos20° ) / (5 + 4 cos20° )\n\nMultiply numerator:\n\n(2 cos20° + 1)(2 + cos20° ) = 4 cos20° + 2 cos²20° + 2 + cos20° = 5 cos20° + 2 cos²20° + 2\n\nH_x = [5 cos20° + 2 cos²20° + 2 ] / (5 + 4 cos20° )\n\nSimilarly, H_y = (2 cos20° + 1 ) sin20° / (5 + 4 cos20° )\n\nLet's compute H_x² + H_y²:\n\nH_x² + H_y² = [ (5 cos20° + 2 cos²20° + 2 )² + ( (2 cos20° + 1 ) sin20° )² ] / (5 + 4 cos20° )²\n\nThis is quite complicated. Maybe instead of expanding, we can find a trigonometric identity or simplify using substitution.\n\nAlternatively, perhaps substituting numerical values with higher precision would give a more accurate angle.\n\nLet's compute H_x, H_y, then compute HA · HB, |HA|, |HB|.\n\nFirst, compute cos20° ≈ 0.9396926, sin20° ≈ 0.3420201.\n\nCompute H_x:\n\nNumerator: (2*0.9396926 + 1)(2 + 0.9396926 ) = (1.8793852 + 1)(2.9396926 ) = (2.8793852)(2.9396926 )\n\nCompute 2.8793852 * 2.9396926:\n\nApproximate as 2.88 * 2.94 ≈ 8.4672\n\nMore precisely:\n\n2.8793852 * 2.9396926:\n\n= (2 + 0.8793852)(2 + 0.9396926 )\n\n= 2*2 + 2*0.9396926 + 0.8793852*2 + 0.8793852*0.9396926\n\n= 4 + 1.8793852 + 1.7587704 + 0.827050\n\n≈ 4 + 1.8794 + 1.7588 + 0.82705 ≈ 8.46525\n\nDenominator of H_x: 5 + 4*0.9396926 ≈ 5 + 3.7587704 ≈ 8.7587704\n\nThus, H_x ≈ 8.46525 / 8.7587704 ≈ 0.9667\n\nSimilarly, H_y:\n\n(2*0.9396926 + 1 ) * 0.3420201 / 8.7587704\n\nNumerator: (1.8793852 + 1 ) * 0.3420201 ≈ 2.8793852 * 0.3420201 ≈ 0.9848\n\nThus, H_y ≈ 0.9848 / 8.7587704 ≈ 0.1125\n\nSo, H ≈ (0.9667, 0.1125 )\n\nTherefore, HA = (1 - 0.9667, 0 - 0.1125 ) ≈ (0.0333, -0.1125 )\n\nHB = (-0.9667, -0.1125 )\n\nCompute HA · HB ≈ (0.0333)(-0.9667) + (-0.1125)(-0.1125 ) ≈ -0.0322 + 0.01266 ≈ -0.0195\n\nCompute |HA| ≈ sqrt(0.0333² + (-0.1125)^2 ) ≈ sqrt(0.00111 + 0.01266 ) ≈ sqrt(0.01377 ) ≈ 0.1174\n\nCompute |HB| ≈ sqrt(0.9667² + 0.1125² ) ≈ sqrt(0.9346 + 0.01266 ) ≈ sqrt(0.9473 ) ≈ 0.9733\n\nThus, cosθ ≈ -0.0195 / (0.1174 * 0.9733 ) ≈ -0.0195 / 0.1143 ≈ -0.1706\n\nTherefore, θ ≈ arccos(-0.1706 ) ≈ 100°. As before.\n\nBut the problem states \"Find the measure of angle AHB.\" 100° seems a bit high, but maybe that's the correct answer. However, given the setup, perhaps there's a geometric insight I'm missing.\n\nWait, let's consider triangle ABC. AB = BC, angle B = 20°, so it's an isosceles triangle with AB = BC, base angles at A and C are 80°. Point M divides AC in ratio 1:2. H is the projection of C onto BM. The angle AHB is approximately 100°, but is there a way to see this as supplementary or complementary to another angle?\n\nAlternatively, perhaps using the Law of Sines in triangle AHB. But we need the lengths of AH, BH, or AB. AB is given as equal to BC. Wait, AB is 1 in our coordinate system. So, AB = BC = 1. In our coordinates, AB is from (0,0) to (1,0), so length 1. BC is from (0,0) to (cos20°, sin20°), which has length sqrt( cos²20° + sin²20° ) = 1. So that checks out.\n\nBut BH is the distance from B to H, which in our coordinates is |HB| ≈ 0.9733. AH is distance from A to H, which is approximately 0.1174. Then, AB is 1. So, in triangle AHB, sides:\n\nAB = 1, AH ≈ 0.1174, BH ≈ 0.9733\n\nApplying the Law of Cosines:\n\nAB² = AH² + BH² - 2 AH BH cosθ\n\nBut AB is opposite angle H, but here angle at H is angle AHB.\n\nWait, no, AB is the side opposite angle H in triangle AHB.\n\nWait, triangle AHB has points A, H, B.\n\nSo, side AH: length |AH| ≈ 0.1174\n\nSide BH: length |BH| ≈ 0.9733\n\nSide AB: length 1.\n\nLaw of Cosines:\n\nAB² = AH² + BH² - 2 AH BH cosθ\n\nSo:\n\n1² = (0.1174)^2 + (0.9733)^2 - 2 * 0.1174 * 0.9733 * cosθ\n\nCalculate RHS:\n\n0.01377 + 0.9473 - 2*0.1174*0.9733 * cosθ ≈ 0.9611 - 0.2284 * cosθ\n\nSet equal to 1:\n\n0.9611 - 0.2284 cosθ = 1\n\n=> -0.2284 cosθ = 1 - 0.9611 = 0.0389\n\n=> cosθ ≈ -0.0389 / 0.2284 ≈ -0.1703\n\nWhich matches our previous result. So θ ≈ arccos(-0.1703 ) ≈ 100°. So my calculation seems consistent.\n\nBut 100° is the approximate answer. However, the problem likely expects an exact value. So maybe there's a way to simplify cosθ = -0.1703. Wait, perhaps we can find an exact expression.\n\nAlternatively, maybe the exact value is 100°, since 100° is 80° + 20°, and the triangle has angles of 20°, 80°, 80°. But how?\n\nAlternatively, maybe recognizing that angle AHB is 180° - angle HBC - angle HBA, but this is unclear.\n\nAlternatively, let's consider the following: since H is the projection of C onto BM, then CH is perpendicular to BM. So, in triangle HBC, angle at H is 90°. So, triangle HBC is right-angled at H.\n\nMaybe there's a cyclic quadrilateral here. If we can find some cyclic quadrilaterals, then opposite angles sum to 180°, etc.\n\nAlternatively, let me consider triangle AHB. If we can find that AH is equal to BH, then angle AHB would be found via isosceles triangle. But in our coordinates, AH ≈ 0.1174 and BH ≈ 0.9733, so not equal.\n\nAlternatively, maybe triangle AHB is related to some other triangle in the figure.\n\nWait, let me think again. Since ABC is isosceles with AB = BC, and angle at B is 20°, and M is on AC such that AM:MC = 1:2. Then, perhaps constructing triangle ABM and triangle CBM.\n\nAlternatively, maybe using the Law of Sines in triangle ABM and triangle CBM.\n\nWait, in triangle ABM:\n\nAB = 1, AM = (1/3)AC.\n\nFirst, compute AC. Since ABC is isosceles with AB = BC = 1, angle at B = 20°, so using the Law of Cosines:\n\nAC² = AB² + BC² - 2 AB BC cos(angle B)\n\n= 1² + 1² - 2 * 1 * 1 * cos20°\n\n= 2 - 2 cos20°\n\nSo, AC = sqrt(2 - 2 cos20° ) = 2 sin(10° ), since sqrt(2 - 2 cosθ) = 2 sin(θ/2). So, θ = 20°, hence AC = 2 sin10° ≈ 0.347296.\n\nBut AM:MC = 1:2, so AM = (1/3)AC = (2/3) sin10°, and MC = (2/3) AC = (4/3) sin10°.\n\nBut perhaps this is not directly helpful. However, in triangle ABM, sides AB = 1, AM = (2/3) sin10°, angle at A is 80°. Maybe apply the Law of Sines to find angle at M or something else.\n\nAlternatively, in triangle BMC, but perhaps this is getting too convoluted.\n\nAlternatively, consider that point H is the foot of the perpendicular from C to BM, so HC is perpendicular to BM. Therefore, in triangle HBC, angle at H is 90°, and we know BC = 1.\n\nSo, in triangle HBC, we can find BH and HC. Let's try to compute BH and HC.\n\nBH is the distance from B to H, which we already approximated as 0.9733. HC is the distance from H to C. Since H is the foot of the perpendicular, HC is the height from C to BM. Let's compute HC.\n\nIn triangle BMC, but maybe via coordinates. Point C is at (cos20°, sin20° ), H is at (0.9667, 0.1125). So HC is the distance between these two points:\n\nHC = sqrt( (cos20° - 0.9667 )² + (sin20° - 0.1125 )² )\n\nCompute cos20° - 0.9667 ≈ 0.9396926 - 0.9667 ≈ -0.0270\n\nsin20° - 0.1125 ≈ 0.3420201 - 0.1125 ≈ 0.2295\n\nThus, HC ≈ sqrt( (-0.0270 )² + (0.2295 )² ) ≈ sqrt(0.000729 + 0.05267 ) ≈ sqrt(0.0534 ) ≈ 0.231\n\nThen, in triangle HBC, which is right-angled at H, we can compute angle at B. tan(angle at B) = HC / BH ≈ 0.231 / 0.9733 ≈ 0.237, so angle at B ≈ arctan(0.237 ) ≈ 13.3°, which is approximately 13.3 degrees. Therefore, angle HBC ≈ 13.3°, but angle ABC is 20°, so angle ABH = angle ABC - angle HBC ≈ 20° - 13.3° ≈ 6.7°. But I'm not sure how this helps.\n\nAlternatively, perhaps in triangle AHB, with angle H ≈ 100°, but that seems like the approximate answer.\n\nBut the problem is likely expecting an exact value. Wait, let's see if 100° is the correct answer. But 100° is not directly related to the given angles. Alternatively, maybe 30°, but how?\n\nWait, maybe there's a geometric construction I'm missing. Let me try to construct the figure with exact values.\n\nGiven triangle ABC, AB = BC = 1, angle B = 20°, AC = 2 sin10°, as calculated before. Point M divides AC into AM:MC = 1:2, so AM = (2/3) sin10°, and MC = (4/3) sin10°. H is the projection of C onto BM.\n\nWait, maybe using trigonometric identities. Let me try to compute angle AHB using exact expressions.\n\nFrom earlier, we have:\n\nH_x = (2 cos20° + 1)(2 + cos20° ) / (5 + 4 cos20° )\n\nH_y = (2 cos20° + 1 ) sin20° / (5 + 4 cos20° )\n\nLet me express HA and HB vectors in terms of these coordinates.\n\nVector HA = (1 - H_x, -H_y )\n\nVector HB = (-H_x, -H_y )\n\nDot product HA · HB = (1 - H_x)(-H_x) + (-H_y)(-H_y ) = -H_x + H_x² + H_y²\n\nWe need to compute this expression:\n\n-H_x + H_x² + H_y²\n\nSubstitute H_x and H_y:\n\nFirst, compute H_x² + H_y²:\n\nH_x² + H_y² = [ (2 cos20° + 1 )^2 (2 + cos20° )^2 + (2 cos20° + 1 )^2 sin²20° ] / (5 + 4 cos20° )²\n\nFactor out (2 cos20° + 1 )^2:\n\n= (2 cos20° + 1 )^2 [ (2 + cos20° )^2 + sin²20° ] / (5 + 4 cos20° )²\n\nExpand (2 + cos20° )^2 + sin²20°:\n\n= 4 + 4 cos20° + cos²20° + sin²20°\n\n= 4 + 4 cos20° + 1\n\n= 5 + 4 cos20°\n\nTherefore, H_x² + H_y² = (2 cos20° + 1 )^2 (5 + 4 cos20° ) / (5 + 4 cos20° )² = (2 cos20° + 1 )^2 / (5 + 4 cos20° )\n\nThus, H_x² + H_y² = (2 cos20° + 1 )^2 / (5 + 4 cos20° )\n\nNow, compute -H_x + H_x² + H_y²:\n\n= -H_x + (2 cos20° + 1 )^2 / (5 + 4 cos20° )\n\nBut H_x = (2 cos20° + 1)(2 + cos20° ) / (5 + 4 cos20° )\n\nTherefore:\n\n-H_x + (2 cos20° + 1 )^2 / (5 + 4 cos20° ) = [ - (2 cos20° + 1)(2 + cos20° ) + (2 cos20° + 1 )^2 ] / (5 + 4 cos20° )\n\nFactor out (2 cos20° + 1 ):\n\n= (2 cos20° + 1 ) [ - (2 + cos20° ) + (2 cos20° + 1 ) ] / (5 + 4 cos20° )\n\nCompute the expression inside the brackets:\n\n- (2 + cos20° ) + (2 cos20° + 1 ) = -2 - cos20° + 2 cos20° + 1 = (-2 + 1 ) + ( - cos20° + 2 cos20° ) = -1 + cos20°\n\nTherefore, numerator:\n\n(2 cos20° + 1 ) ( -1 + cos20° ) / (5 + 4 cos20° )\n\nTherefore, HA · HB = (2 cos20° + 1 )( cos20° - 1 ) / (5 + 4 cos20° )\n\nNow, compute |HA| and |HB|\n\nFirst, |HB| = sqrt( H_x² + H_y² ) = (2 cos20° + 1 ) / sqrt(5 + 4 cos20° )\n\nBecause H_x² + H_y² = (2 cos20° + 1 )^2 / (5 + 4 cos20° ), so sqrt of that is (2 cos20° + 1 ) / sqrt(5 + 4 cos20° )\n\nNow, |HA| = sqrt( (1 - H_x )² + H_y² )\n\nCompute 1 - H_x:\n\n1 - H_x = 1 - (2 cos20° + 1)(2 + cos20° ) / (5 + 4 cos20° )\n\nLet me compute numerator:\n\n(5 + 4 cos20° ) - (2 cos20° + 1 )(2 + cos20° )\n\nExpand (2 cos20° + 1 )(2 + cos20° ) = 4 cos20° + 2 cos²20° + 2 + cos20° = 5 cos20° + 2 cos²20° + 2\n\nSo, numerator:\n\n(5 + 4 cos20° ) - (5 cos20° + 2 cos²20° + 2 ) = 5 + 4 cos20° - 5 cos20° - 2 cos²20° - 2 = 3 - cos20° - 2 cos²20°\n\nTherefore, 1 - H_x = (3 - cos20° - 2 cos²20° ) / (5 + 4 cos20° )\n\nTherefore, |HA| = sqrt( [ (3 - cos20° - 2 cos²20° ) / (5 + 4 cos20° ) ]^2 + [ (2 cos20° + 1 ) sin20° / (5 + 4 cos20° ) ]^2 )\n\nFactor out 1 / (5 + 4 cos20° )^2:\n\n= sqrt( [ (3 - cos20° - 2 cos²20° )² + (2 cos20° + 1 )² sin²20° ] / (5 + 4 cos20° )^2 )\n\n= sqrt( numerator ) / (5 + 4 cos20° )\n\nLet me compute the numerator:\n\n(3 - cos20° - 2 cos²20° )² + (2 cos20° + 1 )² sin²20°\n\nExpand first term:\n\nLet me denote X = 3 - cos20° - 2 cos²20°.\n\nX = 3 - cos20° - 2 cos²20°\n\n= 3 - cos20° - 2 cos²20°\n\nNote that 2 cos²20° = 1 + cos40°, from double-angle formula. So:\n\nX = 3 - cos20° - (1 + cos40° ) = 2 - cos20° - cos40°\n\nBut this might not help. Alternatively, let's compute X²:\n\nX² = (3 - cos20° - 2 cos²20° )²\n\n= [ 3 - cos20° - 2 cos²20° ]²\n\nSimilarly, the second term:\n\n(2 cos20° + 1 )² sin²20° = (4 cos²20° + 4 cos20° + 1 ) sin²20°\n\nNow, combining both terms:\n\nX² + (2 cos20° + 1 )² sin²20° = [3 - cos20° - 2 cos²20°]^2 + (4 cos²20° + 4 cos20° + 1 ) sin²20°\n\nThis seems very complex. Perhaps using numerical values.\n\nCompute each term numerically:\n\nFirst, compute X = 3 - cos20° - 2 cos²20°:\n\ncos20° ≈ 0.9396926\n\ncos²20° ≈ 0.8830223\n\nSo,\n\nX ≈ 3 - 0.9396926 - 2 * 0.8830223 ≈ 3 - 0.9396926 - 1.7660446 ≈ 3 - 2.7057372 ≈ 0.2942628\n\nX² ≈ (0.2942628 )² ≈ 0.0866\n\nSecond term: (4 cos²20° + 4 cos20° + 1 ) sin²20°\n\nCompute:\n\n4 cos²20° ≈ 4 * 0.8830223 ≈ 3.5320892\n\n4 cos20° ≈ 4 * 0.9396926 ≈ 3.7587704\n\nSo, 4 cos²20° + 4 cos20° + 1 ≈ 3.5320892 + 3.7587704 + 1 ≈ 8.2908596\n\nsin²20° ≈ (0.3420201 )² ≈ 0.1169778\n\nMultiply: 8.2908596 * 0.1169778 ≈ 0.970\n\nTherefore, total numerator ≈ 0.0866 + 0.970 ≈ 1.0566\n\nThus, |HA| ≈ sqrt(1.0566 ) / (5 + 4 cos20° ) ≈ 1.0278 / 8.75877 ≈ 0.1174, which matches our previous approximation.\n\nNow, putting it all together:\n\ncosθ = HA · HB / ( |HA| |HB| ) = [ (2 cos20° + 1 )( cos20° - 1 ) / (5 + 4 cos20° ) ] / [ (0.1174 )( 0.9733 ) ]\n\nBut wait, symbolically:\n\ncosθ = [ (2 cos20° + 1 )( cos20° - 1 ) / (5 + 4 cos20° ) ] / [ ( (2 cos20° + 1 ) / sqrt(5 + 4 cos20° )) * ( sqrt(1.0566 ) / (5 + 4 cos20° ) ) ) ]\n\nThis is getting too complicated. Maybe instead, recognizing that after simplification, angle AHB is 100°.\n\nGiven the approximate calculation, it's 100°. But why would that be the case? Let me check with an exact trigonometric identity.\n\nSuppose that angle AHB is 100°, then cosθ = cos100° ≈ -0.1736, which is close to our approximate -0.1705. The difference could be due to approximation errors. So, 100° is a possible answer. But perhaps there's a way to see this exactly.\n\nAlternatively, consider that in triangle ABC, angle B = 20°, AB = BC, and M divides AC in ratio 1:2. The projection H of C onto BM creates angle AHB. \n\nAlternatively, construct triangle ABH. Maybe triangle ABH is related to some other triangle.\n\nAlternatively, perhaps using the Law of Sines in triangle AHB:\n\nsin(angle AHB)/AB = sin(angle HAB)/HB\n\nBut I don't know angle HAB.\n\nAlternatively, if angle AHB is 100°, then the other angles in triangle AHB would be angle HAB and angle HBA, which would sum to 80°. Without further information, this might not be helpful.\n\nGiven the calculation above leading to approximately 100°, which is close to 100°, and since the problem likely expects an exact answer, and given the construction involving 20° and 80° angles, I think the answer is 100°. Therefore, the measure of angle AHB is 100 degrees.\n\n**Final Answer**\nThe measure of angle \\(AHB\\) is \\(\\boxed{100^\\circ}\\).\n</think>To find the measure of angle \\( \\angle AHB \\) in triangle \\( ABC \\) with \\( AB = BC \\) and \\( \\angle B = 20^\\circ \\), and point \\( M \\) on \\( AC \\) such that \\( AM : MC = 1 : 2 \\), and point \\( H \\) is the projection of \\( C \\) onto \\( BM \\), we proceed as follows:\n\n1. **Identify Key Properties and Coordinates**:\n   - Since \\( AB = BC \\), triangle \\( ABC \\) is isosceles with \\( \\angle BAC = \\angle BCA = 80^\\circ \\).\n   - Place \\( B \\) at the origin \\((0, 0)\\), \\( A \\) at \\((1, 0)\\), and \\( C \\) at \\((\\cos 20^\\circ, \\sin 20^\\circ)\\).\n   - Point \\( M \\) divides \\( AC \\) in the ratio \\( 1:2 \\), so the coordinates of \\( M \\) are:\n     \\[\n     M = \\left( \\frac{2 + \\cos 20^\\circ}{3}, \\frac{\\sin 20^\\circ}{3} \\right)\n     \\]\n\n2. **Equation of Line \\( BM \\)**:\n   - The slope of \\( BM \\) is:\n     \\[\n     \\text{slope of } BM = \\frac{\\frac{\\sin 20^\\circ}{3} - 0}{\\frac{2 + \\cos 20^\\circ}{3} - 0} = \\frac{\\sin 20^\\circ}{2 + \\cos 20^\\circ}\n     \\]\n   - The equation of line \\( BM \\) is:\n     \\[\n     y = \\frac{\\sin 20^\\circ}{2 + \\cos 20^\\circ} x\n     \\]\n\n3. **Projection of \\( C \\) onto \\( BM \\)**:\n   - The projection \\( H \\) of \\( C \\) onto \\( BM \\) is the foot of the perpendicular from \\( C \\) to \\( BM \\). Using the projection formula, the coordinates of \\( H \\) are:\n     \\[\n     H = \\left( \\frac{(2 \\cos 20^\\circ + 1)(2 + \\cos 20^\\circ)}{5 + 4 \\cos 20^\\circ}, \\frac{(2 \\cos 20^\\circ + 1) \\sin 20^\\circ}{5 + 4 \\cos 20^\\circ} \\right)\n     \\]\n\n4. **Vectors and Angle Calculation**:\n   - The vectors \\( \\overrightarrow{HA} \\) and \\( \\overrightarrow{HB} \\) are:\n     \\[\n     \\overrightarrow{HA} = \\left( 1 - \\frac{(2 \\cos 20^\\circ + 1)(2 + \\cos 20^\\circ)}{5 + 4 \\cos 20^\\circ}, -\\frac{(2 \\cos 20^\\circ + 1) \\sin 20^\\circ}{5 + 4 \\cos 20^\\circ} \\right)\n     \\]\n     \\[\n     \\overrightarrow{HB} = \\left( -\\frac{(2 \\cos 20^\\circ + 1)(2 + \\cos 20^\\circ)}{5 + 4 \\cos 20^\\circ}, -\\frac{(2 \\cos 20^\\circ + 1) \\sin 20^\\circ}{5 + 4 \\cos 20^\\circ} \\right)\n     \\]\n   - The dot product \\( \\overrightarrow{HA} \\cdot \\overrightarrow{HB} \\) is:\n     \\[\n     \\overrightarrow{HA} \\cdot \\overrightarrow{HB} = -\\left( \\frac{(2 \\cos 20^\\circ + 1)(2 + \\cos 20^\\circ)}{5 + 4 \\cos 20^\\circ} \\right) + \\left( \\frac{(2 \\cos 20^\\circ + 1)(2 + \\cos 20^\\circ)}{5 + 4 \\cos 20^\\circ} \\right)^2 + \\left( \\frac{(2 \\cos 20^\\circ + 1) \\sin 20^\\circ}{5 + 4 \\cos 20^\\circ} \\right)^2\n     \\]\n   - The magnitudes \\( |\\overrightarrow{HA}| \\) and \\( |\\overrightarrow{HB}| \\) are:\n     \\[\n     |\\overrightarrow{HA}| = \\sqrt{ \\left( 1 - \\frac{(2 \\cos 20^\\circ + 1)(2 + \\cos 20^\\circ)}{5 + 4 \\cos 20^\\circ} \\right)^2 + \\left( \\frac{(2 \\cos 20^\\circ + 1) \\sin 20^\\circ}{5 + 4 \\cos 20^\\circ} \\right)^2 }\n     \\]\n     \\[\n     |\\overrightarrow{HB}| = \\sqrt{ \\left( \\frac{(2 \\cos 20^\\circ + 1)(2 + \\cos 20^\\circ)}{5 + 4 \\cos 20^\\circ} \\right)^2 + \\left( \\frac{(2 \\cos 20^\\circ + 1) \\sin 20^\\circ}{5 + 4 \\cos 20^\\circ} \\right)^2 }\n     \\]\n   - Using the dot product and magnitudes, the cosine of the angle \\( \\angle AHB \\) is:\n     \\[\n     \\cos \\angle AHB = \\frac{\\overrightarrow{HA} \\cdot \\overrightarrow{HB}}{|\\overrightarrow{HA}| |\\overrightarrow{HB}|} \\approx -0.1705\n     \\]\n   - Therefore, \\( \\angle AHB \\approx 100^\\circ \\).\n\nThus, the measure of angle \\( \\angle AHB \\) is \\(\\boxed{100^\\circ}\\)."}