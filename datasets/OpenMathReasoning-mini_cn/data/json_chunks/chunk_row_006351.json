{"row_number": 6351, "expected_answer": "\\(-4\\pi\\)", "problem": "How do you apply <PERSON><PERSON><PERSON>'s Theorem to the function \\( f(z) = \\frac{2z}{z-i} \\) where \\( \\Gamma \\) is the circle \\( |z-i| = 3 \\)? Consider the singularity at \\( z = i \\).", "generated_solution": "<think>\nOkay, so I need to apply <PERSON><PERSON><PERSON>'s Theorem to the function f(z) = (2z)/(z - i) where the contour Γ is the circle |z - i| = 3. Hmm, let's start by recalling what <PERSON><PERSON><PERSON>'s <PERSON><PERSON> says. If I remember right, <PERSON><PERSON><PERSON>'s Theorem states that if a function is analytic inside and on a simple closed contour Γ, then the integral of the function around Γ is zero. But wait, there's a singularity at z = i here. Let me check where that singularity is located.\n\nThe function f(z) has a denominator (z - i), so the only singularity is at z = i. Now, the contour Γ is the circle centered at i with radius 3, so |z - i| = 3. That means the circle has a radius of 3, right? So the center is at i, and the radius is 3. The point z = i is the center of this circle. But wait, in complex analysis, if the singularity is inside the contour, then <PERSON><PERSON><PERSON>'s Theorem doesn't apply because the function isn't analytic everywhere inside and on the contour. But if the singularity is outside, then the integral would be zero. Wait, but here z = i is the center, so the distance from the center to the singularity is zero, which is less than the radius 3. So the singularity is inside the contour.\n\nTherefore, since the function has a singularity at z = i inside Γ, <PERSON><PERSON><PERSON>'s Theorem doesn't apply here. Instead, we might need to use the Cauchy Integral Formula. Wait, but the problem says to apply Cauchy's Theorem. Maybe I need to check if the function is analytic in the domain. Let me think. The function f(z) is (2z)/(z - i). The only point where it's not analytic is z = i. Since the contour Γ is the circle of radius 3 around i, then z = i is inside Γ. Therefore, the function is not analytic everywhere inside Γ, so Cauchy's Theorem can't be applied directly. Therefore, the integral is not necessarily zero. Instead, we can compute it using the Cauchy Integral Formula.\n\nWait, but the problem says to apply Cauchy's Theorem. Maybe the answer is that Cauchy's Theorem doesn't apply here because there's a singularity inside Γ, so the integral isn't zero. But maybe I need to elaborate. Alternatively, maybe the problem is a trick question where the answer is zero, but I need to check. Let me re-express the function. Let's see: f(z) = 2z/(z - i). If we can write this as 2(z - i + i)/(z - i) = 2 + 2i/(z - i). Then f(z) = 2 + (2i)/(z - i). Now, integrating f(z) around Γ would be integrating 2 around Γ plus integrating (2i)/(z - i) around Γ. The integral of 2 around a closed contour is zero because it's analytic everywhere. The integral of 2i/(z - i) around Γ would be 2i times 2πi times the residue at z = i. Wait, the residue here is 1, so that term would be 2i * 2πi * 1 = -4π. Wait, but then the total integral would be 0 + (-4π) = -4π. But the problem says to apply Cauchy's Theorem. But maybe there's another way. Alternatively, if the function can be expressed as a derivative, then even with a singularity, maybe... Wait, no. Let me step back.\n\nCauchy's Theorem says that if a function is analytic in a simply connected domain containing Γ, then the integral is zero. Here, the function isn't analytic at z = i, which is inside Γ, so the integral isn't zero. Therefore, Cauchy's Theorem doesn't apply. Alternatively, maybe using the theorem in a different way. Wait, but perhaps the problem is asking how to apply Cauchy's Theorem, not to compute the integral. Maybe the answer is that since the function isn't analytic inside Γ due to the singularity at z = i, Cauchy's Theorem cannot be applied. Alternatively, maybe the answer is that the integral equals 2πi times the residue at z = i, which would be 2πi * 2i = 4πi² = -4π. Wait, that's using the Residue Theorem, not Cauchy's Theorem. So perhaps the problem is expecting to explain that since the function has a singularity inside Γ, Cauchy's Theorem does not apply, and instead, we use the Cauchy Integral Formula or Residue Theorem. Let me check the function again.\n\nWait, the function f(z) = (2z)/(z - i). Let's see, if I consider integrating this over Γ. Let me use the Cauchy Integral Formula, which states that for a function analytic inside and on Γ, and a point a inside Γ, f(a) = (1/(2πi)) ∫_Γ (f(z)/(z - a)) dz. Wait, but here our function looks similar. Let's write f(z) = 2z/(z - i). If I let f(w) = 2w, then our integrand is f(w)/(w - i), where w is the variable of integration. Then, by the Cauchy Integral Formula, the integral ∫_Γ (2w)/(w - i) dw would be 2πi times f(i), since the formula is ∫_Γ (f(w)/(w - a)) dw = 2πi f(a), where a is the point inside Γ. Here, a = i, so f(a) = 2i. Therefore, the integral would be 2πi * 2i = 4πi² = -4π. Therefore, the integral of f(z) over Γ is -4π.\n\nBut the problem specifically says to apply Cauchy's Theorem. But Cauchy's Theorem gives zero when the function is analytic, but here it's not. So the answer is that we cannot apply Cauchy's Theorem directly because of the singularity inside Γ. Instead, using the Cauchy Integral Formula (which is a consequence of Cauchy's Theorem in some formulations), we compute the integral as -4π.\n\nWait, but maybe the problem is testing whether the student knows that the integral isn't zero, but since the problem asks to apply Cauchy's Theorem, perhaps the answer is that it cannot be applied here. However, given that the problem is phrased \"How do you apply Cauchy's Theorem...\", maybe the correct approach is to note that since the function isn't analytic inside Γ, Cauchy's Theorem does not apply. But the problem might expect the application of Cauchy's Integral Formula instead. Let me check.\n\nAlternatively, perhaps the problem is expecting to recognize that the function has a single simple pole at z = i inside Γ, so by the Residue Theorem, the integral is 2πi times the residue at z = i. The residue here is the coefficient of 1/(z - i) in the Laurent expansion. Since f(z) = 2z/(z - i) = 2(z - i + i)/(z - i) = 2 + 2i/(z - i). Therefore, the residue is 2i. Then the integral is 2πi * 2i = 4πi² = -4π.\n\nBut the problem asks how to apply Cauchy's Theorem, not the Residue Theorem. So maybe the answer is that Cauchy's Theorem isn't applicable here because the function has a singularity inside Γ, but using the Residue Theorem, the integral is -4π. However, the problem might be expecting the use of the Cauchy Integral Formula. Let's see. The Cauchy Integral Formula states that for f(z) analytic inside and on Γ, and a point a inside Γ, f(a) = (1/(2πi)) ∫_Γ (f(z)/(z - a)) dz. If we take f(z) = 2z and a = i, then ∫_Γ (2z)/(z - i) dz = 2πi * f(i) = 2πi * 2i = 4πi² = -4π. So this uses the Cauchy Integral Formula, which is a consequence of Cauchy's Theorem. So perhaps the answer is applying the Cauchy Integral Formula here, leading to the integral being -4π. But the problem specifically says \"apply Cauchy's Theorem\". Hmm.\n\nAlternatively, maybe the problem is considering the general Cauchy's Theorem, which applies to functions with no singularities in the domain. Since there is a singularity inside Γ, Cauchy's Theorem can't be applied, so the integral is not zero and must be computed via other means. But the problem is asking \"how do you apply Cauchy's Theorem\", which might mean that the answer is that you can't apply it here. But perhaps the problem expects the application via the integral formula. Since the problem mentions the singularity at z = i, and the contour includes it, so you can't use Cauchy's Theorem (which requires analyticity), but you can use the Cauchy Integral Formula. So maybe the answer is that the integral is -4π. But the question is phrased as \"How do you apply Cauchy's Theorem...\", which is a bit confusing, because Cauchy's Theorem in its basic form doesn't handle singularities. Therefore, the correct approach would be to use the Cauchy Integral Formula here. However, the problem might be expecting this as the answer, even though it's not exactly Cauchy's Theorem. Let me confirm once more.\n\nAlternatively, maybe Cauchy's Theorem is used to derive the Cauchy Integral Formula. So, in applying the problem, you would recognize that since f(z) has a singularity inside Γ, Cauchy's Theorem doesn't apply, but by using the Cauchy Integral Formula (which is a related result), you can compute the integral. Therefore, the answer is -4π. But the problem explicitly mentions applying Cauchy's Theorem. Hmm. Maybe the key point is that Cauchy's Theorem cannot be applied here, so the answer is that you cannot apply Cauchy's Theorem because the function isn't analytic inside Γ. But the problem might be expecting the integral value. Let me check standard examples. If a function has a single pole inside the contour, then Cauchy's Theorem doesn't apply, and the integral is non-zero. So the answer here would be the integral equals -4π. But since the problem asks to apply Cauchy's Theorem, maybe it's a trick question where the answer is that you can't. But given the problem structure, perhaps the expected answer is \\boxed{-4\\pi} using the Cauchy Integral Formula. Given that the user mentions \"put the answer inside \\boxed{}\", it's likely that the numerical answer is expected. So even though Cauchy's Theorem isn't directly applicable, using the appropriate theorem (Cauchy Integral Formula) gives the answer. Therefore, the answer is \\boxed{-4\\pi}.\n</think>To apply Cauchy's Theorem to the function \\( f(z) = \\frac{2z}{z-i} \\) around the contour \\( \\Gamma \\) defined by \\( |z-i| = 3 \\), we first note that Cauchy's Theorem requires the function to be analytic inside and on the contour. However, \\( f(z) \\) has a singularity at \\( z = i \\), which lies inside \\( \\Gamma \\). Therefore, Cauchy's Theorem does not apply directly.\n\nInstead, we use the Cauchy Integral Formula, which states that for a function \\( f(z) \\) analytic inside and on \\( \\Gamma \\) and a point \\( a \\) inside \\( \\Gamma \\):\n\n\\[\nf(a) = \\frac{1}{2\\pi i} \\oint_\\Gamma \\frac{f(z)}{z-a} \\, dz\n\\]\n\nWe start by rewriting \\( f(z) \\):\n\n\\[\nf(z) = \\frac{2z}{z-i} = 2 + \\frac{2i}{z-i}\n\\]\n\nThe integral of \\( f(z) \\) around \\( \\Gamma \\) can be split into two parts:\n\n\\[\n\\oint_\\Gamma f(z) \\, dz = \\oint_\\Gamma \\left( 2 + \\frac{2i}{z-i} \\right) \\, dz = \\oint_\\Gamma 2 \\, dz + \\oint_\\Gamma \\frac{2i}{z-i} \\, dz\n\\]\n\nThe first integral is zero because the integrand is a constant:\n\n\\[\n\\oint_\\Gamma 2 \\, dz = 0\n\\]\n\nFor the second integral, we use the Cauchy Integral Formula. Here, \\( f(z) = 2i \\) (which is analytic everywhere), and \\( a = i \\):\n\n\\[\n\\oint_\\Gamma \\frac{2i}{z-i} \\, dz = 2\\pi i \\cdot 2i = 4\\pi i^2 = -4\\pi\n\\]\n\nThus, the integral of \\( f(z) \\) around \\( \\Gamma \\) is:\n\n\\[\n\\oint_\\Gamma f(z) \\, dz = 0 + (-4\\pi) = -4\\pi\n\\]\n\nTherefore, the final answer is:\n\n\\[\n\\boxed{-4\\pi}\n\\]"}