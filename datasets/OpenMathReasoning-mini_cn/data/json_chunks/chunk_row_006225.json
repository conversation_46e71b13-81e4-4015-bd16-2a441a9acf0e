{"row_number": 6225, "expected_answer": "\\( n(n-1) \\)", "problem": "Find the expression for the $n$th term of the sequence $0, 2, 6, 12, 20, \\dots$.", "generated_solution": "<think>\nOkay, let's see. I need to find the nth term of the sequence 0, 2, 6, 12, 20, and so on. Hmm, let's start by looking at the differences between the terms to see if there's a pattern.\n\nThe first term is 0, the second is 2. The difference here is 2 - 0 = 2. Then the third term is 6, so 6 - 2 = 4. Next, 12 - 6 = 6, and 20 - 12 = 8. So the differences between consecutive terms are 2, 4, 6, 8, etc. That looks like an arithmetic sequence increasing by 2 each time. So the first difference is 2, then 4, then 6, then 8... So each difference is 2n where n starts at 1? Wait, but the first difference (between term 1 and 2) is 2, which would be 2*1. Then between term 2 and 3, it's 4 which is 2*2, then 2*3=6, etc. So the difference between term k and term k+1 is 2k. \n\nBut how do I get the nth term from that? Since the differences are increasing by 2 each time, maybe the nth term is related to the sum of these differences up to n-1 terms. Let's think. \n\nLet me write out the terms:\n\nTerm 1: 0\n\nTerm 2: 0 + 2 = 2\n\nTerm 3: 2 + 4 = 6\n\nTerm 4: 6 + 6 = 12\n\nTerm 5: 12 + 8 = 20\n\nSo each term is the previous term plus an even number. So the nth term is the sum of the first (n-1) even numbers starting from 2. Wait, the first difference is between term 1 and 2, which is 2, so for term n, the number of differences we add is (n-1) terms. The sum of the first m even numbers is m(m+1). Wait, the sum of the first m even numbers is 2 + 4 + 6 + ... + 2m = 2(1 + 2 + 3 + ... + m) = 2*(m(m+1)/2) = m(m+1). But in our case, for the nth term, we're adding (n-1) differences. So the sum would be (n-1)*n. Because m here is (n-1). So 2 + 4 + 6 + ... + 2(n-1) = (n-1)*n. \n\nWait, let's test that. For term 2, n=2, so sum is (2-1)*2 = 1*2=2. Yes, term 2 is 2. For term 3, n=3, sum is 2*(3-1) = 2*2=4, but wait no, wait the sum should be (3-1)*3= 2*3=6. But term 3 is 6, which matches. Similarly term 4: (4-1)*4=3*4=12, which is term 4. Term 5: 4*5=20. That works. So the nth term is (n-1)*n.\n\nSo the formula is n(n-1). Let me check for n=1: 1*0=0, which is correct. n=2: 2*1=2, correct. Yep, that seems to work. So the nth term is n squared minus n. So the expression is n(n - 1) or n² - n. Let me see if there's another way to represent this. Maybe it's related to triangular numbers? Because triangular numbers are n(n+1)/2, but here we have n(n-1), which is similar but different. Maybe not necessary. The answer is n(n - 1). Let me confirm with term 5: 5*4=20, which matches the fifth term. I think that's it.\n</think>To find the expression for the \\(n\\)-th term of the sequence \\(0, 2, 6, 12, 20, \\dots\\), we start by examining the differences between consecutive terms:\n\n- The difference between the first and second term is \\(2 - 0 = 2\\).\n- The difference between the second and third term is \\(6 - 2 = 4\\).\n- The difference between the third and fourth term is \\(12 - 6 = 6\\).\n- The difference between the fourth and fifth term is \\(20 - 12 = 8\\).\n\nWe observe that the differences form an arithmetic sequence: \\(2, 4, 6, 8, \\dots\\), where the \\(k\\)-th difference is given by \\(2k\\).\n\nTo find the \\(n\\)-th term of the original sequence, we need to sum the first \\((n-1)\\) differences. The \\(n\\)-th term is given by:\n\n\\[\n\\text{Term}_n = \\sum_{k=1}^{n-1} 2k\n\\]\n\nWe can factor out the 2 from the sum:\n\n\\[\n\\sum_{k=1}^{n-1} 2k = 2 \\sum_{k=1}^{n-1} k\n\\]\n\nThe sum of the first \\((n-1)\\) positive integers is given by the formula for the sum of an arithmetic series:\n\n\\[\n\\sum_{k=1}^{n-1} k = \\frac{(n-1)n}{2}\n\\]\n\nSubstituting this into our expression, we get:\n\n\\[\n2 \\sum_{k=1}^{n-1} k = 2 \\cdot \\frac{(n-1)n}{2} = (n-1)n\n\\]\n\nThus, the expression for the \\(n\\)-th term of the sequence is:\n\n\\[\n\\boxed{n(n-1)}\n\\]"}