{"row_number": 19085, "expected_answer": "Converges.", "problem": "Study the convergence of the series \\[ \\sum_{n=1}^{\\infty}\\frac{\\sqrt{p_{n}}\\cdot\\log p_{n}}{n^2},\\] where \\( p_{n} \\) denotes the \\( n \\)-th prime number.", "generated_solution": "<think>\nOkay, so I need to figure out whether the series ∑ (sqrt(p_n) * log p_n) / n² converges. Hmm, where to start? Let me recall some tests for convergence. Maybe comparison test? The terms are positive, so that should work.\n\nFirst, let's think about the behavior of p_n. The nth prime number p_n is known to be approximately n log n for large n. There's the prime number theorem which says that p_n ~ n log n as n approaches infinity. So maybe I can use that approximation here.\n\nSo if p_n ≈ n log n, then sqrt(p_n) would be roughly sqrt(n log n). Then log p_n would be log(n log n) = log n + log log n. For large n, log log n is much smaller than log n, so maybe we can approximate log p_n ≈ log n.\n\nPutting that together, the numerator would be approximately sqrt(n log n) * log n = n^{1/2} (log n)^{3/2}. The denominator is n². So each term is roughly (n^{1/2} (log n)^{3/2}) / n² = (log n)^{3/2} / n^{3/2}.\n\nSo the general term behaves like (log n)^{3/2} / n^{3/2}. Now, to check convergence, I can compare this to the series ∑ 1 / n^p. Here, p = 3/2, which is greater than 1, so the p-series ∑1/n^{3/2} converges. But we have an extra (log n)^{3/2} factor. However, even with the logarithmic factor, the series ∑ (log n)^k / n^p converges if p > 1, right? Because the logarithm grows slower than any positive power of n. So even though (log n)^{3/2} increases, it's still dominated by n^{ε} for any ε > 0. So multiplying by (log n)^{3/2} doesn't affect the convergence when p=3/2 >1.\n\nTherefore, by the comparison test, since the terms are positive and our general term is asymptotically less than, say, 1/n^{1.25} (since 3/2 is 1.5, and 1.5 minus a small ε would still be greater than 1), the series should converge.\n\nBut wait, let me make this rigorous. Let's use the limit comparison test. Let a_n = sqrt(p_n) * log p_n / n² and b_n = (log n)^{3/2} / n^{3/2}. Then compute the limit as n approaches infinity of a_n / b_n.\n\nIf this limit is a positive finite number, then both series ∑a_n and ∑b_n will have the same convergence behavior.\n\nSo let's compute a_n / b_n:\n\n= [sqrt(p_n) * log p_n / n²] / [(log n)^{3/2} / n^{3/2}]\n\n= [sqrt(p_n) * log p_n / n²] * [n^{3/2} / (log n)^{3/2}]\n\n= sqrt(p_n) * log p_n * n^{3/2} / (n² * (log n)^{3/2})\n\n= sqrt(p_n) * log p_n / (n^{1/2} * (log n)^{3/2})\n\nNow, substitute p_n ~ n log n:\n\nsqrt(p_n) ~ sqrt(n log n) = (n log n)^{1/2}\n\nlog p_n ~ log(n log n) = log n + log log n ~ log n (since log log n is negligible compared to log n for large n)\n\nSo substituting:\n\nsqrt(p_n) ~ (n log n)^{1/2}\n\nlog p_n ~ log n\n\nSo numerator becomes (n log n)^{1/2} * log n = n^{1/2} (log n)^{3/2}\n\nDenominator: n^{1/2} (log n)^{3/2}\n\nThus, a_n / b_n ~ [n^{1/2} (log n)^{3/2}] / [n^{1/2} (log n)^{3/2}] = 1\n\nTherefore, the limit is 1, which is a positive finite number. Therefore, ∑a_n and ∑b_n converge or diverge together. Since ∑b_n = ∑ (log n)^{3/2}/n^{3/2} converges (as p=3/2 >1), then ∑a_n also converges.\n\nAlternatively, maybe use the comparison test directly. Since p_n ≥ n for all n (since the first prime is 2, then 3, 5, etc., so p_n ≥ n+1 for n ≥1, but asymptotically n log n). So sqrt(p_n) ≤ sqrt(C n log n) for some constant C. Then the numerator is ≤ sqrt(C n log n) * log p_n. But log p_n ~ log n, so the numerator is O( sqrt(n log n) * log n ) = O( n^{1/2} (log n)^{3/2} ). The denominator is n². So the term is O( (log n)^{3/2} / n^{3/2} ), which is summable.\n\nAlternatively, using the integral test. If f(n) = (log n)^{3/2} / n^{3/2}, then the integral from 2 to infinity of (log x)^{3/2} / x^{3/2} dx converges. Let me check substitution. Let t = log x, then dt = 1/x dx. Then x = e^t, so when x approaches infinity, t approaches infinity. The integral becomes ∫ (t)^{3/2} / (e^{t})^{3/2} * e^t dt = ∫ t^{3/2} e^{-t/2} dt, which converges because exponential decay dominates. Therefore, the integral converges, hence the series converges.\n\nSo all these methods point towards convergence. Therefore, the given series converges.\n</think>To study the convergence of the series \\(\\sum_{n=1}^{\\infty}\\frac{\\sqrt{p_{n}}\\cdot\\log p_{n}}{n^2}\\), where \\( p_{n} \\) denotes the \\( n \\)-th prime number, we proceed as follows:\n\n1. **Prime Number Theorem Approximation**:\n   By the prime number theorem, for large \\( n \\), the \\( n \\)-th prime number \\( p_n \\) is approximately \\( p_n \\sim n \\log n \\).\n\n2. **Asymptotic Behavior of the Numerator**:\n   Using the approximation \\( p_n \\sim n \\log n \\), we can approximate the numerator \\(\\sqrt{p_n} \\log p_n\\):\n   \\[\n   \\sqrt{p_n} \\approx \\sqrt{n \\log n} = n^{1/2} (\\log n)^{1/2}\n   \\]\n   \\[\n   \\log p_n \\approx \\log(n \\log n) = \\log n + \\log \\log n \\sim \\log n\n   \\]\n   Therefore, the numerator \\(\\sqrt{p_n} \\log p_n\\) behaves asymptotically as:\n   \\[\n   \\sqrt{p_n} \\log p_n \\sim n^{1/2} (\\log n)^{1/2} \\cdot \\log n = n^{1/2} (\\log n)^{3/2}\n   \\]\n\n3. **General Term Approximation**:\n   The general term of the series is:\n   \\[\n   \\frac{\\sqrt{p_n} \\log p_n}{n^2} \\sim \\frac{n^{1/2} (\\log n)^{3/2}}{n^2} = \\frac{(\\log n)^{3/2}}{n^{3/2}}\n   \\]\n\n4. **Limit Comparison Test**:\n   We use the limit comparison test with the series \\(\\sum \\frac{(\\log n)^{3/2}}{n^{3/2}}\\). Let:\n   \\[\n   a_n = \\frac{\\sqrt{p_n} \\log p_n}{n^2} \\quad \\text{and} \\quad b_n = \\frac{(\\log n)^{3/2}}{n^{3/2}}\n   \\]\n   We need to compute the limit:\n   \\[\n   \\lim_{n \\to \\infty} \\frac{a_n}{b_n} = \\lim_{n \\to \\infty} \\frac{\\frac{\\sqrt{p_n} \\log p_n}{n^2}}{\\frac{(\\log n)^{3/2}}{n^{3/2}}} = \\lim_{n \\to \\infty} \\frac{\\sqrt{p_n} \\log p_n}{(\\log n)^{3/2}} \\cdot \\frac{n^{3/2}}{n^2}\n   \\]\n   Using the asymptotic behavior:\n   \\[\n   \\lim_{n \\to \\infty} \\frac{n^{1/2} (\\log n)^{3/2}}{(\\log n)^{3/2}} \\cdot \\frac{n^{3/2}}{n^2} = \\lim_{n \\to \\infty} \\frac{n^{1/2} \\cdot n^{3/2}}{n^2} = \\lim_{n \\to \\infty} \\frac{n^2}{n^2} = 1\n   \\]\n   Since the limit is 1, which is a positive finite number, the series \\(\\sum a_n\\) and \\(\\sum b_n\\) have the same convergence behavior.\n\n5. **Convergence of the Comparison Series**:\n   The series \\(\\sum \\frac{(\\log n)^{3/2}}{n^{3/2}}\\) converges because it is a \\(p\\)-series with \\(p = 3/2 > 1\\).\n\n6. **Conclusion**:\n   Since \\(\\sum \\frac{(\\log n)^{3/2}}{n^{3/2}}\\) converges, by the limit comparison test, the original series \\(\\sum_{n=1}^{\\infty}\\frac{\\sqrt{p_{n}}\\cdot\\log p_{n}}{n^2}\\) also converges.\n\n\\[\n\\boxed{\\text{The series converges}}\n\\]"}