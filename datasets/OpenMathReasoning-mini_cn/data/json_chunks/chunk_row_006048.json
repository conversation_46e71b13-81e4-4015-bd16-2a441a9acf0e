{"row_number": 6048, "expected_answer": "\\(-\\frac{1}{|r|}\\)", "problem": "Find a scalar field whose gradient is \\( u = \\frac{r}{|r|^3} \\).", "generated_solution": "<think>\nOkay, so I need to find a scalar field whose gradient is equal to u = r divided by the magnitude of r cubed. Hmm, let me start by recalling what a gradient is. The gradient of a scalar field is a vector field that points in the direction of the greatest rate of increase of the scalar field, and its magnitude is the rate of change in that direction. So, if I can find a scalar function whose partial derivatives give me the components of u, that should work.\n\nFirst, let me write down the given vector field. The vector u is r over |r|³. In three dimensions, r is the position vector (x, y, z), and |r| is the magnitude, which is sqrt(x² + y² + z²). So, |r|³ would be (x² + y² + z²)^(3/2). Therefore, the vector field u can be written as (x/(x² + y² + z²)^(3/2), y/(x² + y² + z²)^(3/2), z/(x² + y² + z²)^(3/2)).\n\nI remember that the gradient of 1/|r| is a common result. Let me check that. If f(r) = 1/|r|, then f(r) = 1/sqrt(x² + y² + z²). Let's compute the gradient. The partial derivative with respect to x would be (-1/(x² + y² + z²)^(3/2)) * x. Similarly for y and z. So, the gradient of 1/|r| is -r/|r|³. Wait, that's almost the same as our u, but with a negative sign. So if the gradient of (1/|r|) is -u, then maybe the scalar field we're looking for is -1/|r|? Let me verify.\n\nSuppose φ = -1/|r|. Then the gradient of φ would be the gradient of (-1/(sqrt(x² + y² + z²))). Taking partial derivatives:\n\nFor the x-component: dφ/dx = - (-1/( (x² + y² + z²)^(3/2) )) * x = x / (x² + y² + z²)^(3/2). Similarly for y and z. So each component is x/|r|³, y/|r|³, z/|r|³. Which is exactly the vector field u given. So that works! Therefore, the scalar field is -1 divided by |r|, which is the same as -1 over the magnitude of r.\n\nWait, but I should check if there are any constants to consider. For example, could there be a constant added to the scalar field? Well, when taking the gradient, any constant would disappear, so the scalar field is determined up to a constant. But since the problem just asks for \"a\" scalar field, not all possible ones, -1/|r| should suffice.\n\nLet me make sure there's no mistake here. Let's do a quick calculation for one component. Let's compute the derivative of (-1)/sqrt(x² + y² + z²) with respect to x. The derivative of -1*(x² + y² + z²)^(-1/2) is (-1)*(-1/2)*(x² + y² + z²)^(-3/2)*(2x) = (x)/(x² + y² + z²)^(3/2), which is exactly the x-component of u. Same for y and z. So yes, that's correct.\n\nAlternatively, I remember that the electric field due to a point charge is given by E = (1/(4πε₀)) * (q r)/|r|³, and the electric potential is (1/(4πε₀)) * q / |r|. So the potential is a scalar field whose gradient gives the electric field (with a negative sign in some conventions). So in that analogy, if the vector field is proportional to r/|r|³, then the scalar potential would be proportional to 1/|r|. But here, since the vector field is u = r/|r|³, then the scalar field would be -1/|r|, because gradient of (1/|r|) is -r/|r|³. So that matches.\n\nTherefore, the answer should be -1/|r|. But let me write it in terms of r. The scalar field is φ(r) = -1/|r|, which can also be written as -1/(sqrt(x² + y² + z²)). Since the problem states \"a scalar field whose gradient is u\", this should be the solution. Let me just check once more.\n\nAlternatively, suppose I try to integrate the vector field to find the scalar potential. Let me attempt that.\n\nIf the gradient of φ is u, then:\n\n∂φ/∂x = x/(x² + y² + z²)^(3/2)\n\nSimilarly for y and z components. Let's try to integrate the x-component with respect to x. Let’s treat y and z as constants for the partial derivative.\n\nLet me set φ = ∫ x/(x² + y² + z²)^(3/2) dx + C(y,z)\n\nLet me compute that integral. Let me make a substitution: let u = x² + y² + z², then du/dx = 2x, so (du)/2 = x dx.\n\nBut wait, the integral becomes ∫ x / u^(3/2) dx = ∫ x / (u^(3/2)) dx. With substitution u = x² + y² + z², then du = 2x dx, so x dx = du/2. Therefore, the integral becomes ∫ (1/u^(3/2)) * (du/2) ) = (1/2) ∫ u^(-3/2) du = (1/2) * ( u^(-1/2) / (-1/2) ) + C = -u^(-1/2) + C = -1/sqrt(u) + C = -1/sqrt(x² + y² + z²) + C(y,z)\n\nSo φ after integrating the x-component is -1/|r| + C(y,z). Now, we have to check if the other partial derivatives match. Let's compute ∂φ/∂y. The original scalar field from x-integration is -1/sqrt(x² + y² + z²) + C(y,z). Taking the partial derivative with respect to y:\n\n∂φ/∂y = ( (-1) * (-1/2) * (x² + y² + z²)^(-3/2) * 2y ) + ∂C/∂y = y/(x² + y² + z²)^(3/2) + ∂C/∂y\n\nBut according to the given vector field u, this should equal y/(x² + y² + z²)^(3/2). Therefore, ∂C/∂y must be zero. Similarly, integrating the y-component would show that ∂C/∂z = 0. Therefore, C is a constant. Since the problem doesn't specify any boundary condition, we can set C = 0. Therefore, φ = -1/|r|. That confirms the earlier result.\n\nTherefore, the scalar field is -1 over the magnitude of r. So the answer is -1/|r|. But let me check units or dimensions? Wait, no, this is a math problem, so it's okay. So yeah, the answer is the scalar field φ(r) = -1/|r|.\n\n**Final Answer**\nThe scalar field is \\boxed{ -\\dfrac{1}{|\\mathbf{r}|} }.\n</think>To find a scalar field \\(\\phi\\) whose gradient is \\(\\mathbf{u} = \\frac{\\mathbf{r}}{|\\mathbf{r}|^3}\\), we start by considering the form of \\(\\mathbf{u}\\). The vector field \\(\\mathbf{u}\\) can be written as:\n\\[\n\\mathbf{u} = \\left( \\frac{x}{(x^2 + y^2 + z^2)^{3/2}}, \\frac{y}{(x^2 + y^2 + z^2)^{3/2}}, \\frac{z}{(x^2 + y^2 + z^2)^{3/2}} \\right)\n\\]\nwhere \\(\\mathbf{r} = (x, y, z)\\) and \\(|\\mathbf{r}| = \\sqrt{x^2 + y^2 + z^2}\\).\n\nWe hypothesize that the scalar field \\(\\phi\\) is of the form \\(\\phi = -\\frac{1}{|\\mathbf{r}|}\\). To verify this, we compute the gradient of \\(\\phi\\):\n\n1. **Partial derivative with respect to \\(x\\):**\n\\[\n\\frac{\\partial \\phi}{\\partial x} = \\frac{\\partial}{\\partial x} \\left( -\\frac{1}{\\sqrt{x^2 + y^2 + z^2}} \\right) = -\\frac{\\partial}{\\partial x} \\left( (x^2 + y^2 + z^2)^{-1/2} \\right)\n\\]\nUsing the chain rule:\n\\[\n\\frac{\\partial}{\\partial x} \\left( (x^2 + y^2 + z^2)^{-1/2} \\right) = -\\frac{1}{2} (x^2 + y^2 + z^2)^{-3/2} \\cdot 2x = -\\frac{x}{(x^2 + y^2 + z^2)^{3/2}}\n\\]\nThus,\n\\[\n\\frac{\\partial \\phi}{\\partial x} = \\frac{x}{(x^2 + y^2 + z^2)^{3/2}}\n\\]\n\n2. **Partial derivative with respect to \\(y\\):**\n\\[\n\\frac{\\partial \\phi}{\\partial y} = \\frac{\\partial}{\\partial y} \\left( -\\frac{1}{\\sqrt{x^2 + y^2 + z^2}} \\right) = -\\frac{\\partial}{\\partial y} \\left( (x^2 + y^2 + z^2)^{-1/2} \\right)\n\\]\nUsing the chain rule:\n\\[\n\\frac{\\partial}{\\partial y} \\left( (x^2 + y^2 + z^2)^{-1/2} \\right) = -\\frac{1}{2} (x^2 + y^2 + z^2)^{-3/2} \\cdot 2y = -\\frac{y}{(x^2 + y^2 + z^2)^{3/2}}\n\\]\nThus,\n\\[\n\\frac{\\partial \\phi}{\\partial y} = \\frac{y}{(x^2 + y^2 + z^2)^{3/2}}\n\\]\n\n3. **Partial derivative with respect to \\(z\\):**\n\\[\n\\frac{\\partial \\phi}{\\partial z} = \\frac{\\partial}{\\partial z} \\left( -\\frac{1}{\\sqrt{x^2 + y^2 + z^2}} \\right) = -\\frac{\\partial}{\\partial z} \\left( (x^2 + y^2 + z^2)^{-1/2} \\right)\n\\]\nUsing the chain rule:\n\\[\n\\frac{\\partial}{\\partial z} \\left( (x^2 + y^2 + z^2)^{-1/2} \\right) = -\\frac{1}{2} (x^2 + y^2 + z^2)^{-3/2} \\cdot 2z = -\\frac{z}{(x^2 + y^2 + z^2)^{3/2}}\n\\]\nThus,\n\\[\n\\frac{\\partial \\phi}{\\partial z} = \\frac{z}{(x^2 + y^2 + z^2)^{3/2}}\n\\]\n\nCombining these results, we have:\n\\[\n\\nabla \\phi = \\left( \\frac{x}{(x^2 + y^2 + z^2)^{3/2}}, \\frac{y}{(x^2 + y^2 + z^2)^{3/2}}, \\frac{z}{(x^2 + y^2 + z^2)^{3/2}} \\right) = \\mathbf{u}\n\\]\n\nTherefore, the scalar field \\(\\phi\\) whose gradient is \\(\\mathbf{u} = \\frac{\\mathbf{r}}{|\\mathbf{r}|^3}\\) is:\n\\[\n\\boxed{ -\\dfrac{1}{|\\mathbf{r}|} }\n\\]"}