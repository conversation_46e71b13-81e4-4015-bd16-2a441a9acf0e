{"row_number": 6098, "expected_answer": "15", "problem": "Find the smallest positive integer \\( n \\) such that:\n\\[\n\\begin{cases} \n2^n \\equiv 10 \\pmod{11} \\\\\n3^n \\equiv 1 \\pmod{13}\n\\end{cases}\n\\]", "generated_solution": "<think>\nOkay, so I have this problem where I need to find the smallest positive integer n that satisfies two congruences:\n\n1. 2^n ≡ 10 mod 11\n2. 3^n ≡ 1 mod 13\n\nHmm, okay. Let me break this down. I need to find an n that works for both conditions. That probably means I'll have to solve each congruence separately and then find the least common multiple (LCM) of their solutions, right? Because the smallest n that satisfies both would be the LCM of the two individual periods or something like that. Let's start with the first congruence.\n\nFirst, 2^n ≡ 10 mod 11. Let's see. So 2 modulo 11 is 2, and I need to find the exponent n such that when I raise 2 to that power, I get 10 modulo 11. Let me try calculating powers of 2 modulo 11 until I get 10.\n\n2^1 = 2 mod 11\n2^2 = 4 mod 11\n2^3 = 8 mod 11\n2^4 = 16 ≡ 5 mod 11 (since 16 - 11 = 5)\n2^5 = 2^4 * 2 = 5*2 = 10 mod 11\n\nOh, that's already 10. So for the first equation, n=5 is a solution. Wait, but does this mean the order here is 5? But 2 is a primitive root modulo 11? Wait, let me check the order of 2 modulo 11. The multiplicative order of 2 mod 11 would be the smallest k where 2^k ≡ 1 mod 11. Let me check that. Wait, 2^10 ≡ 1 mod 11 by <PERSON><PERSON><PERSON>'s little theorem, so the order divides 10. Let's see:\n\n2^5 is 10 mod 11, which is not 1. So 2^5 ≡ 10 mod 11\n2^10 would be (2^5)^2 ≡ 10^2 = 100 ≡ 1 mod 11. So the order is 10. So the order is 10. Therefore, the exponents for 2 modulo 11 repeat every 10. But since our first congruence is 2^n ≡ 10 mod 11, which occurred at n=5. Then, the solutions for n would be n ≡ 5 mod 10? Because the period is 10, right? So every 10 steps, it'll cycle. So the solutions are n = 5, 15, 25, etc.\n\nNow, moving on to the second congruence: 3^n ≡ 1 mod 13. So here, we need the order of 3 modulo 13. Again, by Fermat's little theorem, since 13 is prime, 3^12 ≡ 1 mod 13. So the order of 3 divides 12. Let's check the possible orders. The divisors of 12 are 1, 2, 3, 4, 6, 12.\n\nCheck 3^1 ≡ 3 mod 13 ≠1\n3^2 = 9 mod13 ≠1\n3^3 = 27 ≡ 1 mod13? 27-26=1. Wait, 3^3=27≡1 mod13? That's 1. So the order is 3? Wait, 3^3=27 which is 1 mod13. So that's correct. Wait, so the order of 3 modulo 13 is 3. Therefore, the solutions to 3^n ≡1 mod13 are n ≡0 mod3. So the exponents n must be multiples of 3. So the solutions are n=3,6,9,12,15,...\n\nBut wait, let me confirm:\n\n3^1=3 mod13\n3^2=9 mod13\n3^3=27=26+1=1 mod13. Yes, so order is 3. So n must be a multiple of 3. So the minimal solution for the second equation is n=3, then 6,9,12,15,...\n\nTherefore, the first congruence requires n ≡5 mod10, and the second requires n ≡0 mod3. So we need to find the smallest n that satisfies both n ≡5 mod10 and n ≡0 mod3.\n\nSo this is a system of congruences. Let me solve it.\n\nWe need n ≡5 mod10 and n ≡0 mod3.\n\nLet me write n =10k +5 for some integer k≥0. Then, substituting into the second equation: 10k +5 ≡0 mod3.\n\nCompute 10k +5 mod3. 10 ≡1 mod3, so 10k ≡k mod3. Then, k +5 ≡0 mod3. So k ≡-5 mod3. But -5 mod3 is equivalent to 1 mod3, since -5 +6=1. So k ≡1 mod3. Therefore, k=3m +1 for some integer m≥0. Then n=10*(3m +1) +5=30m +10 +5=30m +15. So the solutions are n=15,45,75,... The smallest positive integer is 15.\n\nBut wait, let me check n=15.\n\nCheck the first equation: 2^15 mod11. Since 2^10 ≡1 mod11, so 2^10 *2^5 ≡1*10≡10 mod11. So 2^15 ≡10 mod11. That works.\n\nSecond equation: 3^15 mod13. The order of 3 is 3, so 15 is a multiple of 3, so 3^15 ≡1 mod13. Correct.\n\nTherefore, the smallest positive integer n is 15. So the answer is 15.\n\nWait, but wait, wait. Let me check with lower n. The solutions from the system are 15,45,... but is there a smaller n? Let me check n=15.\n\nBut according to the first equation, n has to be 5 mod10. The possible n's are 5,15,25, etc. For the second equation, n must be 0 mod3. So check if 5 is a multiple of 3? 5/3=1.666, no. Next, 15: 15/3=5. Yes. So 15 is the smallest n that is 5 mod10 and 0 mod3. Therefore, 15 is correct.\n\nTherefore, the answer is 15. So boxed, it's \\boxed{15}.\n\n**Final Answer**\nThe smallest positive integer \\( n \\) is \\boxed{15}.\n</think>To find the smallest positive integer \\( n \\) that satisfies the given congruences:\n\n1. \\( 2^n \\equiv 10 \\pmod{11} \\)\n2. \\( 3^n \\equiv 1 \\pmod{13} \\)\n\n### Step 1: Solve \\( 2^n \\equiv 10 \\pmod{11} \\)\n\nFirst, we calculate the powers of 2 modulo 11 to find the smallest \\( n \\) such that \\( 2^n \\equiv 10 \\pmod{11} \\):\n\n\\[\n\\begin{align*}\n2^1 &\\equiv 2 \\pmod{11}, \\\\\n2^2 &\\equiv 4 \\pmod{11}, \\\\\n2^3 &\\equiv 8 \\pmod{11}, \\\\\n2^4 &\\equiv 16 \\equiv 5 \\pmod{11}, \\\\\n2^5 &\\equiv 2^4 \\cdot 2 \\equiv 5 \\cdot 2 = 10 \\pmod{11}.\n\\end{align*}\n\\]\n\nThus, \\( 2^5 \\equiv 10 \\pmod{11} \\). The order of 2 modulo 11 is 10 (since \\( 2^{10} \\equiv 1 \\pmod{11} \\) by Fermat's Little Theorem), so the solutions for \\( n \\) are \\( n \\equiv 5 \\pmod{10} \\).\n\n### Step 2: Solve \\( 3^n \\equiv 1 \\pmod{13} \\)\n\nNext, we calculate the powers of 3 modulo 13 to find the order of 3 modulo 13:\n\n\\[\n\\begin{align*}\n3^1 &\\equiv 3 \\pmod{13}, \\\\\n3^2 &\\equiv 9 \\pmod{13}, \\\\\n3^3 &\\equiv 27 \\equiv 1 \\pmod{13}.\n\\end{align*}\n\\]\n\nThus, \\( 3^3 \\equiv 1 \\pmod{13} \\). The order of 3 modulo 13 is 3, so the solutions for \\( n \\) are \\( n \\equiv 0 \\pmod{3} \\).\n\n### Step 3: Combine the Solutions\n\nWe need to find the smallest positive integer \\( n \\) that satisfies both:\n\\[\nn \\equiv 5 \\pmod{10} \\quad \\text{and} \\quad n \\equiv 0 \\pmod{3}.\n\\]\n\nExpress \\( n \\) as \\( n = 10k + 5 \\) for some integer \\( k \\). Substituting into the second congruence:\n\\[\n10k + 5 \\equiv 0 \\pmod{3} \\implies k + 5 \\equiv 0 \\pmod{3} \\implies k \\equiv -5 \\equiv 1 \\pmod{3}.\n\\]\n\nThus, \\( k = 3m + 1 \\) for some integer \\( m \\). Substituting back:\n\\[\nn = 10(3m + 1) + 5 = 30m + 15.\n\\]\n\nThe smallest positive integer \\( n \\) is when \\( m = 0 \\), giving \\( n = 15 \\).\n\n### Verification\n\n- For \\( 2^{15} \\equiv 10 \\pmod{11} \\):\n  \\[\n  2^{15} = 2^{10} \\cdot 2^5 \\equiv 1 \\cdot 10 \\equiv 10 \\pmod{11}.\n  \\]\n\n- For \\( 3^{15} \\equiv 1 \\pmod{13} \\):\n  \\[\n  3^{15} = (3^3)^5 \\equiv 1^5 \\equiv 1 \\pmod{13}.\n  \\]\n\nThus, the smallest positive integer \\( n \\) that satisfies both congruences is \\(\\boxed{15}\\)."}