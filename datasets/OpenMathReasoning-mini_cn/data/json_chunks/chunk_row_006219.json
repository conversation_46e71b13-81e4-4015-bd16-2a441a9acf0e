{"row_number": 6219, "expected_answer": "34", "problem": "Find the number of ordered pairs of positive integer solutions $(m, n)$ to the equation $20m + 12n = 2012$.", "generated_solution": "<think>\nOkay, so I need to find the number of ordered pairs of positive integers (m, n) that satisfy the equation 20m + 12n = 2012. Hmm, let me think about how to approach this. \n\nFirst, I remember that equations like this are called linear Diophantine equations, and there's a method to find all integer solutions. But since we need positive integers, I'll have to find solutions where both m and n are greater than zero. \n\nLet me start by simplifying the equation. Maybe I can divide through by a common factor of the coefficients. The coefficients are 20 and 12. The greatest common divisor (GCD) of 20 and 12 is 4. Let me check that. 20 divided by 4 is 5, and 12 divided by 4 is 3. 5 and 3 are coprime, so their GCD is 1. So dividing the entire equation by 4 should simplify things. Let's do that:\n\n20m + 12n = 2012  \nDivide both sides by 4:  \n5m + 3n = 503\n\nOkay, so now the equation is 5m + 3n = 503. This seems simpler. Now, I need to find positive integers m and n that satisfy this equation. \n\nLet me try to express one variable in terms of the other. Let's solve for m first. \n\n5m = 503 - 3n  \nSo, m = (503 - 3n)/5\n\nSince m must be a positive integer, (503 - 3n) must be divisible by 5 and result in a positive integer. So, (503 - 3n) needs to be a multiple of 5, and also (503 - 3n) must be positive. Similarly, solving for n:\n\n3n = 503 - 5m  \nn = (503 - 5m)/3\n\nSimilarly, (503 - 5m) must be divisible by 3 and positive. \n\nAlternatively, since the coefficients are smaller now, maybe we can find the possible values for n such that (503 - 3n) is divisible by 5. \n\nAnother approach is to find particular solutions and then describe the general solution. For linear Diophantine equations, once a particular solution is found, the general solution can be written in terms of the coefficients. \n\nLet me recall the theory. For an equation ax + by = c, if gcd(a, b) divides c, then there are integer solutions. Here, after dividing by 4, the equation is 5m + 3n = 503. The GCD of 5 and 3 is 1, which divides 503, so solutions exist. \n\nThe general solution can be written as:\n\nm = m0 + 3t  \nn = n0 - 5t  \n\nwhere t is an integer, and (m0, n0) is a particular solution. \n\nSo, first, I need to find a particular solution (m0, n0). Let me try to find such a solution. \n\nWe can use the extended Euclidean algorithm to find integers x and y such that 5x + 3y = 1, then scale it up to 503. \n\nBut maybe in this case, it's easier to find a particular solution by trial. \n\nLet me consider the equation 5m + 3n = 503. Let's fix m to some value and see if n is integer. For example, let's try m = 1: 5(1) + 3n = 503 => 3n = 498 => n = 166. So that works! So m=1, n=166 is a solution. Wait, is that correct? 5*1 + 3*166 = 5 + 498 = 503. Yes, correct. \n\nSo, a particular solution is m0=1, n0=166. \n\nThen, the general solution would be:\n\nm = 1 + 3t  \nn = 166 - 5t  \n\nBut since m and n have to be positive integers, we need to find the values of t such that both m and n are positive. \n\nSo:\n\nFor m > 0: 1 + 3t > 0 => 3t > -1 => t >= 0 (since t must be integer). \n\nFor n > 0: 166 - 5t > 0 => 5t < 166 => t < 166/5 = 33.2. So t <= 33. \n\nBut t must be an integer. So t can take integer values from 0 up to 33, inclusive? Wait, 33*5 = 165, so 166 - 5*33 = 166 - 165 = 1 > 0. But t=33 gives n=1. t=34 would give n=166 - 5*34 = 166 - 170 = -4, which is negative. So yes, t must be in the range 0 <= t <= 33. \n\nBut let's check if when t=33, m=1 + 3*33 = 1 + 99 = 100. Then, n=166 - 165=1. So (100, 1) is a solution, which is valid. \n\nSo t can range from 0 to 33. How many integers is that? From t=0 to t=33 inclusive, that's 34 values. \n\nWait, but let me verify if all these t values result in m and n being positive. For t=0: m=1, n=166, which is valid. For t=1: m=4, n=161. Still positive. For t=2: m=7, n=156. Hmm, seems okay. And so on up to t=33: m=100, n=1. All these should be positive. \n\nTherefore, the number of solutions is 34. But let me check if there's another way. \n\nAlternatively, since the coefficients after division are 5 and 3, and the equation is 5m + 3n = 503, the solutions are spaced by increments of 3 in m and decrements of 5 in n. So, starting from (1,166), the next solution is (4,161), then (7,156), etc. Each time, m increases by 3 and n decreases by 5. So how many such steps can we take before n becomes zero or negative?\n\nStarting from n=166, each step subtracts 5. So the maximum number of steps is the floor of 166/5, which is 33.2, so 33 steps. But since we start at t=0 (which is the original solution), the number of solutions is 33 +1 =34. \n\nThat seems to confirm the previous result. But let me check with another approach. \n\nSuppose we solve for m:\n\nm = (503 -3n)/5. \n\nFor m to be a positive integer, 503 -3n must be divisible by 5, so 3n ≡503 mod5. Let's compute 503 mod5. 500 is divisible by 5, so 503 mod5 is 3. Therefore, 3n ≡3 mod5. \n\nDivide both sides by 3 (since 3 and 5 are coprime, we can multiply by the inverse of 3 mod5). The inverse of 3 mod5 is 2, since 3*2=6≡1 mod5. Therefore, multiplying both sides by 2: \n\nn ≡3*2=6≡1 mod5. \n\nTherefore, n ≡1 mod5. So n can be written as n=5k +1, where k is a non-negative integer. \n\nNow, substituting back into the equation: n=5k +1, so:\n\n5m + 3*(5k +1) =503  \n5m +15k +3 =503  \n5m =503 -15k -3  \n5m =500 -15k  \nm = (500 -15k)/5  \nm =100 -3k  \n\nSo, m=100 -3k. \n\nSince m must be positive, 100 -3k >0  \n=> 3k <100  \n=> k <100/3 ≈33.333...\n\nSince k must be a non-negative integer, k can be 0,1,2,...,33 (since 33*3=99 <100). Thus, k can take values from 0 to 33 inclusive, which is 34 values. \n\nTherefore, the number of solutions is 34. \n\nWait, so both methods give 34 solutions. Let me check with n=1. If n=1, then m=(503 -3*1)/5=(500)/5=100, which is valid. If k=33, n=5*33 +1=165 +1=166, which is the same as the first solution. Wait, no, k=0 gives n=1, and k=33 gives n=5*33 +1=166? Wait, no. Wait, n=5k +1. So when k=0: n=1, m=100 -0=100. Wait, but previously when we found t=33, m=100, n=1. So maybe there's a confusion here. \n\nWait, in the first method, t ranges from 0 to33, giving m from1 to100 and n from166 to1. In the second method, k ranges from0 to33, giving n from1 to166 and m from100 to1. So both approaches are consistent. So regardless, the number of solutions is34. \n\nBut let me verify with another example. Let's take k=1. Then n=5*1 +1=6. Then m=100 -3*1=97. So 5*97 +3*6=485 +18=503. Correct. \n\nSimilarly, k=2: n=11, m=94. 5*94=470, 3*11=33. 470 +33=503. Correct. \n\nSo all these check out. So the number of solutions is indeed34. \n\nAlternatively, let's check possible m values. Since m =100 -3k, where k is from0 to33. Thus, m starts at100 and decreases by3 each time. To have m positive, m=100 -3k >0 =>k <100/3≈33.333. So maximum k=33, which gives m=100 -99=1. So m can take values100,97,...,1. So how many terms in this arithmetic sequence starting at100, ending at1, with common difference -3. \n\nThe number of terms can be calculated as: ((last term - first term)/common difference)+1. But since it's decreasing, ((100 -1)/3)+1= (99/3)+1=33+1=34. So 34 terms. \n\nTherefore, all methods confirm that the number of ordered pairs is34. \n\nBut to be thorough, let me check the initial equation again. The problem says \"positive integer solutions\". So both m and n have to be greater than0. So for each solution found, m and n are positive. For example, when k=0, n=1, m=100. Both positive. When k=33, n=166, m=1. Both positive. So all 34 solutions are valid. \n\nTherefore, the answer is34. \n\nBut wait, the original equation was20m +12n=2012, which we reduced by dividing by4 to get5m +3n=503. So in that case, all solutions to the reduced equation correspond to solutions of the original equation, right? Because if m and n are integers for the reduced equation, multiplying them by1 (since we divided by4) would still keep them as integers. So yes, the solutions are preserved. So 34 is indeed the answer. \n\nI think that's all. I can't see any mistakes here. The key steps were simplifying the equation by dividing by GCD, finding a particular solution, then using the general solution to determine the number of valid positive integer solutions. Both methods (expressing in terms of t or k) gave the same result, and checking specific examples worked. So I'm confident the answer is34.\n</think>To find the number of ordered pairs of positive integer solutions \\((m, n)\\) to the equation \\(20m + 12n = 2012\\), we can follow these steps:\n\n1. **Simplify the Equation:**\n   The equation \\(20m + 12n = 2012\\) can be simplified by dividing through by the greatest common divisor (GCD) of 20 and 12, which is 4:\n   \\[\n   5m + 3n = 503\n   \\]\n\n2. **Find a Particular Solution:**\n   We need to find a particular solution \\((m_0, n_0)\\) to the equation \\(5m + 3n = 503\\). By trial, we find:\n   \\[\n   m = 1 \\implies 5(1) + 3n = 503 \\implies 3n = 498 \\implies n = 166\n   \\]\n   So, a particular solution is \\((m_0, n_0) = (1, 166)\\).\n\n3. **General Solution:**\n   The general solution to the linear Diophantine equation \\(5m + 3n = 503\\) can be written as:\n   \\[\n   m = 1 + 3t \\quad \\text{and} \\quad n = 166 - 5t\n   \\]\n   where \\(t\\) is an integer.\n\n4. **Ensure Positive Integer Solutions:**\n   Both \\(m\\) and \\(n\\) must be positive integers:\n   - For \\(m > 0\\):\n     \\[\n     1 + 3t > 0 \\implies t \\geq 0\n     \\]\n   - For \\(n > 0\\):\n     \\[\n     166 - 5t > 0 \\implies 5t < 166 \\implies t < \\frac{166}{5} = 33.2\n     \\]\n     Since \\(t\\) must be an integer, \\(t \\leq 33\\).\n\n   Therefore, \\(t\\) ranges from 0 to 33 inclusive, which gives 34 possible integer values for \\(t\\).\n\n5. **Conclusion:**\n   The number of ordered pairs \\((m, n)\\) that satisfy the equation \\(20m + 12n = 2012\\) and are positive integers is:\n   \\[\n   \\boxed{34}\n   \\]"}