{"row_number": 6473, "expected_answer": "\\(k!\\)", "problem": "Find \\(\\lim_{n \\to \\infty}\\frac{n^{k}}{\\binom{n}{k}}\\) where \\(k \\in \\mathbf{N}\\).", "generated_solution": "<think>\nOkay, let's see. I need to find the limit as n approaches infinity of n^k divided by the binomial coefficient \"n choose k\". Hmm. Alright, first, I remember that the binomial coefficient is given by the formula:\n\n\\[\n\\binom{n}{k} = \\frac{n!}{k!(n - k)!}\n\\]\n\nSo the expression we're looking at is:\n\n\\[\n\\frac{n^k}{\\frac{n!}{k!(n - k)!}} = \\frac{n^k \\cdot k! \\cdot (n - k)!}{n!}\n\\]\n\nRight. Now, I need to simplify this expression and see what happens as n becomes very large. Since k is a natural number and fixed, while n is approaching infinity, maybe I can find a way to approximate the factorials for large n?\n\nI remember that <PERSON>'s approximation might be useful here. <PERSON>'s formula approximates n! for large n as:\n\n\\[\nn! \\approx \\sqrt{2\\pi n} \\left( \\frac{n}{e} \\right)^n\n\\]\n\nBut maybe using <PERSON>'s formula here would complicate things. Let me see if there's another way. Let's write out the factorial terms in the binomial coefficient.\n\nThe binomial coefficient is:\n\n\\[\n\\binom{n}{k} = \\frac{n(n - 1)(n - 2) \\cdots (n - k + 1)}{k!}\n\\]\n\nBecause if you expand n! / (n - k)! you get the product of n down to n - k + 1. So substituting this back into the original expression:\n\n\\[\n\\frac{n^k}{ \\frac{n(n - 1)(n - 2) \\cdots (n - k + 1)}{k!} } = \\frac{n^k \\cdot k!}{n(n - 1)(n - 2) \\cdots (n - k + 1)}\n\\]\n\nSo that simplifies to:\n\n\\[\nk! \\cdot \\frac{n^k}{n(n - 1)(n - 2) \\cdots (n - k + 1)}\n\\]\n\nNow, let's focus on the fraction part:\n\n\\[\n\\frac{n^k}{n(n - 1)(n - 2) \\cdots (n - k + 1)}\n\\]\n\nIf I factor out n from each term in the denominator:\n\nThe denominator is n multiplied by (n - 1) multiplied by ... multiplied by (n - k + 1). So each term is n - i where i goes from 0 to k - 1. So the denominator can be written as:\n\n\\[\nn \\cdot (n - 1) \\cdot (n - 2) \\cdots (n - k + 1) = n^k \\prod_{i=1}^{k-1} \\left(1 - \\frac{i}{n}\\right)\n\\]\n\nWait, actually, let's check that. Let me see:\n\nIf I factor out n from each term, the first term is n, the second term is n(1 - 1/n), the third term is n(1 - 2/n), and so on, up to the k-th term which is n(1 - (k - 1)/n). Therefore, the entire product is:\n\n\\[\nn^k \\prod_{i=0}^{k-1} \\left(1 - \\frac{i}{n}\\right)\n\\]\n\nBut when i=0, it's (1 - 0/n) = 1. So actually, the product is from i=1 to i=k-1. Let me correct that:\n\n\\[\nn \\cdot (n - 1) \\cdot (n - 2) \\cdots (n - k + 1) = n^k \\prod_{i=1}^{k-1} \\left(1 - \\frac{i}{n}\\right)\n\\]\n\nWait, no. Let's see:\n\nFirst term: n = n\n\nSecond term: n - 1 = n(1 - 1/n)\n\nThird term: n - 2 = n(1 - 2/n)\n\n...\n\nk-th term: n - (k - 1) = n(1 - (k - 1)/n)\n\nSo multiplying all these together:\n\nn * n(1 - 1/n) * n(1 - 2/n) * ... * n(1 - (k -1)/n) = n^k * product_{i=0}^{k-1} (1 - i/n). But wait, when i=0, it's 1 - 0/n = 1. So the product from i=0 to k-1 is equal to 1 * (1 - 1/n) * (1 - 2/n) * ... * (1 - (k -1)/n). So the denominator is n^k multiplied by that product.\n\nTherefore, the denominator is:\n\nn^k * ∏_{i=0}^{k-1} (1 - i/n)\n\nBut the numerator in the fraction is n^k. So when we take the ratio n^k / denominator:\n\nn^k / [n^k * ∏_{i=0}^{k-1} (1 - i/n)] = 1 / ∏_{i=0}^{k-1} (1 - i/n)\n\nTherefore, going back to the original expression:\n\nk! * [1 / ∏_{i=0}^{k-1} (1 - i/n)]\n\nSo the entire limit becomes:\n\nlim_{n→∞} [k! / ∏_{i=0}^{k-1} (1 - i/n)]\n\nNow, as n approaches infinity, each term (1 - i/n) approaches 1, but since we're taking the product of k terms, each approaching 1, but each i is up to k -1, which is fixed. Wait, but n is going to infinity, so i/n becomes 0. So each term in the product is approaching 1. Therefore, the entire product ∏_{i=0}^{k-1} (1 - i/n) approaches 1 * 1 * ... * 1 = 1. Therefore, the limit is k! / 1 = k!.\n\nWait, but that can't be right. Because if we take the limit as n approaches infinity of n^k / binom(n,k) equals k!? Let me check with a small k. Let's take k = 1. Then binom(n,1) is n. So the expression becomes n^1 / n = 1, and k! is 1!, which is 1. So that works.\n\nTake k=2. Then binom(n,2) is n(n -1)/2. So the expression is n^2 / [n(n -1)/2] = 2n / (n -1). As n approaches infinity, that's 2n / n = 2, which is 2! = 2. That works.\n\nSimilarly, for k=3. binom(n,3) = n(n -1)(n -2)/6. The expression is n^3 / [n(n -1)(n -2)/6] = 6n^3 / [n(n -1)(n -2)] = 6n^3 / [n^3 -3n^2 + 2n] ≈ 6n^3 / n^3 = 6 as n approaches infinity, which is 3! = 6. Yep, that's correct.\n\nSo the pattern seems to hold. So the limit is k!.\n\nTherefore, the answer is \\boxed{k!}\n\nBut wait, let me just make sure I didn't make any mistake in the derivation. Let's see:\n\nOriginal expression: n^k / binom(n,k) = n^k * k! / [n(n -1)...(n -k +1)]\n\nWhich is k! * n^k / [n^k * product_{i=0}^{k-1} (1 - i/n)] = k! / product_{i=0}^{k-1} (1 - i/n)\n\nThen as n→infty, each (1 - i/n) → 1, so the product tends to 1. Therefore, the limit is k!.\n\nYes, that makes sense. So the answer is k!.\n\n**Final Answer**\n\\boxed{k!}\n</think>To find the limit \\(\\lim_{n \\to \\infty} \\frac{n^k}{\\binom{n}{k}}\\) where \\(k \\in \\mathbf{N}\\), we start by expressing the binomial coefficient \\(\\binom{n}{k}\\) as:\n\n\\[\n\\binom{n}{k} = \\frac{n!}{k!(n - k)!}\n\\]\n\nRewriting the given expression, we have:\n\n\\[\n\\frac{n^k}{\\binom{n}{k}} = \\frac{n^k \\cdot k! \\cdot (n - k)!}{n!}\n\\]\n\nUsing the expansion of the binomial coefficient, we can write:\n\n\\[\n\\binom{n}{k} = \\frac{n(n - 1)(n - 2) \\cdots (n - k + 1)}{k!}\n\\]\n\nSubstituting this back into the original expression gives:\n\n\\[\n\\frac{n^k}{\\frac{n(n - 1)(n - 2) \\cdots (n - k + 1)}{k!}} = \\frac{n^k \\cdot k!}{n(n - 1)(n - 2) \\cdots (n - k + 1)}\n\\]\n\nFactoring out \\(n\\) from each term in the denominator, we get:\n\n\\[\nn(n - 1)(n - 2) \\cdots (n - k + 1) = n^k \\left(1 \\cdot \\left(1 - \\frac{1}{n}\\right) \\left(1 - \\frac{2}{n}\\right) \\cdots \\left(1 - \\frac{k-1}{n}\\right)\\right)\n\\]\n\nThus, the expression simplifies to:\n\n\\[\n\\frac{n^k \\cdot k!}{n^k \\left(1 \\cdot \\left(1 - \\frac{1}{n}\\right) \\left(1 - \\frac{2}{n}\\right) \\cdots \\left(1 - \\frac{k-1}{n}\\right)\\right)} = k! \\cdot \\frac{1}{\\left(1 \\cdot \\left(1 - \\frac{1}{n}\\right) \\left(1 - \\frac{2}{n}\\right) \\cdots \\left(1 - \\frac{k-1}{n}\\right)\\right)}\n\\]\n\nAs \\(n\\) approaches infinity, each term \\(\\left(1 - \\frac{i}{n}\\right)\\) approaches 1. Therefore, the product \\(\\left(1 \\cdot \\left(1 - \\frac{1}{n}\\right) \\left(1 - \\frac{2}{n}\\right) \\cdots \\left(1 - \\frac{k-1}{n}\\right)\\right)\\) approaches 1. Hence, the limit is:\n\n\\[\n\\lim_{n \\to \\infty} k! \\cdot \\frac{1}{\\left(1 \\cdot \\left(1 - \\frac{1}{n}\\right) \\left(1 - \\frac{2}{n}\\right) \\cdots \\left(1 - \\frac{k-1}{n}\\right)\\right)} = k!\n\\]\n\nThus, the final answer is:\n\n\\[\n\\boxed{k!}\n\\]"}