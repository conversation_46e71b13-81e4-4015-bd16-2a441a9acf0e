{"row_number": 6362, "expected_answer": "1001", "problem": "Find the largest positive integer \\( n \\) such that \\( n+10 \\) is a divisor of \\( n^3 + 2011 \\).", "generated_solution": "<think>\nOkay, let's see. I need to find the largest positive integer n such that n + 10 divides n³ + 2011. Hmm. Divisors and polynomials... Maybe I should use polynomial division or the Remainder Theorem. Let me start by recalling that if n + 10 is a divisor of n³ + 2011, then when we divide n³ + 2011 by n + 10, the remainder should be zero. \n\nThe Remainder Theorem says that if a polynomial f(n) is divided by n - a, the remainder is f(a). But here, the divisor is n + 10, which is the same as n - (-10). So applying the Remainder Theorem, the remainder when dividing by n + 10 is f(-10). Let me compute that. If f(n) = n³ + 2011, then f(-10) would be (-10)³ + 2011 = -1000 + 2011 = 1011. So the remainder is 1011. Wait, but for n + 10 to divide n³ + 2011, the remainder must be zero. So that implies that 1011 must be congruent to 0 mod n + 10. In other words, n + 10 divides 1011. So n + 10 is a divisor of 1011. Therefore, n + 10 must be a positive divisor of 1011, and since we want the largest positive integer n, we need the largest divisor of 1011. But the divisors of 1011 come in pairs. Let me factorize 1011 to find its divisors.\n\nFirst, check if 1011 is prime. Let's divide by small primes. 2: 1011 is odd. 3: 1 + 0 + 1 + 1 = 3, which is divisible by 3. So 1011 ÷ 3 is 337. So 1011 = 3 × 337. Now check if 337 is prime. Let's see: sqrt(337) is about 18.35. So check primes up to 17. 337 ÷ 2 = nope. 337 ÷ 3: 3 × 112 = 336, so 337 is not divisible by 3. 5: doesn't end with 0 or 5. 7: 7 × 48 = 336, so 337 - 336 = 1, not divisible by 7. 11: 11 × 30 = 330, 337 - 330 = 7, not divisible by 11. 13: 13 × 25 = 325, 337 - 325 = 12, not divisible. 17: 17 × 19 = 323, 337 - 323 = 14, not divisible. So 337 is prime. Therefore, the prime factorization of 1011 is 3 × 337. \n\nTherefore, the positive divisors of 1011 are 1, 3, 337, and 1011. Since n + 10 must be one of these divisors, and n is a positive integer, then n + 10 can be 1, 3, 337, or 1011. Therefore, the corresponding n would be:\n\n- If n + 10 = 1, then n = -9 (but we need positive integer, so discard)\n- If n + 10 = 3, then n = -7 (discard)\n- If n + 10 = 337, then n = 337 - 10 = 327\n- If n + 10 = 1011, then n = 1011 - 10 = 1001\n\nWait, but 1001 is larger than 327. But we need to check if these n values actually satisfy the original condition. Because even though n + 10 divides 1011, which we derived from the remainder theorem, we need to confirm that n³ + 2011 is indeed divisible by n + 10.\n\nLet me check n = 1001. Compute n³ + 2011. That's 1001³ + 2011. Instead of computing the entire number, which is huge, maybe we can use modular arithmetic. Since n + 10 = 1011, then n ≡ -10 mod 1011. So n³ + 2011 ≡ (-10)^3 + 2011 = -1000 + 2011 = 1011 ≡ 0 mod 1011. So yes, that works. \n\nNow check n = 327. Then n + 10 = 337. Check if 327³ + 2011 is divisible by 337. Similarly, since 327 ≡ -10 mod 337. So let's compute (-10)^3 + 2011 mod 337. That's -1000 + 2011 = 1011. Now compute 1011 mod 337. 337 × 3 = 1011. So 1011 mod 337 is 0. Therefore, 327³ + 2011 is divisible by 337. So that works too.\n\nBut n = 1001 is larger, so the answer should be 1001. Wait, but I need to confirm that n is positive, which it is, so 1001 is the largest possible. Therefore, the largest positive integer n is 1001.\n\nBut just to be thorough, maybe I should check if there are any other divisors of 1011 that I missed. But since 1011 is 3 × 337, and both 3 and 337 are primes, the only positive divisors are 1, 3, 337, and 1011. So no, we didn't miss any. So the possible n are 1001 and 327, and since 1001 is larger, that's the answer.\n\n**Final Answer**\nThe largest positive integer \\( n \\) is \\boxed{1001}.\n</think>To find the largest positive integer \\( n \\) such that \\( n + 10 \\) is a divisor of \\( n^3 + 2011 \\), we use the Remainder Theorem. According to the Remainder Theorem, if \\( n + 10 \\) divides \\( n^3 + 2011 \\), then the remainder when \\( n^3 + 2011 \\) is divided by \\( n + 10 \\) must be zero. This means that evaluating the polynomial \\( f(n) = n^3 + 2011 \\) at \\( n = -10 \\) should give zero:\n\n\\[\nf(-10) = (-10)^3 + 2011 = -1000 + 2011 = 1011\n\\]\n\nThus, \\( n + 10 \\) must be a divisor of 1011. The prime factorization of 1011 is \\( 3 \\times 337 \\), so the positive divisors of 1011 are 1, 3, 337, and 1011. We now find the corresponding values of \\( n \\):\n\n- If \\( n + 10 = 1 \\), then \\( n = -9 \\) (not a positive integer).\n- If \\( n + 10 = 3 \\), then \\( n = -7 \\) (not a positive integer).\n- If \\( n + 10 = 337 \\), then \\( n = 337 - 10 = 327 \\).\n- If \\( n + 10 = 1011 \\), then \\( n = 1011 - 10 = 1001 \\).\n\nWe verify both \\( n = 327 \\) and \\( n = 1001 \\):\n\n- For \\( n = 1001 \\), we check if \\( 1001^3 + 2011 \\) is divisible by 1011. Since \\( 1001 \\equiv -10 \\pmod{1011} \\), we have:\n  \\[\n  (-10)^3 + 2011 = -1000 + 2011 = 1011 \\equiv 0 \\pmod{1011}\n  \\]\n  Thus, \\( 1011 \\) divides \\( 1001^3 + 2011 \\).\n\n- For \\( n = 327 \\), we check if \\( 327^3 + 2011 \\) is divisible by 337. Since \\( 327 \\equiv -10 \\pmod{337} \\), we have:\n  \\[\n  (-10)^3 + 2011 = -1000 + 2011 = 1011 \\equiv 0 \\pmod{337}\n  \\]\n  Thus, \\( 337 \\) divides \\( 327^3 + 2011 \\).\n\nSince \\( n = 1001 \\) is the largest value that satisfies the condition, the largest positive integer \\( n \\) is:\n\n\\[\n\\boxed{1001}\n\\]"}