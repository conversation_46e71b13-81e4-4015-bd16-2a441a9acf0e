{"row_number": 19100, "expected_answer": "\\( g(z) = 2(z - \\frac{3}{2})^{1/2} \\cdot (z + \\frac{3}{2})^{1/2} \\)", "problem": "Find a function \\( g \\) with the properties:\n- \\( g \\) is analytic on \\( D = \\{ z \\in \\mathbb{C} : |z| > 2 \\} \\).\n- \\( g(z)^2 = 4z^2 - 9 \\) for all \\( z \\in D \\).", "generated_solution": "<think>\nOkay, so I need to find a function g that's analytic on the domain D, which is all complex numbers z with absolute value greater than 2. And this function g has to satisfy g(z) squared equals 4z² minus 9 for every z in D. Hmm, let's start breaking this down.\n\nFirst, I know that square roots of holomorphic functions can sometimes be defined if we're on a simply connected domain and the function doesn't vanish there. But here, the domain D is the exterior of the disk with radius 2, which is actually not simply connected—it's a punctured plane at infinity. Wait, is that right? The set |z| > 2 is an open annulus extending to infinity. Wait, but in complex analysis, the exterior of a disk is actually simply connected. Because any loop in |z| > 2 can be contracted to a point, right? For example, if you have a loop out there, you can just shrink it to a very large circle and then to a point at infinity. So maybe D is simply connected? Hmm. Wait, no. Actually, in the complex plane, the complement of a disk is not simply connected because, for example, a loop that goes around the disk once can't be contracted to a point without passing through the disk, which is not in our domain. So D is not simply connected. So that complicates things because if the domain isn't simply connected, then even if the function 4z² - 9 doesn't vanish there, we might not have a global analytic square root. Wait, but maybe 4z² - 9 has no zeros in D? Let's check that.\n\nThe zeros of 4z² - 9 are z = ±3/2. Since 3/2 is 1.5, which is less than 2, so those zeros are inside the disk |z| < 2. So in the domain D, which is |z| > 2, the function 4z² - 9 doesn't vanish. So even though D is not simply connected, maybe we can still define an analytic square root of 4z² - 9 on D. Because if there are no zeros, perhaps the monodromy is trivial? Wait, but the problem states that g is analytic on D. So maybe we can construct such a function by choosing a branch.\n\nAlternatively, maybe we can express 4z² - 9 as something squared. Let me factor it. 4z² - 9 is (2z - 3)(2z + 3). So, maybe I can write this as 2z times (1 - 3/(2z)) times 2z times (1 + 3/(2z))? Wait, no. Wait, 4z² - 9 = (2z)^2 - 3^2 = (2z - 3)(2z + 3). So that's the factorization. Now, if we're in the domain |z| > 2, then 3/(2z) will have absolute value less than 3/(2*2) = 3/4 < 1. So maybe we can take the square root by expanding each factor as a product involving square roots of (1 ± 3/(2z)).\n\nBut wait, maybe a better approach is to factor 4z² - 9 as (2z - 3)(2z + 3), and then take the square root as a product of square roots of each factor. But square roots of (2z - 3) and (2z + 3). But since both 2z - 3 and 2z + 3 are linear functions, their square roots would have branch points at z = 3/2 and z = -3/2. But these points are inside |z| < 2, so in our domain D, which is |z| > 2, we can define analytic branches of sqrt(2z - 3) and sqrt(2z + 3).\n\nAlternatively, maybe we can write sqrt(4z² - 9) as 2z * sqrt(1 - 9/(4z²)). Since |z| > 2, then 9/(4z²) has absolute value less than 9/(4*4) = 9/16 < 1. So we can use the binomial expansion for sqrt(1 - w) where |w| < 1. Then, maybe express sqrt(4z² - 9) as 2z * sqrt(1 - 9/(4z²)) and then expand the square root as a power series in 1/z. But since the problem says \"find a function g analytic on D\", we might need an explicit expression, not necessarily a series.\n\nAlternatively, perhaps we can define g(z) as 2z multiplied by the square root of (1 - 9/(4z²)), choosing a branch such that the square root is analytic in |z| > 2. But how do we choose the branch?\n\nLet me consider the function h(z) = sqrt(1 - 9/(4z²)). To define h(z) analytically in |z| > 2, we need to ensure that 1 - 9/(4z²) doesn't cross the negative real axis, which is the usual branch cut for the square root. So, 1 - 9/(4z²) must not be a negative real number or zero. Let me see when 1 - 9/(4z²) is a negative real number.\n\nLet’s set 1 - 9/(4z²) = -t where t ≥ 0. Then 9/(4z²) = 1 + t. So z² = 9/(4(1 + t)), so z is a complex number whose square is 9/(4(1 + t)). So z can be any complex number such that z = ± 3/(2sqrt(1 + t)). But sqrt(1 + t) is a positive real number, so z would be real numbers with absolute value 3/(2sqrt(1 + t)) ≤ 3/2, since t ≥ 0. Therefore, the set of z where 1 - 9/(4z²) is a negative real number or zero is the real interval between -3/2 and 3/2. But our domain is |z| > 2, which doesn't include those points. Therefore, in the domain |z| > 2, 1 - 9/(4z²) is not a negative real number. Therefore, we can define the principal branch of the square root here. That is, we can take sqrt(1 - 9/(4z²)) using the principal branch, which is analytic in the domain where 1 - 9/(4z²) is not a negative real number or zero. Since in our domain, 1 - 9/(4z²) is either in the right half-plane or not on the negative real axis, so this should work.\n\nTherefore, the function g(z) = 2z * sqrt(1 - 9/(4z²)) is analytic in |z| > 2. Then, squaring this, we get (2z)^2 * (1 - 9/(4z²)) = 4z² * (1 - 9/(4z²)) = 4z² - 9, as required.\n\nBut wait, let me check if sqrt(1 - 9/(4z²)) is analytic. Since 1 - 9/(4z²) is analytic in |z| > 2, and as we saw, the expression inside the square root doesn't cross the negative real axis in that domain, so the principal branch of the square root is analytic here. Therefore, the composition is analytic.\n\nAlternatively, another approach is to factor 4z² - 9 as (2z - 3)(2z + 3), and then take sqrt((2z - 3)(2z + 3)). But to define the square root of a product as the product of the square roots, we need to be careful with the branch cuts. However, both 2z - 3 and 2z + 3 have zeros at z = 3/2 and z = -3/2, which are inside |z| < 2. Therefore, in the domain |z| > 2, both factors are non-zero, and their arguments can be defined continuously. So perhaps we can write sqrt((2z - 3)(2z + 3)) as sqrt(2z - 3) * sqrt(2z + 3), choosing branches of the square roots such that their product is analytic in D.\n\nBut maybe that's more complicated than the previous approach. The first method, expressing it as 2z*sqrt(1 - 9/(4z²)), seems simpler. Let me verify if this works. Let’s check for large z. As z approaches infinity, 9/(4z²) approaches 0, so sqrt(1 - 9/(4z²)) ≈ 1 - (9)/(8z²) - ... by the binomial expansion. Therefore, g(z) ≈ 2z*(1 - 9/(8z²)) = 2z - 9/(4z), which is analytic at infinity. So that's good.\n\nAlternatively, if we define g(z) as 2z times sqrt(1 - 9/(4z²)), where the square root is the principal branch, then this should be analytic in |z| > 2. Let me check if there are any branch cuts in this domain. The principal branch of sqrt(w) has a branch cut along the negative real axis. So we need 1 - 9/(4z²) not to be a negative real number. As we saw earlier, this occurs when z is real between -3/2 and 3/2, but in our domain |z| > 2, these points are excluded. Therefore, the function 1 - 9/(4z²) is either in the right half-plane or not crossing the negative real axis. So the principal branch is analytic here.\n\nTherefore, the function g(z) = 2z * sqrt(1 - 9/(4z²)) is analytic in D and satisfies g(z)^2 = 4z² - 9.\n\nAlternatively, another approach could be to take g(z) = sqrt(4z² - 9) where the branch of the square root is chosen such that it's analytic in |z| > 2. But then, how to specify the branch? But since we already did that by expressing it in terms of the principal branch, maybe that's the same thing.\n\nWait, but sometimes when you have sqrt(4z² - 9), the standard branch cuts would be where 4z² - 9 is a negative real number. Let's check where that is. 4z² - 9 ≤ 0 implies z² ≤ 9/4, so z is in the real interval between -3/2 and 3/2. So the branch cut is that interval. But in our domain |z| > 2, which is outside the disk of radius 2, this branch cut is entirely inside the disk. Therefore, in D, the function sqrt(4z² - 9) with the principal branch is analytic. Wait, that's a good point. So maybe the function g(z) can be directly defined as sqrt(4z² - 9), using the principal branch of the square root, and since the branch cut is inside |z| ≤ 3/2 < 2, which is excluded from D, then this function is analytic on D. Then, of course, (sqrt(4z² - 9))² = 4z² - 9, as needed.\n\nBut wait, the principal branch of sqrt(w) is defined as the one with a branch cut along the negative real axis, and returning the positive square root for positive real w. So if we take sqrt(4z² - 9), then the branch cuts occur when 4z² - 9 is a negative real number. Which, as before, is when z is real between -3/2 and 3/2. Therefore, outside of that interval, in the complex plane, we can define sqrt(4z² - 9) as an analytic function. But our domain is |z| > 2, which includes complex numbers not on that interval. Therefore, sqrt(4z² - 9) with the principal branch is analytic on D.\n\nTherefore, another possible answer is g(z) = sqrt(4z² - 9), with the principal branch. Then, this would be analytic on D. But wait, is this the same as the previous expression? Let me see. If we take sqrt(4z² - 9), then 4z² - 9 = 4z²(1 - 9/(4z²)), so sqrt(4z² - 9) = sqrt(4z²(1 - 9/(4z²))) = 2z sqrt(1 - 9/(4z²)). So yes, they are the same. Therefore, the function can be written either way. But since z is in |z| > 2, 1 - 9/(4z²) is not on the negative real axis, so the sqrt is defined via the principal branch. Therefore, both expressions are equivalent.\n\nTherefore, the answer is g(z) = 2z * sqrt(1 - 9/(4z²)) or equivalently sqrt(4z² - 9), with the principal branch. But to write it in a simplified form, perhaps 2z times the square root term is more explicit, but sqrt(4z² - 9) is simpler. However, since the problem states that g is analytic on D, we need to ensure that the square root is defined appropriately. But given that in D, the expression under the sqrt doesn't cross the branch cut, and the principal branch suffices.\n\nAlternatively, another way to write this is to note that sqrt(4z² -9) = sqrt((2z - 3)(2z + 3)). Then, choosing branches for each sqrt. But in |z| > 2, both 2z - 3 and 2z + 3 are non-zero and don't cross the negative real axis if we choose appropriate branches. For example, if we take sqrt(2z - 3) as the branch with a cut along the line from 3/2 to infinity along the real axis, but since 2z - 3 in |z| > 2 would not cross the negative real axis unless z is on some other line. Wait, maybe this is complicating things. The key point is that both 2z - 3 and 2z + 3, when |z| > 2, their arguments can be defined in such a way that their product's square root is analytic. But perhaps it's more straightforward to use the principal branch directly on the entire expression.\n\nTherefore, the conclusion is that g(z) = sqrt(4z² - 9), with the square root defined via the principal branch, is analytic on D and satisfies the required equation.\n\nAlternatively, to make it more explicit, maybe writing it in terms of z, but in the end, sqrt(4z² - 9) is acceptable. Let me verify with an example. Take z approaching infinity along the positive real axis. Then sqrt(4z² -9) ~ 2z - 9/(4z), which is analytic. Similarly, for z approaching infinity in other directions, the principal branch should give the correct behavior. Since the branch cut is along the real axis from -3/2 to 3/2, which is inside |z| < 2, in our domain |z| > 2, the function is analytic.\n\nTherefore, the answer is g(z) = sqrt(4z² - 9), with the principal branch. But to express it without any reference to branches, maybe they want it written in another form? Alternatively, perhaps the problem expects an expression in terms of z, but we can just state it as sqrt(4z² - 9).\n\nAlternatively, since 4z² -9 can be written as (2z)^2 - 3^2, which is a difference of squares, but that's already factored. Wait, maybe there's a way to write the square root as 2z times sqrt(1 - (3/(2z))²), which is similar to before. So that's again 2z*sqrt(1 - 9/(4z²)). So this is also a valid expression, and when expanded for large z, it approximates 2z - 9/(4z), which is nice.\n\nBut maybe the answer is required to be in terms of a Laurent series or something else. However, since the problem just asks for a function g with those properties, and given that sqrt(4z² -9) is analytic in D and satisfies the equation, that should suffice. So either expression is acceptable. However, to write it in a form that explicitly shows the analyticity, maybe the factored form with the square root expanded as 2z*sqrt(1 - 9/(4z²)) is better, but it's equivalent.\n\nLet me check another point. Take z = 3, which is in D since |3| = 3 > 2. Then g(3) = sqrt(4*9 -9) = sqrt(27) = 3*sqrt(3). Alternatively, using the other expression: 2*3*sqrt(1 - 9/(4*9)) = 6*sqrt(1 - 1/4) = 6*sqrt(3/4) = 6*(sqrt(3)/2) = 3*sqrt(3). Same result. So both expressions give the same value. So they are equivalent.\n\nTherefore, the answer is either g(z) = sqrt(4z² - 9) or g(z) = 2z*sqrt(1 - 9/(4z²)), but since the problem might prefer the simplest form, probably sqrt(4z² -9), but I need to ensure that it's analytic. Since the branch cut is inside |z| <= 3/2, and we are in |z| > 2, it's analytic.\n\nAlternatively, maybe the answer is written as 2z times sqrt(1 - 9/(4z²)), which is more explicit in terms of a Laurent series expansion, but in terms of a closed-form expression, both are correct. The problem says \"find a function\", so either form is acceptable. But I think the answer expected is probably g(z) = 2z*sqrt(1 - 9/(4z²)), since that makes the analyticity more explicit by avoiding the branch cut in the domain. Wait, but as we saw earlier, even sqrt(4z² - 9) is analytic in D because the branch cut is not in D.\n\nAlternatively, to write it as a product of square roots: sqrt(2z - 3) * sqrt(2z + 3). But here, each sqrt would have its own branch cut. For sqrt(2z -3), the branch cut is where 2z -3 is a negative real number, which is z real and less than 3/2. Similarly, sqrt(2z +3) has a branch cut where 2z +3 is a negative real number, which is z real and less than -3/2. So the combined branch cuts would be along (-infty, 3/2] and (-infty, -3/2]. But since our domain is |z| > 2, which includes points outside the disk of radius 2, these branch cuts are in the left half-plane extending to -infty. Wait, but if we take the principal branch for each sqrt(2z -3) and sqrt(2z +3), then their product would have branch cuts along (-infty, 3/2] and (-infty, -3/2]. However, in the domain |z| > 2, which includes points like z = 3, which is on the real axis, but also points in the upper and lower half-planes. However, near z = 3, sqrt(2z -3) is sqrt(3), which is fine, but as you move z around the point 3/2, but since |z| >2, you can't circle around 3/2 without leaving the domain. So maybe the product is analytic in D. Wait, actually, for |z| >2, any loop in D can't encircle the branch points at 3/2 or -3/2 because those points are inside |z| < 2. Therefore, the function sqrt(2z -3) is analytic in |z| >2, as the branch cut is from 3/2 to -infty along the real axis, but in |z| >2, the points where 2z -3 is real and negative are z real < 3/2, which are not in D. Similarly for sqrt(2z +3): 2z +3 negative real implies z real < -3/2, but z in D is |z| >2, so z real < -2. Wait, z real < -2 would be in D, but in that case, 2z +3 is real and 2z +3 < 2*(-2) +3 = -1, which is negative. Therefore, if we take the principal branch of sqrt(2z +3), then for z real < -2, sqrt(2z +3) would be a purely imaginary number. But then sqrt(2z -3) for z real < -2 is sqrt(2*(-2) -3) = sqrt(-7), which is also imaginary. Then their product is sqrt(-7)*sqrt(2z +3), but if z is approaching from the upper half-plane or lower half-plane, we might get different signs. Wait, maybe there's a problem here. For z real < -2, sqrt(2z +3) would be sqrt(negative real) = i*sqrt(|2z +3|), and sqrt(2z -3) would also be sqrt(negative real) = i*sqrt(|2z -3|). Then their product would be -sqrt(|2z +3|) * sqrt(|2z -3|) = -sqrt{(2z +3)(2z -3)} = -sqrt{4z² -9}. But we wanted sqrt(4z² -9) to be g(z). So this suggests that the product sqrt(2z -3)*sqrt(2z +3) is equal to -sqrt(4z² -9) when z is real and less than -2. But in our domain D, which includes points on the real line less than -2, this would mean that the product of the square roots has a sign different from the principal branch of sqrt(4z² -9). Therefore, if we use sqrt(2z -3)*sqrt(2z +3), we might not get the principal branch, but a different branch. Therefore, perhaps to avoid this, it's better to use the expression 2z*sqrt(1 -9/(4z²)) or sqrt(4z² -9) with the principal branch. However, in the case of sqrt(4z² -9), when z is on the negative real axis with z < -2, sqrt(4z² -9) would be positive real, because 4z² -9 is positive real. For example, z = -3: sqrt(4*(-3)^2 -9) = sqrt(36 -9) = sqrt(27) = 3*sqrt(3). On the other hand, sqrt(2z -3)*sqrt(2z +3) when z = -3 is sqrt(-6 -3)*sqrt(-6 +3) = sqrt(-9)*sqrt(-3) = (3i)(i√3) = 3i^2√3 = -3√3, which is negative. So in this case, sqrt(4z² -9) is 3√3, while the product sqrt(2z -3)*sqrt(2z +3) is -3√3. Therefore, they differ by a sign. Therefore, depending on how we define the branches, we can get different signs. Therefore, the function g(z) must be defined carefully. Since the problem states that g is analytic on D and satisfies g(z)^2 =4z² -9, there are two possible branches: one positive and one negative. However, since the problem says \"find a function g\", either branch is acceptable. But in the previous examples, for z real >2, sqrt(4z² -9) is positive, which matches the principal branch. For z real < -2, sqrt(4z² -9) is still positive, but sqrt(2z -3)*sqrt(2z +3) is negative. Therefore, the two different expressions differ by a sign. Therefore, if we use the principal branch of sqrt(4z² -9), then g(z) is equal to sqrt(4z² -9), and it's analytic in D. However, if we use sqrt(2z -3)*sqrt(2z +3), then we get a different branch, which is -sqrt(4z² -9) in parts of D.\n\nTherefore, the correct answer must be g(z) = sqrt(4z² -9) using the principal branch of the square root, which is analytic in D. Alternatively, writing it as 2z*sqrt(1 -9/(4z²)) also works and is equivalent. However, the former is simpler. Therefore, the answer is g(z) = sqrt(4z² -9), and it's analytic on D because the branch cut is inside |z| < 2.\n\nAlternatively, let me check if there's another standard form. For example, sometimes when dealing with functions outside a disk, you can express them using Laurent series. Let's try expanding sqrt(1 - 9/(4z²)) as a binomial series. So, sqrt(1 - w) = 1 - (1/2)w - (1/8)w² - ... for |w| <1. Here, w =9/(4z²), so |w| < 9/(4*4) = 9/16 <1, so the series converges. Therefore, sqrt(1 - 9/(4z²)) = 1 - (1/2)(9/(4z²)) - (1/8)(9/(4z²))² - ... So multiplying by 2z gives 2z - (9)/(4z) - (9²)/(64z³) - ..., which is a Laurent series converging for |z| > 3/2. But since our domain is |z| >2, which is within the radius of convergence (which is 3/2), wait, no. Wait, the expansion is valid for |9/(4z²)| <1 => |z| > 3/2. But our domain is |z| >2, which is more restrictive. Therefore, in |z| >2, the series converges. Therefore, this gives an explicit Laurent series expansion for g(z). But the problem just asks for a function, not necessarily a series, so the closed-form expression is acceptable.\n\nTherefore, after this lengthy thinking, I think the answer is g(z) = sqrt(4z² - 9), analytic on D = { |z| >2 } because the branch cut of the square root is inside |z| ≤ 3/2, hence does not intersect D. Therefore, this function satisfies the required properties.\n</think>To find a function \\( g \\) with the properties:\n- \\( g \\) is analytic on \\( D = \\{ z \\in \\mathbb{C} : |z| > 2 \\} \\).\n- \\( g(z)^2 = 4z^2 - 9 \\) for all \\( z \\in D \\).\n\nWe start by noting that the function \\( 4z^2 - 9 \\) can be factored as:\n\\[\n4z^2 - 9 = (2z - 3)(2z + 3)\n\\]\n\nThe zeros of \\( 4z^2 - 9 \\) are \\( z = \\pm \\frac{3}{2} \\), which lie inside the disk \\( |z| \\leq 2 \\). Therefore, in the domain \\( D \\) where \\( |z| > 2 \\), the function \\( 4z^2 - 9 \\) does not vanish.\n\nTo define \\( g(z) \\) as the square root of \\( 4z^2 - 9 \\), we need to ensure that \\( g(z) \\) is analytic. The function \\( 4z^2 - 9 \\) can be written as:\n\\[\n4z^2 - 9 = 4z^2 \\left(1 - \\frac{9}{4z^2}\\right)\n\\]\n\nFor \\( |z| > 2 \\), the term \\( \\frac{9}{4z^2} \\) has an absolute value less than 1. Thus, we can use the principal branch of the square root to define:\n\\[\ng(z) = 2z \\sqrt{1 - \\frac{9}{4z^2}}\n\\]\n\nTo verify that this \\( g(z) \\) is analytic, we need to ensure that \\( 1 - \\frac{9}{4z^2} \\) does not cross the negative real axis in the domain \\( |z| > 2 \\). Since \\( \\left| \\frac{9}{4z^2} \\right| < 1 \\), the term \\( 1 - \\frac{9}{4z^2} \\) lies in the right half-plane and does not cross the negative real axis. Therefore, the principal branch of the square root is well-defined and analytic in \\( D \\).\n\nTo confirm that \\( g(z) \\) satisfies the required equation, we compute:\n\\[\ng(z)^2 = \\left(2z \\sqrt{1 - \\frac{9}{4z^2}}\\right)^2 = 4z^2 \\left(1 - \\frac{9}{4z^2}\\right) = 4z^2 - 9\n\\]\n\nThus, \\( g(z) = 2z \\sqrt{1 - \\frac{9}{4z^2}} \\) is a valid function that is analytic on \\( D \\) and satisfies \\( g(z)^2 = 4z^2 - 9 \\).\n\nAlternatively, we can write \\( g(z) \\) directly as:\n\\[\ng(z) = \\sqrt{4z^2 - 9}\n\\]\nwhere the square root is taken with the principal branch. This is also analytic on \\( D \\) because the branch cut of the square root function is along the negative real axis, and \\( 4z^2 - 9 \\) does not cross this cut for \\( |z| > 2 \\).\n\nTherefore, the final answer is:\n\\[\n\\boxed{g(z) = \\sqrt{4z^2 - 9}}\n\\]"}