{"row_number": 6078, "expected_answer": "\\(\\log_2 n + O(1)\\)", "problem": "We toss a fair coin $n$ times. Let $R$ be the number of runs and $N_1, \\ldots, N_R$ be the lengths of the runs. Find the asymptotics of $\\max N_i$ as $n \\to \\infty$.", "generated_solution": "<think>\nOkay, so I need to figure out the asymptotics of the maximum run length when we toss a fair coin n times as n goes to infinity. Hmm, runs are consecutive heads or tails, right? So, for example, in a sequence like HHTTHHTTTH, the runs are HH, TT, HH, TTT, H, so the lengths are 2, 2, 2, 3, 1. The maximum here is 3. The problem is asking what's the behavior of the maximum run length as n becomes very large.\n\nFirst, I remember that in probability and statistics, there's something about the longest run of heads in coin tosses. Maybe I can relate that to this problem. Since the coin is fair, the probability of heads or tails is 1/2 each. But in this problem, runs can be of either heads or tails, so the maximum could be from either. But maybe the analysis for heads applies similarly to tails because of symmetry. So perhaps the maximum run length for both would behave the same way asymptotically?\n\nWait, but if we're considering runs of either heads or tails, does that affect the maximum? Intuitively, considering both might lead to a slightly longer maximum run compared to just considering one, but maybe the difference is negligible in the asymptotic sense. Let me think. Suppose we have n tosses. The number of runs is typically around 2 * (n/2) = n runs? Wait, no. Wait, the expected number of runs in n tosses is 1 + (n-1)*p, where p is the probability that a new run starts, which for a fair coin is 1/2. So the expected number of runs R is 1 + (n-1)/2 ≈ n/2. So R is approximately n/2. So the number of runs is proportional to n, and each run length is either heads or tails. Then, each run length is a geometric random variable with parameter 1/2, since after each run, the next toss starts a new run with probability 1/2. Wait, but maybe not exactly, because runs are dependent. For example, once a run ends, the next run must be of the opposite type. Hmm, so the lengths of the runs are dependent, but maybe for the purpose of maximum, the dependence is negligible? Or maybe they can be approximated as independent?\n\nAlternatively, maybe the maximum run length can be approximated using the theory of records or extreme value theory. For example, if we have a sequence of independent random variables, the maximum is typically around log n / log(1/p), where p is the parameter of the geometric distribution. But wait, in our case, the run lengths are geometrically distributed. Let me recall: the length of a run of heads is a geometric random variable with probability p=1/2 (probability of ending the run at each step). So the probability that a run has length k is (1/2)^k * 1/2 = (1/2)^{k+1}? Wait, no. Wait, geometric distribution: the number of trials until the first success, with probability p. So for a run of heads, the probability that the run length is k is (1/2)^{k-1} * (1/2) = (1/2)^k. Because to have a run of length k, you need k consecutive heads and then a tail. Similarly for a run of tails.\n\nBut in the case of the runs, each run's length is a geometric random variable with parameter 1/2, starting at 1. So the probability that a given run is of length k is (1/2)^k. Then, the expected run length is sum_{k=1}^\\infty k*(1/2)^k = 2. Which makes sense, since the average run length is 2, so with n tosses, the number of runs is about n/2, as I thought earlier.\n\nNow, we want the maximum of R such geometric variables. If R is about n/2, then the maximum of n/2 iid geometric variables with parameter 1/2. Wait, but in reality, the run lengths are not independent, since the coin is alternating between heads and tails. However, maybe for the purpose of maximum, the dependence is not too significant? Maybe the maximum run length is similar to the case of independent geometric variables?\n\nAlternatively, maybe there's a known result for the longest run in a coin tossing sequence. Let me recall. I think that the longest run of heads in n tosses is roughly log_2 n, but maybe with some constant factor. Wait, actually, I remember a result that the longest run is about log_2 n with some correction term. For example, in Erdős–Rényi law or similar.\n\nWait, perhaps the correct asymptotic is log n / log 2, which is log base 2 of n. But since we are allowing runs of either heads or tails, maybe the maximum run is similar but perhaps slightly different. Wait, but actually, since each run of heads is followed by a run of tails, so the number of runs is about n/2, and each run has a geometric length with parameter 1/2. So the maximum of about n/2 geometric variables with parameter 1/2. What is the maximum of m iid Geometric(p) variables? For m large, the maximum is roughly log m / log(1/(1-p)). Wait, the Geometric distribution here is the number of trials until the first success, so the probability mass function is P(X=k) = (1-p)^{k-1} p for k=1,2,... So in our case, p=1/2, so the survival function is P(X >= k) = (1-p)^{k-1} = (1/2)^{k-1}. So the expected number of runs with length >=k is m * (1/2)^{k-1}. So if we set this equal to 1, we get m * (1/2)^{k-1} ≈ 1, so k ≈ log_2 m + 1. Therefore, the maximum is around log_2 m, where m is the number of runs. Since m is about n/2, so log_2(n/2) = log_2 n - 1. So the maximum run length is roughly log_2 n -1. But since we are talking about asymptotics as n → ∞, the -1 becomes negligible, so the maximum is asymptotically log_2 n. But is that right? Wait, but for runs of either heads or tails.\n\nBut actually, if we have n tosses, the number of runs is R = 1 + the number of times the coin changes sides. Since each toss after the first has a 1/2 chance to switch, so the expected number of runs is 1 + (n-1)/2 ≈ n/2. So the number of runs R is concentrated around n/2.\n\nTherefore, if we model each run length as independent Geometric(1/2) variables (they are actually not independent, but maybe for the maximum, the dependence is negligible), then the maximum of R ≈ n/2 such variables is roughly log_{1/(1-p)} (R). Wait, since for a Geometric(p) variable, the maximum of m iid variables is approximately log_{1/(1-p)} (m). Since p=1/2, 1/(1-p) is 2, so log base 2 of m. So log_2 (n/2) ≈ log_2 n -1. So as n becomes large, this is approximately log_2 n. But perhaps we need to consider the exact constants here?\n\nWait, but perhaps there's a more precise result. I remember that in the case of the longest run of heads in n tosses, the expected maximum run length is approximately log_2 n - (log_2 log_2 n)/ln 2 + something. But maybe asymptotically, it's log_2 n. However, since in our problem, we are considering runs of both heads and tails, which effectively doubles the number of runs, but each run is still geometric with p=1/2.\n\nAlternatively, maybe the maximum run length is similar to the case where we have n independent trials and look for the longest run of either heads or tails. But in that case, the maximum run would be longer than considering only heads. Wait, but actually, maybe not. Because the process alternates between heads and tails, so the runs are dependent. If you have a long run of heads, the next run must be tails, so the runs are negatively correlated. But perhaps for the maximum, the effect is that the maximum run is similar to the case where you have n/2 independent runs. Hmm.\n\nAlternatively, let me think about the probability that there exists a run of length at least k in n tosses. For a given k, the expected number of runs of length at least k is approximately (n/2) * (1/2)^{k-1}. Because there are about n/2 runs, each with probability (1/2)^{k-1} of being at least k in length. So if we set (n/2) * (1/2)^{k-1} ≈ 1, then solving for k gives k ≈ log_2(n/2) +1 ≈ log_2 n. So the threshold where the expected number of such runs is 1 is around k = log_2 n. Therefore, by the second moment method, we can show that with high probability, there exists a run of length approximately log_2 n.\n\nTherefore, the maximum run length is asymptotically log_2 n. But I need to confirm this. Let me check if my reasoning makes sense.\n\nIf we have n tosses, the number of possible runs of length k is n - k +1. Wait, but no, because runs can overlap. But in the case of runs separated by different outcomes, a run of k heads can only start at positions where the previous toss is tails. So maybe the number of possible runs is approximately n/2, since each run starts after a change in the outcome. Wait, this is getting a bit confusing.\n\nAlternatively, using the Poisson approximation. The expected number of runs of length at least k is roughly n * (1/2)^{k}, since the probability that a run starting at position i is at least k is (1/2)^{k}, and there are n possible starting positions. Wait, but this is not exactly correct because runs cannot overlap. For example, if there's a run of k heads starting at position i, then positions i+1 to i+k-1 cannot start another run of heads. So the number of overlapping runs complicates things.\n\nHowever, for large k and large n, the dependence between runs might be negligible. So perhaps the expected number of runs of length at least k is approximately n * (1/2)^{k}, as if each position independently starts a run with probability (1/2)^{k}. Then, if we set n * (1/2)^{k} ≈ 1, solving for k gives k ≈ log_2 n. So the maximum run length is around log_2 n.\n\nWait, but this is actually a standard result. In the literature, the length of the longest run in a sequence of n independent fair coin tosses is known to be approximately log_2 n. Moreover, more precise results exist, such as the Erdős–Rényi law which states that the longest run of heads (or equivalently tails) in n trials is about log_{1/p} n with p=1/2 here. So in our case, since the problem allows runs of either heads or tails, does that affect the maximum?\n\nWait, no, because runs of heads and tails are interleaved. So the maximum run of heads is about log_2 n, and the same for tails. However, in the combined case, since there are more runs (about n/2 instead of n), perhaps the maximum would be similar? Wait, but if we consider the maximum over both heads and tails, then effectively, the number of runs is the same as considering only heads or only tails. Wait, actually, if you have n tosses, the number of runs of heads is approximately n/2, same for tails. Wait, no. For example, if you have a sequence H T H T..., the number of runs of heads is n/2 and tails is n/2. But in a random sequence, the number of runs of heads is roughly n/4? Wait, no, the total number of runs R is about n/2. Each run alternates between heads and tails, so the number of runs of heads is roughly R/2 ≈ n/4, same for tails. Therefore, the number of runs of heads is about n/4, so the maximum run length among heads would be similar to the maximum of n/4 geometric variables with parameter 1/2. Which would be log_2(n/4) ≈ log_2 n - 2. But since we are considering both heads and tails, the maximum over all runs would be the maximum of the heads runs and tails runs. But each has maximum around log_2 n - 2, so the overall maximum is still log_2 n asymptotically. Because the difference of constants becomes negligible as n grows. So perhaps the answer is that the maximum run length is asymptotically log_2 n, i.e., (1 + o(1)) log_2 n.\n\nBut I need to confirm this. Alternatively, maybe the maximum run length is (1 + o(1)) log_2 n. So the answer would be \\boxed{(1 + o(1)) \\log_2 n}.\n\nBut wait, in some references, the longest run in n coin tosses is known to be around log_2 n - log_2 log_2 n or something similar, but maybe that's for the case where you consider only runs of heads. For example, in the Erdős–Rényi law, they discuss that the longest run of heads is about log_2 n, but with a precise constant. Wait, actually, the Erdős–Rényi law says that if you have a sequence of independent trials with probability p of success, then the length L_n of the longest run of successes satisfies:\n\nlim_{n→∞} L_n / log_{1/p} n = 1 almost surely.\n\nSo in our case, for heads with p=1/2, the longest run of heads is about log_2 n. Similarly, the longest run of tails is also about log_2 n. But in the problem, we are considering runs of either heads or tails, so the maximum over all runs. Since both heads and tails have runs of length about log_2 n, the maximum among all runs would still be about log_2 n. So the asymptotics is still log_2 n.\n\nHence, the answer should be that the maximum run length is asymptotically equivalent to log_2 n, which can be written as \\boxed{(1 + o(1)) \\log_2 n}.\n\nBut maybe the question expects a different form? For example, sometimes in asymptotic analysis, people use ln n / ln 2 instead of log_2 n, but in terms of big Theta notation, it's all equivalent. Alternatively, if the problem expects a more precise expression, but given that it's asking for asymptotics, (1 + o(1)) log_2 n seems appropriate. Alternatively, sometimes in mathematics, the natural logarithm is used, but since the base is 2 here, log_2 n is more precise.\n\nAlternatively, if we use the result from extreme value theory, for the maximum of n iid geometric variables, but in our case, the variables are dependent. However, since the number of runs is proportional to n, and the runs are only weakly dependent (since the type alternates), the maximum should still behave like log_2 n.\n\nAlternatively, if we model the runs as a Poisson process, but I think the previous reasoning suffices. So I think the answer is that the maximum run length grows like log_2 n as n tends to infinity, so asymptotically it's (1 + o(1)) log_2 n. Hence, the answer is \\boxed{(1 + o(1)) \\log_2 n}.\n</think>To determine the asymptotic behavior of the maximum run length in a sequence of \\( n \\) fair coin tosses as \\( n \\to \\infty \\), we need to analyze the properties of the runs and their lengths.\n\n1. **Number of Runs**:\n   - The expected number of runs \\( R \\) in \\( n \\) tosses is approximately \\( \\frac{n}{2} \\). This is because each toss after the first has a \\( \\frac{1}{2} \\) probability of starting a new run.\n\n2. **Run Length Distribution**:\n   - Each run length follows a geometric distribution with parameter \\( \\frac{1}{2} \\). The probability that a run has length \\( k \\) is \\( \\frac{1}{2^k} \\).\n\n3. **Maximum of Geometric Variables**:\n   - For \\( m \\) independent geometric random variables with parameter \\( \\frac{1}{2} \\), the maximum run length is approximately \\( \\log_2 m \\). In this case, \\( m \\approx \\frac{n}{2} \\), so the maximum run length is approximately \\( \\log_2 \\left( \\frac{n}{2} \\right) \\).\n\n4. **Asymptotic Simplification**:\n   - As \\( n \\to \\infty \\), \\( \\log_2 \\left( \\frac{n}{2} \\right) \\) simplifies to \\( \\log_2 n - 1 \\). However, for asymptotic analysis, the constant term \\( -1 \\) becomes negligible, so the maximum run length is asymptotically \\( \\log_2 n \\).\n\n5. **Symmetry Consideration**:\n   - Since runs of heads and tails are symmetric and interleaved, the maximum run length among both types remains asymptotically the same as considering one type alone.\n\nThus, the asymptotic behavior of the maximum run length as \\( n \\to \\infty \\) is given by:\n\n\\[\n\\boxed{(1 + o(1)) \\log_2 n}\n\\]"}