{"row_number": 6369, "expected_answer": "130", "problem": "Find all numbers \\( n \\) with at least 4 natural divisors \\( d_1, d_2, \\ldots, d_k \\) (where \\( 1 = d_1 < d_2 < \\ldots < d_k = n \\)) such that:\n\n\\[ n = d_1^2 + d_2^2 + d_3^2 + d_4^2 \\]", "generated_solution": "<think>\nOkay, let's tackle this problem. So, we need to find all numbers n that have at least four natural divisors, ordered as 1 = d₁ < d₂ < ... < dₖ = n, such that n equals the sum of the squares of the first four divisors: d₁² + d₂² + d₃² + d₄². Hmm, interesting. Let's break this down step by step.\n\nFirst, since the divisors are in ascending order and start with 1 and end with n, the number n must have at least four divisors. That means n can't be a prime number because primes only have two divisors. Also, numbers like 4 (which has divisors 1, 2, 4) only have three, so they're out too. So n must be a composite number with at least four divisors.\n\nThe problem says \"at least four,\" but since we need to use the first four, maybe the number could have more than four divisors, but we only take the first four? Wait, but the equation is n = sum of d₁² to d₄², so even if there are more divisors, the fourth one would be included. But maybe n has exactly four divisors? Let's check both possibilities.\n\nIf n has exactly four divisors, its divisors would be 1, a, b, n, where a and b are primes or powers of primes. Wait, numbers with exactly four divisors are either the product of two distinct primes (like 6 = 2×3, divisors 1,2,3,6) or a cube of a prime (like 8 = 2³, divisors 1,2,4,8). Hmm, so in the case of 8, the divisors are 1,2,4,8. Then the sum would be 1 + 4 + 16 + 64 = 85, which is way bigger than 8, so 8 doesn't work. For 6, sum is 1 + 4 + 9 + 36 = 50, not 6. So maybe numbers with exactly four divisors don't work. Let's check another one. Take 10, divisors 1,2,5,10. Sum is 1 +4 +25 +100=130, which is not 10. So maybe numbers with exactly four divisors don't satisfy the equation. So maybe n has more than four divisors? Let's think.\n\nIf n has more than four divisors, then the first four divisors would be 1, d₂, d₃, d₄, and then others. So, for example, take 12. Divisors are 1,2,3,4,6,12. So first four are 1,2,3,4. Sum is 1 +4 +9 +16=30. 30 is not 12. How about 30? Divisors of 30 are 1,2,3,5,6,10,15,30. First four:1,2,3,5. Sum is 1+4+9+25=39≠30. Hmm, not working. Let's check 28. Divisors:1,2,4,7,14,28. First four:1,2,4,7. Sum is 1+4+16+49=70≠28. Hmm.\n\nWait, maybe n is smaller. Let's think of the smallest numbers with at least four divisors. The smallest number with four divisors is 6 (as before). But 6 didn't work. Then 8 (four divisors), 10, 12, 14, 15, 16... Let's check 15. Divisors:1,3,5,15. Sum of first four:1 +9 +25 +225=260≠15. 16: divisors 1,2,4,8,16. First four:1,2,4,8. Sum is 1 +4 +16 +64=85≠16.\n\nHow about 130? Wait, 130 was the sum for 10. Maybe n is 85? Let's check. 85 is 5×17. Divisors:1,5,17,85. Sum of first four would be 1 +25 +289 +7225=7540≠85. Nope.\n\nWait, maybe there's a number where the first four divisors add up to itself. Let's try some numbers. Let's check n=130. Wait, n=130. Divisors of 130:1,2,5,10,13,26,65,130. The first four are 1,2,5,10. Sum is 1 +4 +25 +100=130. Hey, that's equal to n! So 130 would be a solution. Wait, is that correct?\n\nWait, divisors of 130. Let's confirm: 130 divided by 1 is 130. 2 divides 130 (65), 5 divides (26), 10 divides (13). Then 13, 26, 65, 130. So yes, the divisors in order are 1,2,5,10,13,26,65,130. So first four:1,2,5,10. Their squares:1,4,25,100. Sum:1+4=5, +25=30, +100=130. So yes, 130 is a solution. That seems promising.\n\nSo 130 works. Let's check if there are others.\n\nLet's check if n=30. Divisors:1,2,3,5,6,10,15,30. First four:1,2,3,5. Sum:1 +4 +9 +25=39. 39 is not 30. What about 39? Divisors of 39:1,3,13,39. First four sum:1 +9 +169 +1521=1700≠39. Not good.\n\nHow about n=85? We checked that earlier. Not working.\n\nWait, let's think about how 130 worked. Its first four divisors are 1,2,5,10. So d₂ is 2, which is the smallest prime factor. Then d₃ is 5, the next prime factor. Then d₄ is 10, which is 2×5. So in this case, d₄ is the product of the first two prime factors. So the structure here is that the first four divisors are 1, p, q, pq, where p and q are primes. Then, n is equal to 1² + p² + q² + (pq)². But n in this case is pq times something. Wait, in the case of 130, p=2, q=5, so pq=10. Then the sum is 1 + 4 +25 +100=130. So n=130=2×5×13. Wait, 130 is 2×5×13. But why is the fourth divisor 10? Because in the list of divisors, after 1,2,5, the next would be 10, which is 2×5, and then 13, etc. So for a number with three distinct prime factors, the fourth divisor would be the product of the first two primes. Hmm. So maybe numbers that are the product of three primes where the first two primes multiply to give the fourth divisor, which is part of the sum. Let's try another number.\n\nSuppose we take primes 2,3, and another prime, say 7. Then n=2×3×7=42. Divisors of 42:1,2,3,6,7,14,21,42. So first four:1,2,3,6. Sum of squares:1 +4 +9 +36=50. 50≠42. Not working.\n\nBut in 130's case, the fourth divisor is 10, which is 2×5, and n=2×5×13. So maybe if the fourth divisor is pq, then the next prime is larger than pq? Wait, in 130's case, after 10 comes 13, which is the third prime factor. So the ordering of divisors is 1,2,5,10,13,... So 5 is smaller than 10, which is pq. Wait, but 5 is the third divisor, so the order is sorted. Let's see: divisors are generated in order. Let me think: To list divisors, they are generated by prime factors. For n = p*q*r, where p < q < r primes. Then the divisors are:\n\n1, p, q, r, pq, pr, qr, pqr.\n\nWait, but sorted in order. Wait, actually, that's not necessarily the case. For example, take n=2*3*5=30. Divisors are 1,2,3,5,6,10,15,30. So sorted order:1,2,3,5,6,10,15,30. So in this case, the first four divisors are 1,2,3,5, then 6. For 130, which is 2*5*13, the divisors in order are 1,2,5,10,13,26, etc. So here, the fourth divisor is 10, which is 2*5, and then the next is 13. So the ordering here depends on the primes. If the product of the first two primes is smaller than the third prime, then that product comes before the third prime. For example, in 130, 2*5=10, and 13 is larger than 10, so 10 comes before 13 in the divisor list. But if the third prime is smaller than the product of the first two, then the third prime would come first. For example, take n=2*3*5=30. The product of first two primes is 6, which is larger than 5, so 5 comes before 6. Hmm, so in general, the sorted list of divisors for n = p*q*r (p < q < r primes) is 1, p, q, r, pq, pr, qr, pqr if r < pq? Wait, but in 130, p=2, q=5, r=13. 2*5=10 <13, so 10 comes before 13. But in 30, p=2, q=3, r=5. 2*3=6>5, so 5 comes before 6. So that's the key.\n\nSo in order for the fourth divisor to be pq, it must be that pq < r. Otherwise, if pq > r, then the fourth divisor would be r. So, in the case of n=130, since 2*5=10 <13, the fourth divisor is 10. So the sum would be 1 + p² + q² + (p*q)², which equals n = p*q*r. So for n to be equal to that sum, we need p*q*r = 1 + p² + q² + (p*q)². Let's write that equation down:\n\np*q*r = 1 + p² + q² + p²*q²\n\nThat's the key equation we need to solve for primes p < q < r, with the condition that p*q < r.\n\nSo our problem reduces to finding primes p, q, r with p < q < r and p*q < r, such that p*q*r = 1 + p² + q² + p²*q².\n\nLet's rearrange this equation. Let's factor the right-hand side:\n\n1 + p² + q² + p²*q² = (1 + p²)(1 + q²)\n\nWait, let me check:\n\n(1 + p²)(1 + q²) = 1*1 + 1*q² + p²*1 + p²*q² = 1 + q² + p² + p² q². Yes, exactly. So the equation becomes:\n\np q r = (1 + p²)(1 + q²)\n\nTherefore:\n\nr = (1 + p²)(1 + q²) / (p q)\n\nSince r must be a prime number, the right-hand side must be an integer and a prime.\n\nSo, r = [ (1 + p²)(1 + q²) ] / (p q )\n\nWe need this to result in an integer and that integer must be a prime. Also, since p and q are primes, and p < q, we can try small primes p and q to see if this works.\n\nLet's start with the smallest primes. Let p=2.\n\nCase 1: p=2.\n\nThen, r = [ (1 + 4)(1 + q²) ] / (2 q ) = [5(1 + q²)] / (2 q )\n\nWe need this to be an integer and prime. Let's try q=3.\n\nq=3:\n\nr = [5*(1 + 9)]/(2*3) = 5*10 /6 = 50/6 ≈8.333... Not integer. Not valid.\n\nq=5:\n\nr = [5*(1 +25)]/(2*5)=5*26 /10=130/10=13. Which is prime. So here, p=2, q=5, r=13. That's the case for n=2*5*13=130. So that works.\n\nLet's check next q=7:\n\nr= [5*(1 +49)]/(2*7)=5*50/14=250/14≈17.857. Not integer.\n\nq=11:\n\nr= [5*(1 +121)]/(2*11)=5*122/22=610/22≈27.727. Not integer.\n\nq=13:\n\nr=5*(1 +169)/(2*13)=5*170/26=850/26≈32.69. Not integer.\n\nSo, when p=2, only q=5 gives an integer prime r=13. So that's 130.\n\nCase 2: p=3. Let's see if there are solutions for p=3.\n\nThen:\n\nr = [ (1 + 9)(1 + q²) ] / (3 q ) = [10(1 + q²)] / (3 q )\n\nWe need this to be an integer and prime. Let's try q=5 (since p=3 < q).\n\nq=5:\n\nr=10*(1 +25)/15=10*26/15≈260/15≈17.333... Not integer.\n\nq=7:\n\nr=10*(1+49)/21=10*50/21≈500/21≈23.809... Not integer.\n\nq=11:\n\nr=10*(1 +121)/33=10*122/33≈1220/33≈36.96... Not integer.\n\nq=2: Wait, q must be greater than p=3, so next primes are 5,7,11,...\n\nNot seeing any integer r here. Let's check q= next primes.\n\nq=13:\n\nr=10*(1+169)/39=10*170/39≈1700/39≈43.58. Not integer.\n\nSo, no solution for p=3.\n\nCase 3: p=5. Then:\n\nr = [ (1 +25)(1 + q²) ] / (5 q ) = [26(1 + q²)] / (5 q )\n\nq must be a prime greater than 5. Let's try q=7:\n\nr=26*(1 +49)/35=26*50/35=1300/35=260/7≈37.14... Not integer.\n\nq=11:\n\nr=26*(1 +121)/55=26*122/55=3172/55≈57.67... Not integer.\n\nq=13:\n\nr=26*(1 +169)/65=26*170/65=4420/65=68. Not integer. 68 isn't prime.\n\nq=17:\n\nr=26*(1 +289)/85=26*290/85=7540/85≈88.7... Not integer.\n\nNot working either.\n\nCase 4: p=7. Let's check even though it's getting bigger.\n\nr= [50(1 + q²)]/(7 q )\n\nBut q would be primes greater than 7.\n\nq=11:\n\nr=50*(1 + 121)/77=50*122/77=6100/77≈79.22. Not integer.\n\nSo, seems like no solutions here.\n\nSo, the only solution when p=2, q=5, r=13, giving n=130.\n\nBut wait, maybe there are numbers with more than three prime factors?\n\nWait, the problem states \"numbers with at least four divisors.\" So n could be, for example, a product of more primes, or a prime power.\n\nWait, let's check numbers with four divisors but not of the form p*q*r. For example, n could be a square of a prime, like 16 (which is 2^4, divisors 1,2,4,8,16). Wait, but 16 has five divisors. The first four are 1,2,4,8. Sum is 1+4+16+64=85. 85≠16. Not working.\n\nHow about n=12, which has divisors 1,2,3,4,6,12. The first four divisors sum to 1+4+9+16=30. 30≠12. 30 is in the problem earlier, but when we checked 30, it didn't work. However, 30's sum is 39, which isn't equal. Wait, 12's sum is 30, which isn't 12.\n\nAnother idea: maybe numbers with four divisors where the fourth divisor is a square? Not sure. Let's see. For example, take n=28. Divisors:1,2,4,7,14,28. First four:1,2,4,7. Sum:1+4+16+49=70. 70≠28. Not working.\n\nWait, but 70's divisors:1,2,5,7,10,14,35,70. First four:1,2,5,7. Sum:1+4+25+49=79. 79≠70. Not working.\n\nWait, but in the previous case with 130, the number is a product of three primes, but let's confirm if there's another number. Let's suppose p=2, q=2. But wait, primes are distinct? No, q has to be greater than p. Wait, p and q have to be primes with p < q. So, in the case of 130, p=2, q=5, r=13.\n\nIs there another possible combination where p*q*r equals (1 + p² + q² + (p*q)^2 )?\n\nLet me check p=2, q=2. But they have to be distinct primes. If p=2, q=3:\n\nr=(1 +4 +9 +36)/(2*3)=50/6≈8.333. Not integer.\n\np=2, q=7:\n\nAs before, (1+4+49+196)/(2*7)=250/14≈17.857. Not integer.\n\nSo only when q=5 gives r=13.\n\nAnother approach: Check if there are numbers with more divisors, but where the fourth divisor is different. For example, numbers with a different structure.\n\nTake n=84. Divisors:1,2,3,4,6,7,12,14,21,28,42,84. First four:1,2,3,4. Sum:1+4+9+16=30≠84.\n\nn=105: Divisors 1,3,5,7,15,21,35,105. First four:1,3,5,7. Sum:1+9+25+49=84≠105.\n\nn=210: Divisors:1,2,3,5,6,7,10,15,21,30,35,42,70,105,210. First four:1,2,3,5. Sum:1+4+9+25=39≠210.\n\nWait, another thought: Maybe numbers where the first four divisors are 1, p, p², p³, but then n would be a prime power. But for a prime power, the number of divisors is k+1 if n=p^k. So if n has at least four divisors, then k ≥3. Let's take n=16, which is 2^4. Divisors:1,2,4,8,16. First four:1,2,4,8. Sum is 1+4+16+64=85≠16. Not working. n=81=3^4. Divisors:1,3,9,27,81. Sum:1+9+81+729=820≠81.\n\nAlternatively, maybe n is a square number with more divisors. Take n=36. Divisors:1,2,3,4,6,9,12,18,36. First four:1,2,3,4. Sum:1+4+9+16=30≠36. Not working.\n\nAnother angle: Let's think if there's a number where the first four divisors are 1, a, b, a*b, similar to the 130 case but not necessarily requiring three primes. For example, if n is a product of two primes where one is squared. Wait, let's try n=100. Divisors:1,2,4,5,10,20,25,50,100. First four:1,2,4,5. Sum:1+4+16+25=46≠100. Not working.\n\nWait, let's consider n=46. Divisors:1,2,23,46. Only four divisors. Sum:1+4+529+2116=2650≠46.\n\nHmm. Let's check n=50. Divisors:1,2,5,10,25,50. First four:1,2,5,10. Sum:1+4+25+100=130. Wait, 130! But n=50. So here, the sum is 130, which is another number. But 50's sum is 130, which we already know is a solution.\n\nBut how about n=130. It's sum is 130. So n=130 is a solution. Let's check if there's another number where the sum of the first four divisors equals itself. For instance, n=1. But n=1 has only one divisor. n=3, which has divisors 1,3. Not enough. n=4: 1,2,4. Still not enough. n=5: prime. So no. So perhaps 130 is the only number.\n\nWait, let's check if n=85. Divisors:1,5,17,85. Sum:1 +25 +289 +7225=7540≠85. Nope.\n\nWait, let's check n=325. Divisors:1,5,13,25,65,325. First four:1,5,13,25. Sum:1 +25 +169 +625=820≠325.\n\nWait, let's check n=546. Divisors:1,2,3,6,7,13,14,21,26,39,42,78,91,182,273,546. First four:1,2,3,6. Sum:1 +4 +9 +36=50≠546.\n\nWait, another idea. Let's try the equation again but for other forms. Suppose n is a product of two primes, p and q, but with more exponents. For example, n = p^3 * q. Let's take n=12=2^2*3. Divisors:1,2,3,4,6,12. First four:1,2,3,4. Sum:1+4+9+16=30≠12. Not helpful.\n\nAlternatively, n= p^2 * q^2. Take n=36=2^2*3^2. Divisors:1,2,3,4,6,9,12,18,36. First four:1,2,3,4. Sum:30≠36.\n\nWait, maybe check if the sum equals n for other numbers. For example, n=30. Sum was 39. 39's divisors:1,3,13,39. Sum:1+9+169+1521=1700≠39.\n\nn=85. Divisors:1,5,17,85. Sum is way larger. 130's sum is 130. 130's divisors:1,2,5,10,13,26,65,130. So first four sum to 130. So, that's the only one found so far.\n\nAnother thought: Maybe n=130 is the only solution. Let's verify if there's any other number. Suppose we try p=2, q=7. Then, r=(1+4)(1+49)/(2*7)=5*50/14=250/14≈17.857. Not integer. Doesn't work. So that's not possible.\n\nWait, what if n has four divisors but in a different structure? For example, n=p^3. Divisors:1,p,p²,p³. Sum:1 + p² + p⁴ + p^6. That's way larger than n=p³. Not possible.\n\nAlternatively, numbers with six divisors. For example, n=12, divisors 1,2,3,4,6,12. The first four divisors sum to 30. Not 12.\n\nAnother attempt: Let's assume n is a product of two primes, p and q, with p=2 and q= something. So divisors are 1,2,q,2q. So sum is 1 +4 + q² +4q²=1 +4 + q² +4q²=5 +5q². And n=2q. So we have equation 2q =5 +5q² =>5q² -2q +5=0. That quadratic equation discriminant: 4 -100= -96. No real roots. So no solution here.\n\nSimilarly, if n is a product of two primes, but in this case, with four divisors, but in the case of n being a product of two primes, it only has four divisors. So as above, that approach doesn't work. So no solution for numbers with exactly four divisors except maybe 130, but 130 has eight divisors. So 130 is a product of three primes.\n\nWait, another angle. Suppose n has four divisors:1, a, b, n. Then sum is 1 +a² +b² +n². But n is equal to this sum, so n =1 +a² +b² +n². Rearranged, n² +a² +b² +1 -n=0. Which seems impossible since n² is much larger than n for n>1. Hence, numbers with exactly four divisors won't work, which aligns with our earlier observations.\n\nTherefore, the only candidate we have is 130. Let's check once more to confirm.\n\nDivisors of 130:1,2,5,10,13,26,65,130. The first four divisors are 1,2,5,10. Their squares:1,4,25,100. Sum:1+4=5, 5+25=30, 30+100=130. Yep, exactly n. So 130 is a solution.\n\nAre there others? Let's think of another structure. Suppose n has divisors 1, p, q, r, ..., where p is prime, q is another prime or composite, etc. Maybe when n has a divisor structure where the fourth divisor is something else. For example, n=210.\n\nDivisors of 210:1,2,3,5,6,7,10,15,21,30,35,42,70,105,210. First four:1,2,3,5. Sum:1+4+9+25=39≠210.\n\nAlternatively, let's take n=5460, but that's too big. Probably not helpful.\n\nAlternatively, suppose there's another triplet of primes p, q, r where p*q*r =1 +p² +q² + (p*q)^2. Let's try p=2, q=7. Then:\n\nr=(1 +4 +49 + (2*7)^2 )/(2*7)=(1 +4 +49 +196)/14=250/14≈17.857. Not integer.\n\np=2, q=11:\n\nr=(1 +4 +121 + (22)^2 )/(2*11)=(1 +4 +121 +484)/22=610/22≈27.727. Not integer.\n\np=3, q=2. But p must be smaller than q, so p=2, q=3. Which we already tried.\n\np=2, q=13:\n\nr=(1 +4 +169 + (26)^2 )/(2*13)=(1+4+169+676)/26=850/26=32.69... Not integer.\n\nSo no luck.\n\nAnother idea: Maybe the number has more than three prime factors. For example, n=2*3*5*7=210. But we've seen that first four divisors are 1,2,3,5. Sum:1+4+9+25=39≠210.\n\nAlternatively, maybe n is a square number with a specific structure. Let's try n=325=5²*13. Divisors:1,5,13,25,65,325. First four:1,5,13,25. Sum:1+25+169+625=820≠325.\n\nAlternatively, n=1 +4 +9 +16=30. Which is 30. But sum of 1,2,3,4's squares. But divisors of 30 are1,2,3,5,6, etc. The fourth divisor is 5, so sum would be 1+4+9+25=39≠30. So no.\n\nAlternatively, is there a number where the first four divisors' squares sum to itself? We have 130. Let's see if there are others.\n\nWait, let's check n=30. We saw sum is 39. What's 39's sum? Divisors of 39:1,3,13,39. Sum:1 +9 +169 +1521=1700≠39. No.\n\nn=130: sum is 130. So perhaps this is unique.\n\nAlternatively, n=325: sum of first four divisors' squares is 820. Check divisors of 820. Let's see: 820 factors. 820=2²*5*41. Divisors:1,2,4,5,10,20,41,82,164,205,410,820. First four:1,2,4,5. Sum:1 +4 +16 +25=46≠820.\n\nAlternatively, check n=50. Divisors:1,2,5,10,25,50. First four sum:1+4+25+100=130. So n=50's sum is 130. But n=130's sum is 130. So, this creates a chain, but no new numbers.\n\nAnother thought: If there is another solution, it must satisfy the equation p q r = (1 + p²)(1 + q²) with primes p < q < r and p q < r. We saw only p=2, q=5 gives r=13. Are there any other primes p, q where this could work?\n\nLet me check p=2, q= next prime.\n\np=2, q=7:\n\n(1 +4)(1 +49) =5*50=250=2*7*r=14 r. So 14 r=250 ⇒ r=250/14≈17.857. Not integer.\n\np=2, q=11:\n\n(1 +4)(1 +121)=5*122=610=2*11*r=22 r ⇒ r=610/22=27.727. Not integer.\n\np=2, q=17:\n\n(1 +4)(1 +289)=5*290=1450=2*17*r=34 r ⇒ r=1450/34≈42.647. Not integer.\n\np=2, q= next primes all give non-integer r.\n\nIf p=3:\n\n(1 +9)(1 + q²)=10*(1 + q²)=3*q*r. Need 10*(1 + q²)/(3*q)=r. For q=5:\n\n10*(1 +25)/15=260/15≈17.333. Not integer.\n\nq=7:\n\n10*(1 +49)/21=500/21≈23.809. Not integer.\n\np=5:\n\n(1 +25)(1 + q²)=26*(1 + q²)=5*q*r. For q=7:\n\n26*(1 +49)/35=26*50/35=1300/35≈37.14. Not integer.\n\nSo, no other solutions here.\n\nTherefore, it's likely that 130 is the only solution. Therefore, the answer is \\boxed{130}.\n</think>To solve the problem of finding all numbers \\( n \\) with at least four natural divisors \\( d_1, d_2, d_3, d_4 \\) such that \\( n = d_1^2 + d_2^2 + d_3^2 + d_4^2 \\), we proceed as follows:\n\n1. **Identify the structure of \\( n \\):**\n   - \\( n \\) must have at least four divisors, and the first four divisors in ascending order are \\( 1, d_2, d_3, d_4 \\).\n   - We hypothesize that \\( n \\) is a product of three distinct primes \\( p, q, r \\) such that \\( p < q < r \\) and \\( pq < r \\).\n\n2. **Formulate the equation:**\n   - Given \\( n = pqr \\), the equation \\( n = 1^2 + d_2^2 + d_3^2 + d_4^2 \\) translates to:\n     \\[\n     pqr = 1 + p^2 + q^2 + (pq)^2\n     \\]\n   - Simplifying, we get:\n     \\[\n     pqr = 1 + p^2 + q^2 + p^2 q^2\n     \\]\n   - Rearranging, we have:\n     \\[\n     pqr = (1 + p^2)(1 + q^2)\n     \\]\n\n3. **Test small primes:**\n   - Let \\( p = 2 \\) and \\( q = 5 \\):\n     \\[\n     r = \\frac{(1 + 2^2)(1 + 5^2)}{2 \\cdot 5} = \\frac{5 \\cdot 26}{10} = 13\n     \\]\n   - Thus, \\( n = 2 \\cdot 5 \\cdot 13 = 130 \\).\n\n4. **Verify the solution:**\n   - The divisors of 130 are \\( 1, 2, 5, 10, 13, 26, 65, 130 \\).\n   - The first four divisors are \\( 1, 2, 5, 10 \\).\n   - Calculate the sum of their squares:\n     \\[\n     1^2 + 2^2 + 5^2 + 10^2 = 1 + 4 + 25 + 100 = 130\n     \\]\n   - This confirms that \\( n = 130 \\) satisfies the given condition.\n\n5. **Check for other possible solutions:**\n   - Testing other combinations of primes \\( p, q, r \\) does not yield any other solutions that satisfy the equation \\( pqr = (1 + p^2)(1 + q^2) \\).\n\nTherefore, the only number \\( n \\) that satisfies the given condition is \\(\\boxed{130}\\)."}