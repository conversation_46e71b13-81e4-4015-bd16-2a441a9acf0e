{"row_number": 19088, "expected_answer": "\\( x = (A^TA)^{-1}A^Tb \\)", "problem": "Let \\( A \\) be a real \\( (m \\times n) \\) matrix. Find and classify the critical points of the function \\( f(x) = ||Ax - b||^2 \\), assuming that \\( A'A \\) is invertible.", "generated_solution": "<think>\nOkay, so I need to find and classify the critical points of the function f(x) = ||Ax - b||², where A is an m x n real matrix and A'A is invertible. Hmm, let's start by recalling what critical points are. Critical points are points where the derivative is zero or undefined. Since this function is a squared norm, it's likely differentiable everywhere, so we just need to find where the gradient is zero. \n\nFirst, I remember that the squared norm ||Ax - b||² can be expanded as (Ax - b)'(Ax - b), which is the same as (Ax - b) transpose multiplied by (Ax - b). Let me write that out: f(x) = (Ax - b)ᵗ(Ax - b). Expanding this might help. Let's do that.\n\nExpanding the expression, we get x'A'Ax - 2b'Ax + b'b. So f(x) is a quadratic function in x. To find the critical points, I need to take the derivative of f with respect to x and set it equal to zero. \n\nSince f is a scalar function, the derivative, or gradient, would be a vector. Let me recall how to take derivatives of such expressions. The derivative of x'Mx with respect to x is (M + M')x, but if M is symmetric, it's 2Mx. Since A'A is symmetric because (A'A)' = A'A'' = A'A, that's symmetric. So the derivative of x'A'Ax is 2A'Ax. Then, the derivative of -2b'Ax is -2A'b, since the derivative of a linear term a'x is a. The derivative of the constant term b'b is zero. So putting it all together, the gradient ∇f(x) is 2A'Ax - 2A'b. \n\nSet this gradient equal to zero to find the critical points: 2A'Ax - 2A'b = 0. Dividing both sides by 2, we get A'Ax = A'b. Since we are given that A'A is invertible, we can multiply both sides by (A'A)⁻¹ to solve for x. Therefore, the critical point is x = (A'A)⁻¹A'b. That's the least squares solution, right? So that's the only critical point.\n\nNow, to classify this critical point, we need to determine if it's a minimum, maximum, or saddle point. Since the function f(x) is a quadratic form, and the matrix A'A is the coefficient matrix of the quadratic term. Since A is a real matrix, A'A is positive semi-definite because for any vector x, x'A'Ax = ||Ax||² ≥ 0. However, since A'A is invertible, it must be positive definite. Because if A'A were only positive semi-definite, then there would exist some non-zero x such that A'Ax = 0, which would imply that Ax = 0. But if A'A is invertible, it's only possible if x = 0. Therefore, A'A is positive definite.\n\nA positive definite matrix in the quadratic term implies that the function f(x) is convex, and thus the critical point is a global minimum. Therefore, the only critical point is the global minimum at x = (A'A)⁻¹A'b.\n\nLet me check if I missed anything. The problem says to classify the critical points. Since there's only one critical point and the function is convex, there's no maximum or saddle points. So yes, the classification is that it's a global minimum. \n\nI think that's it. So the critical point is found by solving the normal equations, and since A'A is invertible, there's a unique solution which is the minimum.\n</think>To find and classify the critical points of the function \\( f(x) = ||Ax - b||^2 \\), where \\( A \\) is an \\( m \\times n \\) real matrix and \\( A^\\top A \\) is invertible, we proceed as follows:\n\n1. **Expand the function**:\n   \\[\n   f(x) = ||Ax - b||^2 = (Ax - b)^\\top (Ax - b) = x^\\top A^\\top A x - 2 b^\\top A x + b^\\top b.\n   \\]\n\n2. **Compute the gradient**:\n   The gradient of \\( f(x) \\) is given by the derivative of the quadratic form. For the quadratic term \\( x^\\top A^\\top A x \\), the gradient is \\( 2 A^\\top A x \\). For the linear term \\( -2 b^\\top A x \\), the gradient is \\( -2 A^\\top b \\). Therefore, the gradient of \\( f(x) \\) is:\n   \\[\n   \\nabla f(x) = 2 A^\\top A x - 2 A^\\top b.\n   \\]\n\n3. **Set the gradient to zero**:\n   To find the critical points, set the gradient to zero:\n   \\[\n   2 A^\\top A x - 2 A^\\top b = 0.\n   \\]\n   Simplifying, we get:\n   \\[\n   A^\\top A x = A^\\top b.\n   \\]\n   Since \\( A^\\top A \\) is invertible, we can solve for \\( x \\) by multiplying both sides by \\( (A^\\top A)^{-1} \\):\n   \\[\n   x = (A^\\top A)^{-1} A^\\top b.\n   \\]\n\n4. **Classify the critical point**:\n   To classify the critical point, we need to determine the nature of the function \\( f(x) \\). The function \\( f(x) \\) is a quadratic form with the matrix \\( A^\\top A \\) as the coefficient of the quadratic term. Since \\( A^\\top A \\) is symmetric and positive definite (as \\( A^\\top A \\) is invertible and \\( x^\\top A^\\top A x = ||Ax||^2 \\geq 0 \\) with equality only when \\( x = 0 \\)), the function \\( f(x) \\) is convex. Therefore, the critical point is a global minimum.\n\nThus, the critical point of \\( f(x) = ||Ax - b||^2 \\) is:\n\\[\n\\boxed{x = (A^\\top A)^{-1} A^\\top b}\n\\]\nand it is a global minimum."}