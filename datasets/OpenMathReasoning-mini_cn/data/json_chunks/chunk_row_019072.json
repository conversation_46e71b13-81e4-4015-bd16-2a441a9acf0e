{"row_number": 19072, "expected_answer": "\\( 45 \\cdot (55)^2 \\)", "problem": "Let \\( n_1 = x_1x_2x_3 \\) and \\( n_2 = y_1y_2y_3 \\) be two 3-digit numbers. How many pairs of \\( n_1 \\) and \\( n_2 \\) can be formed so that \\( n_1 \\) can be subtracted from \\( n_2 \\) without borrowing?", "generated_solution": "<think>\nOkay, so I need to find the number of pairs of 3-digit numbers, n1 and n2, where subtracting n1 from n2 doesn't require any borrowing. Hmm, borrowing in subtraction happens when a digit in the minuend (n2 here) is smaller than the corresponding digit in the subtrahend (n1), right? So, for each digit place (units, tens, hundreds), the digit in n2 must be greater than or equal to the corresponding digit in n1. That way, when you subtract, you don't have to borrow from the next higher place.\n\nLet me break this down. Let's denote n1 as x1x2x3 and n2 as y1y2y3, where each x and y represents a digit. Since they're 3-digit numbers, x1 and y1 can't be zero. But x2, x3, y2, y3 can be zero, right? Wait, no, actually, for a 3-digit number, the first digit is from 1-9, and the other two can be 0-9. So, x1 and y1 are 1-9, while x2, x3, y2, y3 are 0-9.\n\nThe key here is that for each digit position (units, tens, hundreds), y_i >= x_i. Because if, say, in the units place, y3 >= x3, then subtracting x3 from y3 doesn't require a borrow. Similarly for tens place: y2 >= x2, and hundreds place: y1 >= x1. If all three of these conditions are satisfied, then subtracting n1 from n2 doesn't need any borrowing.\n\nTherefore, the problem reduces to counting the number of possible pairs (n1, n2) where for each digit i (1 to 3), y_i >= x_i. Since the digits are independent across the different places (as there's no borrowing affecting the other digits), I can compute the number of valid pairs for each digit position separately and then multiply them together.\n\nLet's start with the hundreds place. Here, both x1 and y1 range from 1 to 9 (since they are the first digits of 3-digit numbers). The condition is y1 >= x1. How many pairs (x1, y1) satisfy this?\n\nFor each x1, y1 can be from x1 to 9. So for x1 = 1, y1 can be 1-9 (9 options), for x1 = 2, y1 can be 2-9 (8 options), ..., for x1 = 9, y1 can only be 9 (1 option). So the number of valid pairs for the hundreds place is the sum from k=1 to 9 of k, which is 9+8+7+...+1. Wait, but when x1=1, y1 has 9 options; x1=2, 8 options, etc. So the total is 9 + 8 + ... + 1. The sum from 1 to 9 is (9*10)/2 = 45, but here it's the sum from 1 to 9 in reverse. So 9+8+7+6+5+4+3+2+1 = 45. So 45 possible pairs for the hundreds place.\n\nNow the tens place: x2 and y2 can be from 0 to 9. But here, y2 must be >= x2. So similar logic, but x2 can start at 0. Let's calculate the number of pairs (x2, y2) where y2 >= x2, and x2, y2 are digits from 0 to 9.\n\nFor x2=0, y2 can be 0-9 (10 options)\nFor x2=1, y2=1-9 (9 options)\n...\nFor x2=9, y2=9 (1 option)\n\nSo the sum here is 10 + 9 + 8 + ... +1. The sum from 1 to 10 is (10*11)/2 = 55. But here, starting from 10 when x2=0, then 9, etc., so it's 10 + 9 + ... +1. Wait, 10 + sum from 1 to 9. But sum from 1 to 9 is 45, so 10 +45=55. So total 55 pairs for the tens place.\n\nSimilarly, the units place is the same as the tens place because x3 and y3 are digits from 0-9. So, the units place also contributes 55 pairs.\n\nTherefore, the total number of pairs is the product of the hundreds, tens, and units possibilities: 45 * 55 * 55.\n\nWait, let me compute that.\n\nFirst, compute 45 * 55.\n\n45 * 55: 45*50=2250, 45*5=225, so total 2250+225=2475.\n\nThen, 2475 *55. Let's compute that step by step.\n\n2475 * 50 = 123,750\n2475 *5 = 12,375\nAdding them together: 123,750 +12,375 = 136,125.\n\nTherefore, the total number of pairs is 136,125.\n\nWait a second. Let me check if I did that multiplication correctly.\n\nFirst, 45 * 55: 45*(50 +5) = 45*50 +45*5 = 2250 +225 = 2475. That's correct.\n\nThen, 2475 *55 = 2475*(50 +5) = 2475*50 +2475*5.\n\n2475*50: 2475 *5 *10 = 12,375 *10 = 123,750.\n\n2475*5: 12,375.\n\nSo 123,750 +12,375 = 136,125. Yes, that's correct.\n\nSo the total number of pairs is 136,125.\n\nBut wait, let me check if there's any mistake in the logic. The hundreds digit is 1-9 for both numbers, and we required y1 >=x1, leading to 45 possibilities. For the tens and units digits, which are 0-9, and y >=x, leading to 55 possibilities each. Then multiplying them gives 45*55*55 = 136,125. That seems correct.\n\nAlternatively, let's think of how to compute the number of pairs where y1 >=x1, y2 >=x2, y3 >=x3, with x1,y1 from 1-9, x2,y2 from 0-9, and x3,y3 from 0-9. Each digit's pair is independent, so total number is the product.\n\nFor the hundreds place: number of ordered pairs (x1,y1) where y1 >=x1. For x1 from 1-9, for each x1, y1 can be x1 to 9. So total is sum_{x1=1}^9 (10 - x1). Wait, wait, 9 - x1 +1. So, for x1=1, 9 options (1-9), which is 9-1+1=9. Similarly, x1=2: 8 options. So sum_{x1=1}^9 (10 -x1). Wait, 10 -x1? Let's see:\n\nIf x1=1: 9 options (1 to9). 10 -1=9, yes. So sum_{x1=1}^9 (10 -x1) = sum_{k=1}^9 k = 45. So that's correct.\n\nFor the tens and units digits, x and y from 0-9. For each x in 0-9, y can be x to 9, so number of options is (10 -x). So sum_{x=0}^9 (10 -x) = sum_{k=1}^{10}k = 55. So 55 for each. Therefore, multiplying 45*55*55=136,125. Seems correct.\n\nTherefore, the answer should be 136,125.\n\nBut let me verify with a smaller case. Let's say 2-digit numbers. Suppose n1 = x1x2 and n2 = y1y2. Then, similar logic, the hundreds place here is the tens place in 2-digit. But for 2-digit numbers, the first digit (tens place) is from 1-9, the second (units) from 0-9. Then total pairs would be (sum_{x1=1}^9 (10 -x1)) * (sum_{x2=0}^9 (10 -x2)) = 45 * 55 = 2475. Let's check with actual example. Suppose n1 and n2 are 2-digit numbers, how many pairs where n2 >=n1 without borrowing. For each digit, y1 >=x1 and y2 >=x2. Then, the count is indeed 45*55=2475. Let's check with 1-digit numbers. If they are 1-digit numbers (from 1-9), then the number of pairs where y >=x is sum_{x=1}^9 (10 -x) = 45, which is correct. So the formula seems to hold.\n\nTherefore, scaling up to 3 digits, it's 45*55*55=136,125. Therefore, I think the answer is 136,125.\n\n**Final Answer**\n\\boxed{136125}\n</think>To solve the problem of finding the number of pairs of 3-digit numbers \\( n_1 = x_1x_2x_3 \\) and \\( n_2 = y_1y_2y_3 \\) such that \\( n_1 \\) can be subtracted from \\( n_2 \\) without borrowing, we need to ensure that each digit of \\( n_2 \\) is greater than or equal to the corresponding digit of \\( n_1 \\).\n\n1. **Hundreds place (x1 and y1):**\n   - Both \\( x_1 \\) and \\( y_1 \\) range from 1 to 9.\n   - The number of valid pairs \\((x_1, y_1)\\) where \\( y_1 \\geq x_1 \\) is calculated as the sum of the series \\( 9 + 8 + 7 + \\ldots + 1 \\).\n   - This sum is \\( \\frac{9 \\times 10}{2} = 45 \\).\n\n2. **Tens place (x2 and y2):**\n   - Both \\( x_2 \\) and \\( y_2 \\) range from 0 to 9.\n   - The number of valid pairs \\((x_2, y_2)\\) where \\( y_2 \\geq x_2 \\) is calculated as the sum of the series \\( 10 + 9 + 8 + \\ldots + 1 \\).\n   - This sum is \\( \\frac{10 \\times 11}{2} = 55 \\).\n\n3. **Units place (x3 and y3):**\n   - Both \\( x_3 \\) and \\( y_3 \\) range from 0 to 9.\n   - The calculation is the same as the tens place, resulting in 55 valid pairs.\n\nThe total number of pairs is the product of the number of valid pairs for each digit place:\n\\[\n45 \\times 55 \\times 55\n\\]\n\nFirst, calculate \\( 45 \\times 55 \\):\n\\[\n45 \\times 55 = 45 \\times (50 + 5) = 45 \\times 50 + 45 \\times 5 = 2250 + 225 = 2475\n\\]\n\nNext, calculate \\( 2475 \\times 55 \\):\n\\[\n2475 \\times 55 = 2475 \\times (50 + 5) = 2475 \\times 50 + 2475 \\times 5\n\\]\n\\[\n2475 \\times 50 = 123750\n\\]\n\\[\n2475 \\times 5 = 12375\n\\]\n\\[\n123750 + 12375 = 136125\n\\]\n\nThus, the number of pairs of \\( n_1 \\) and \\( n_2 \\) is \\(\\boxed{136125}\\)."}