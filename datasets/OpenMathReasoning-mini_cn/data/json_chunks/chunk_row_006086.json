{"row_number": 6086, "expected_answer": "921", "problem": "Find the last three digits in the decimal representation of $111^{2012}$.", "generated_solution": "<think>\nOkay, so I need to find the last three digits of 111 raised to the power of 2012. Hmm, that means I have to compute 111^2012 mod 1000, right? Because the last three digits of a number are equivalent to the number modulo 1000. \n\nFirst, maybe I can simplify the base. Let's see, 111 mod 1000 is just 111, so that doesn't help. But maybe I can break it down using properties of exponents. Oh, wait, maybe using <PERSON><PERSON><PERSON>'s theorem? <PERSON><PERSON><PERSON>'s theorem says that if two numbers are coprime, then a^φ(n) ≡ 1 mod n, where φ is <PERSON><PERSON><PERSON>'s totient function. So, φ(1000)... let's calculate that. \n\n1000 factors into 2^3 * 5^3, so φ(1000) = 1000 * (1 - 1/2) * (1 - 1/5) = 1000 * 1/2 * 4/5 = 1000 * 2/5 = 400. So φ(1000) is 400. Therefore, if 111 and 1000 are coprime, then 111^400 ≡ 1 mod 1000. \n\nAre they coprime? Let's check. The GCD of 111 and 1000. 111 factors into 3*37, and 1000 is 2^3*5^3, so they share no common factors other than 1. Yes, they are coprime. So <PERSON><PERSON><PERSON>'s theorem applies here. \n\nSo, 111^400 ≡ 1 mod 1000. Therefore, 111^(400*k) ≡ 1^k ≡ 1 mod 1000 for any integer k. Now, 2012 divided by 400: let's see, 400*5 = 2000, so 2012 = 400*5 + 12. Therefore, 111^2012 = (111^400)^5 * 111^12 ≡ 1^5 * 111^12 ≡ 111^12 mod 1000. \n\nSo now the problem reduces to calculating 111^12 mod 1000. That might be manageable with exponentiation by squaring. Let's compute step by step. But maybe even expanding 111^12 directly is too tedious, so let's use the method of successive squaring.\n\nFirst, compute 111^2 mod 1000. 111*111 = 12321. The last three digits are 321. So 111^2 ≡ 321 mod 1000.\n\nThen, 111^4 = (111^2)^2. So compute 321^2 mod 1000. Let's calculate 320^2 = 102400, which is 400 mod 1000. Then 321^2 = (320 + 1)^2 = 320^2 + 2*320*1 + 1 = 102400 + 640 + 1 = 103041. So 103041 mod 1000 is 041. So 111^4 ≡ 41 mod 1000.\n\nNext, 111^8 = (111^4)^2. 41^2 = 1681, so mod 1000 is 681. So 111^8 ≡ 681 mod 1000.\n\nNow, we have 111^12 = 111^8 * 111^4. So, 681 * 41 mod 1000. Let's compute that. 680*40 = 27,200, which is 200 mod 1000. Then 680*1 = 680, and 1*40 = 40, and 1*1=1. Wait, perhaps breaking it down as (680 + 1)*(40 + 1) = 680*40 + 680*1 + 1*40 + 1*1. So 27,200 + 680 + 40 + 1 = 27,200 + 721 = 27,921. 27,921 mod 1000 is 921. But let me verify that 681 * 41. 41*600=24,600; 41*80=3,280; 41*1=41. Adding those: 24,600 + 3,280 = 27,880 + 41 = 27,921. Yes, 27,921 mod 1000 is 921. So 111^12 ≡ 921 mod 1000. \n\nWait, but earlier steps: So according to this, 111^2012 ≡ 111^12 ≡ 921 mod 1000. But is this correct? Let me double-check the calculations because this seems a bit straightforward, but maybe I made a mistake somewhere.\n\nFirst, 111^2 = 12321, so 321 mod 1000 is correct. Then 321^2 = (300 + 21)^2 = 300^2 + 2*300*21 + 21^2 = 90,000 + 12,600 + 441 = 103,041, which is 41 mod 1000. Correct. Then 41^2 is 1681, which is 681 mod 1000. So 111^8 ≡ 681. Then 111^12 is 111^8 * 111^4 = 681 * 41. 681 * 40 = 27,240 and 681 * 1 = 681, so total 27,240 + 681 = 27,921. 27,921 mod 1000 is 921. So that seems right. \n\nBut wait, let me compute 111^12 mod 1000 in another way to confirm. Let's compute each power step by step:\n\n111^1 = 111 mod 1000\n\n111^2 = 111*111 = 12321 → 321\n\n111^3 = 321 * 111. Let's compute that: 300*111=33,300; 21*111=2,331. Sum: 33,300 + 2,331 = 35,631 → 631 mod 1000.\n\n111^4 = 631 * 111. 600*111=66,600; 31*111=3,441. Sum: 66,600 + 3,441 = 70,041 → 41 mod 1000.\n\n111^5 = 41 * 111 = 4,551 → 551 mod 1000.\n\n111^6 = 551 * 111. 500*111=55,500; 50*111=5,550; 1*111=111. Sum: 55,500 + 5,550 = 61,050 + 111 = 61,161 → 161 mod 1000.\n\n111^7 = 161 * 111. 100*111=11,100; 60*111=6,660; 1*111=111. Sum: 11,100 + 6,660 = 17,760 + 111 = 17,871 → 871 mod 1000.\n\n111^8 = 871 * 111. Let's compute: 800*111=88,800; 70*111=7,770; 1*111=111. Total: 88,800 + 7,770 = 96,570 + 111 = 96,681 → 681 mod 1000. Okay, so 111^8 is indeed 681.\n\n111^9 = 681 * 111. Let's do that: 600*111=66,600; 80*111=8,880; 1*111=111. Total: 66,600 + 8,880 = 75,480 + 111 = 75,591 → 591 mod 1000.\n\n111^10 = 591 * 111. 500*111=55,500; 90*111=9,990; 1*111=111. Total: 55,500 + 9,990 = 65,490 + 111 = 65,601 → 601 mod 1000.\n\n111^11 = 601 * 111. 600*111=66,600; 1*111=111. Total: 66,600 + 111 = 66,711 → 711 mod 1000.\n\n111^12 = 711 * 111. 700*111=77,700; 11*111=1,221. Total: 77,700 + 1,221 = 78,921 → 921 mod 1000. \n\nOkay, so both methods give me 921 mod 1000 for 111^12. So that seems correct. Therefore, 111^2012 ≡ 921 mod 1000. But wait, that seems too straightforward. Wait, I thought that using Euler's theorem, since φ(1000)=400, then 111^400 ≡1 mod 1000, so 111^(400*5 + 12) = (111^400)^5 * 111^12 ≡1^5*111^12≡111^12. Then, according to the previous steps, 111^12 mod 1000 is 921. Therefore, the last three digits should be 921. \n\nBut wait, let me check with another approach. Maybe using the Chinese Remainder Theorem. Since 1000 = 8 * 125, and 8 and 125 are coprime. So if I can find 111^2012 mod 8 and mod 125, then combine them using the Chinese Remainder Theorem. Let's try this as a cross-check.\n\nFirst, compute 111^2012 mod 8. Since 111 mod 8 is 7 (since 8*13=104; 111-104=7). So 7^2012 mod 8. But note that 7 ≡ -1 mod 8. So (-1)^2012 = 1. So 7^2012 ≡1 mod 8.\n\nNext, compute 111^2012 mod 125. For this, since 111 and 125 are coprime (since 111 is 3*37, and 125 is 5^3), Euler's theorem can be applied here as well. φ(125)=125*(1-1/5)=100. Therefore, 111^100 ≡1 mod 125. Then, 2012 divided by 100 is 20 with remainder 12, so 111^2012=(111^100)^20 *111^12≡1^20 *111^12≡111^12 mod 125.\n\nSo we need to compute 111^12 mod 125. Let's compute 111 mod 125 is 111. Compute 111^2: 111*111=12321. 12321 mod 125. Let's divide 12321 by 125. 125*98=12250, so 12321-12250=71. So 111^2 ≡71 mod 125.\n\nThen 111^4=(111^2)^2=71^2=5041. 5041 mod 125. 125*40=5000, so 5041-5000=41. So 111^4 ≡41 mod 125.\n\n111^8=(111^4)^2=41^2=1681. 1681 mod 125: 125*13=1625, 1681-1625=56. So 111^8≡56 mod 125.\n\nThen 111^12=111^8 *111^4=56*41. 56*40=2240, 56*1=56. 2240+56=2296. 2296 mod 125: 125*18=2250, 2296-2250=46. So 111^12≡46 mod 125.\n\nSo now we have that 111^2012 ≡1 mod 8 and ≡46 mod 125. Now we need to find a number x such that x ≡1 mod 8 and x≡46 mod 125. Then x mod 1000 will be the answer.\n\nLet's solve these congruences. Let x=125k +46 for some integer k. Then, we need 125k +46 ≡1 mod 8. 125 mod 8 is 5 (since 8*15=120, 125-120=5), so 5k +46 ≡1 mod 8. 46 mod 8 is 6 (since 8*5=40, 46-40=6). So 5k +6 ≡1 mod 8 →5k ≡ -5 mod 8 →5k≡3 mod 8. To solve 5k≡3 mod 8. Multiply both sides by the inverse of 5 mod 8. The inverse of 5 mod8 is 5, because 5*5=25≡1 mod8. So k≡3*5=15≡7 mod8. Thus, k=8m +7 for some integer m. Therefore, x=125*(8m +7) +46=1000m +875 +46=1000m +921. Therefore, x≡921 mod1000. So the last three digits are 921. \n\nSo both methods give 921. So that must be the correct answer. Therefore, the last three digits of 111^2012 are 921.\n</think>To find the last three digits of \\(111^{2012}\\), we need to compute \\(111^{2012} \\mod 1000\\). We will use Euler's theorem and the Chinese Remainder Theorem for this purpose.\n\n### Step 1: Euler's Theorem\nEuler's theorem states that if \\(a\\) and \\(n\\) are coprime, then \\(a^{\\phi(n)} \\equiv 1 \\mod n\\), where \\(\\phi\\) is the Euler's totient function.\n\nFor \\(n = 1000\\):\n\\[\n\\phi(1000) = 1000 \\left(1 - \\frac{1}{2}\\right)\\left(1 - \\frac{1}{5}\\right) = 1000 \\cdot \\frac{1}{2} \\cdot \\frac{4}{5} = 400\n\\]\nSince \\(111\\) and \\(1000\\) are coprime, we have:\n\\[\n111^{400} \\equiv 1 \\mod 1000\n\\]\nThus,\n\\[\n111^{2012} = 111^{400 \\cdot 5 + 12} = (111^{400})^5 \\cdot 111^{12} \\equiv 1^5 \\cdot 111^{12} \\equiv 111^{12} \\mod 1000\n\\]\n\n### Step 2: Successive Squaring\nWe now compute \\(111^{12} \\mod 1000\\) using successive squaring:\n\\[\n111^2 = 12321 \\equiv 321 \\mod 1000\n\\]\n\\[\n111^4 = (111^2)^2 = 321^2 = 103041 \\equiv 41 \\mod 1000\n\\]\n\\[\n111^8 = (111^4)^2 = 41^2 = 1681 \\equiv 681 \\mod 1000\n\\]\n\\[\n111^{12} = 111^8 \\cdot 111^4 = 681 \\cdot 41 = 27921 \\equiv 921 \\mod 1000\n\\]\n\n### Step 3: Chinese Remainder Theorem\nTo verify, we use the Chinese Remainder Theorem by finding \\(111^{2012} \\mod 8\\) and \\(111^{2012} \\mod 125\\).\n\n#### Modulo 8:\n\\[\n111 \\equiv 7 \\mod 8\n\\]\n\\[\n7^{2012} \\equiv (-1)^{2012} \\equiv 1 \\mod 8\n\\]\n\n#### Modulo 125:\n\\[\n\\phi(125) = 125 \\left(1 - \\frac{1}{5}\\right) = 100\n\\]\n\\[\n111^{2012} = 111^{100 \\cdot 20 + 12} = (111^{100})^20 \\cdot 111^{12} \\equiv 1^{20} \\cdot 111^{12} \\equiv 111^{12} \\mod 125\n\\]\nWe compute \\(111^{12} \\mod 125\\):\n\\[\n111^2 = 12321 \\equiv 71 \\mod 125\n\\]\n\\[\n111^4 = 71^2 = 5041 \\equiv 41 \\mod 125\n\\]\n\\[\n111^8 = 41^2 = 1681 \\equiv 56 \\mod 125\n\\]\n\\[\n111^{12} = 111^8 \\cdot 111^4 = 56 \\cdot 41 = 2296 \\equiv 46 \\mod 125\n\\]\n\n#### Combining Results:\nWe need \\(x\\) such that:\n\\[\nx \\equiv 1 \\mod 8\n\\]\n\\[\nx \\equiv 46 \\mod 125\n\\]\n\nSolving these congruences:\nLet \\(x = 125k + 46\\). Then:\n\\[\n125k + 46 \\equiv 1 \\mod 8\n\\]\n\\[\n5k + 6 \\equiv 1 \\mod 8 \\implies 5k \\equiv -5 \\mod 8 \\implies 5k \\equiv 3 \\mod 8\n\\]\nMultiplying both sides by the inverse of 5 mod 8 (which is 5):\n\\[\nk \\equiv 15 \\equiv 7 \\mod 8\n\\]\nThus, \\(k = 8m + 7\\). Substituting back:\n\\[\nx = 125(8m + 7) + 46 = 1000m + 875 + 46 = 1000m + 921\n\\]\nTherefore, \\(x \\equiv 921 \\mod 1000\\).\n\n### Final Answer\nThe last three digits of \\(111^{2012}\\) are \\(\\boxed{921}\\)."}