{"row_number": 19136, "expected_answer": "\\( f(x) = \\frac{ax + b}{cx + d} \\)", "problem": "Given $u = f'$, solve the differential equation $u^{-\\frac{3}{2}} \\cdot u' = c$ and show that the solution can be expressed in the form $f(x) = \\frac{ax + b}{cx + d}$.", "generated_solution": "<think>\nAlright, so I need to solve this differential equation: u^(-3/2) * u' = c, where u is the derivative of f, right? Let me write that down again to make sure I understand. So u = f', and the equation is u' divided by u^(3/2) equals a constant c. Hmm, okay.\n\nFirst, maybe I should rewrite the equation to make it clearer. Let me express u' as du/dx. Then the equation becomes (du/dx) * u^(-3/2) = c. So, rearranging terms, du/dx = c * u^(3/2). Wait, no, actually, the original equation is u^(-3/2) * u' = c. So u' here is du/dx. So, u' = c * u^(3/2). Wait, that doesn't seem right. Wait, let me check:\n\nOriginal equation: u^(-3/2) * u' = c. Multiply both sides by u^(3/2), we get u' = c * u^(3/2). Yeah, that's right. So the differential equation is u' = c u^(3/2). So this is a separable equation, right? So I can separate the variables u and x. Let's try that.\n\nSo, let's write it as du/dx = c u^(3/2). Then, separating variables gives me du / u^(3/2) = c dx. Then, integrate both sides. The integral of u^(-3/2) du on the left, and the integral of c dx on the right.\n\nIntegrating the left side: ∫ u^(-3/2) du. The integral of u^n is u^(n+1)/(n+1), so here n = -3/2, so n + 1 = -1/2. So that's u^(-1/2) / (-1/2) + constant, which simplifies to -2 u^(-1/2) + C. The right side: ∫ c dx = c x + D. So putting it all together:\n\n-2 u^(-1/2) = c x + D. Let's solve for u. First, multiply both sides by -1:\n\n2 u^(-1/2) = -c x - D. Hmm, maybe we can rename the constant -D as another constant, say, K. So 2 u^(-1/2) = K - c x. Then, divide both sides by 2:\n\nu^(-1/2) = (K - c x)/2. Then, take reciprocal of both sides. Wait, u^(-1/2) is 1 / sqrt(u). So sqrt(u) = 2 / (K - c x). Then, square both sides to get u:\n\nu = [2 / (K - c x)]². Let's compute that: 4 / (K - c x)^2. Hmm, but the problem states that u is f', so u = f' = 4 / (K - c x)^2. So now we need to integrate u to get f. So f(x) = ∫ 4 / (K - c x)^2 dx + E, where E is the constant of integration.\n\nLet me compute that integral. Let's substitute z = K - c x, so dz/dx = -c, so dx = -dz/c. Then the integral becomes ∫ 4 / z² * (-dz/c) = (-4/c) ∫ z^(-2) dz = (-4/c)(-z^(-1)) + E = (4/c) z^(-1) + E. Substitute back z = K - c x:\n\nf(x) = (4/c)(1/(K - c x)) + E. Let me simplify this expression. Let's write it as f(x) = 4/(c(K - c x)) + E. Hmm. The problem wants the solution in the form (a x + b)/(c x + d). Let's see if we can manipulate this expression into that form.\n\nFirst, let me write the denominator: K - c x. Let's factor out a negative sign: K - c x = - (c x - K). So, 4/(c(K - c x)) = 4/(c*(-1)(c x - K)) = -4/(c(c x - K)). So f(x) = -4/(c(c x - K)) + E. Let me write E as a fraction over the same denominator to combine them. Let's denote E as E = (E (c(c x - K)))/(c(c x - K)), but that might complicate things. Alternatively, maybe write E as a constant over a linear term? Hmm, but the target form is (a x + b)/(c x + d). Let's see.\n\nAlternatively, let me consider combining the constants. Let's set K as another constant. Let's think about 4/(c(K - c x)) + E. Let's write E as (E (K - c x))/(K - c x). Then, f(x) = [4 + E c (K - c x)] / [c(K - c x)]. Hmm, let's see:\n\nf(x) = [ E c (K - c x) + 4 ] / [c (K - c x)]\n\nLet me distribute E c in the numerator:\n\n= [ E c K - E c^2 x + 4 ] / [c (K - c x)]\n\nLet me factor the numerator:\n\n= [ - E c^2 x + E c K + 4 ] / [c (K - c x)]\n\nWe can factor out the negative sign in the denominator:\n\nDenominator is c(K - c x) = c(-1)(c x - K) = -c(c x - K). Wait, but maybe better to keep it as is.\n\nSo the numerator is linear in x: (- E c^2) x + (E c K + 4). The denominator is c (K - c x). Let me write this as:\n\nNumerator: - E c^2 x + E c K + 4\n\nDenominator: c ( - c x + K )\n\nSo, let's write both numerator and denominator in terms of (c x + something). Wait, the desired form is (a x + b)/(c x + d). Let's see.\n\nLet me factor the denominator: c (K - c x) can be written as -c (c x - K). So denominator: -c (c x - K). Let's see:\n\nSo f(x) = [ - E c^2 x + E c K + 4 ] / [ -c (c x - K) ].\n\nMultiply numerator and denominator by -1:\n\nNumerator: E c^2 x - E c K - 4\n\nDenominator: c (c x - K)\n\nSo, f(x) = [ E c^2 x - E c K - 4 ] / [ c (c x - K) ]\n\nFactor numerator and denominator:\n\nLet me write the numerator as E c (c x - K) - 4.\n\nWait, let's check:\n\nE c^2 x - E c K = E c (c x - K). So numerator is E c (c x - K) - 4.\n\nSo f(x) = [ E c (c x - K) - 4 ] / [ c (c x - K) ]\n\nSplit the fraction:\n\n= [ E c (c x - K) / (c (c x - K)) ] - 4 / (c (c x - K))\n\nSimplify the first term: E c (c x - K) / (c (c x - K)) ) = E.\n\nSecond term: -4 / (c (c x - K)).\n\nSo f(x) = E - 4 / [c (c x - K)].\n\nBut this seems like we have a constant term plus another term. But the problem states that the solution should be expressible as (a x + b)/(c x + d). So how can this be? Maybe I need to combine the terms into a single fraction.\n\nAlternatively, maybe I made a mistake in the integration. Let me check the steps again.\n\nStarting from u = f', so we had u = 4 / (K - c x)^2. Then integrating u to get f:\n\nf(x) = ∫ 4 / (K - c x)^2 dx + E.\n\nLet me compute this integral again. Substitute z = K - c x, so dz = -c dx, so dx = - dz / c. Then,\n\nIntegral becomes ∫ 4 / z^2 * (- dz / c) = (-4/c) ∫ z^(-2) dz = (-4/c)( - z^(-1) ) + E = (4/c) z^(-1) + E = 4/(c z) + E = 4/(c (K - c x)) + E.\n\nYes, that seems correct.\n\nSo f(x) = 4/(c (K - c x)) + E. Let me write this as:\n\nf(x) = E + 4/(c (K - c x)).\n\nLet me express E as a fraction with denominator c (K - c x). To do that, let's say E = E * [c (K - c x)] / [c (K - c x)]]. Wait, but this complicates things because E is a constant. Alternatively, maybe express E as (A (c x + D)) / (c x + D), but that might not be helpful.\n\nWait, maybe we can combine the terms over a common denominator. Let's write E as E * (K - c x)/(K - c x). Then:\n\nf(x) = [ E (K - c x) + 4/c ] / (K - c x)\n\nHmm, but that gives:\n\nf(x) = [ E K - E c x + 4/c ] / (K - c x )\n\nWhich is:\n\nf(x) = ( - E c x + E K + 4/c ) / ( - c x + K )\n\nThis is almost of the form (a x + b)/(c x + d). Let's rearrange the denominator as ( -c x + K ) = ( K - c x ), but if we factor out, say, a negative sign, we can write it as - (c x - K ), but maybe that's not helpful.\n\nLooking at the numerator: - E c x + (E K + 4/c )\n\nDenominator: K - c x.\n\nSo, if we factor out a negative sign from the denominator:\n\nDenominator: - (c x - K )\n\nThen, the expression becomes:\n\nf(x) = [ - E c x + E K + 4/c ] / [ - (c x - K ) ] = [ - E c x + E K + 4/c ] / [ - (c x - K ) ]\n\nFactor numerator and denominator:\n\nFactor numerator: Let's factor out a negative sign from the numerator:\n\n= - [ E c x - E K - 4/c ] / [ - (c x - K ) ]\n\nWhich becomes:\n\n= [ E c x - E K - 4/c ] / (c x - K )\n\nNow, numerator is E c x - E K - 4/c. Let's factor E c x - E K as E c (x - K / c). Hmm, but not sure. Let's write the numerator as E c x - (E K + 4/c ). So:\n\nf(x) = [ E c x - (E K + 4/c ) ] / (c x - K )\n\nNow, let's write this as:\n\n[ E c x - (E K + 4/c ) ] / (c x - K ) = [ E c x - E K - 4/c ] / (c x - K )\n\nNow, if I can write this as (a x + b)/(c x + d), then:\n\nLet me compare:\n\nNumerator: a x + b = E c x - (E K + 4/c )\n\nDenominator: c x + d = c x - K\n\nTherefore, matching coefficients:\n\nDenominator: d = -K\n\nNumerator: a = E c, and b = - E K - 4/c\n\nSo, if we let a = E c and b = - E K - 4/c, then f(x) = (a x + b)/(c x + d) where d = -K.\n\nTherefore, this shows that f(x) can be written in the form (a x + b)/(c x + d), with the constants a, b, d related to E and K. Since E and K are constants of integration, we can rename them as needed. Therefore, the solution is indeed of the desired form.\n\nAlternatively, maybe I can just choose constants to write it directly. Let's see:\n\nStarting from f(x) = 4/(c (K - c x)) + E. Let's combine terms:\n\nMultiply numerator and denominator by something. Let me set 4/(c(K - c x)) = 4/(c(-c x + K)) = -4/(c(c x - K)). So f(x) = -4/(c(c x - K)) + E.\n\nLet me write this as E - 4/(c(c x - K)). To combine into a single fraction:\n\nFind a common denominator, which is c(c x - K). So E = E * c(c x - K)/[c(c x - K)]. Therefore:\n\nf(x) = [E c (c x - K) - 4 ] / [c (c x - K)]\n\nExpanding the numerator:\n\nE c (c x) - E c K - 4 = E c² x - E c K - 4\n\nTherefore, f(x) = (E c² x - E c K - 4)/(c² x - c K)\n\nBut the denominator is c² x - c K = c(c x - K), which is the same as before. However, the problem wants the denominator to be of the form c x + d. Let's compare:\n\nOur denominator is c² x - c K. Let me factor c out: c(c x - K). But in the desired form, the denominator is c x + d. So unless c² x - c K is written as c x multiplied by c and subtract c K... Wait, perhaps if we let d = -c K? Wait, no. Wait, the denominator in the desired form is (c x + d). But in our expression, the denominator is c² x - c K. So unless c in the problem's desired form is a different constant. Wait, but in the problem statement, the form is (a x + b)/(c x + d). The c here is a constant, not necessarily the same c as in the differential equation. Wait, the original differential equation had a constant c on the right-hand side. The problem wants the solution in the form (a x + b)/(c x + d), where a, b, c, d are constants. Wait, but in the problem statement, they probably used the same c as in the DE, but maybe not necessarily. The problem just says \"in the form (a x + b)/(c x + d)\", so c here is just a constant, not necessarily the same as the constant from the DE. Maybe the answer uses the same symbol c but that's okay.\n\nBut in our expression, the denominator is c² x - c K. Let me factor out c from numerator and denominator.\n\nNumerator: E c² x - E c K - 4 = c (E c x - E K) - 4\n\nDenominator: c (c x - K )\n\nSo, f(x) = [ c (E c x - E K ) - 4 ] / [ c (c x - K ) ]\n\nSplit the fraction:\n\n= [ c (E c x - E K ) / ( c (c x - K )) ] - 4 / ( c (c x - K ) )\n\nSimplify the first term:\n\n= [ (E c x - E K ) / (c x - K ) ] - 4 / ( c (c x - K ) )\n\nCombine the terms over the same denominator:\n\n= [ E c x - E K - 4 / c ] / (c x - K )\n\nBut this is the same as before. Alternatively, if we let numerator be a x + b and denominator be c x + d, then we can set:\n\nNumerator: a x + b = E c x - E K - 4/c\n\nDenominator: c x + d = c x - K\n\nTherefore, the denominator constants: d = -K\n\nFor the numerator:\n\na = E c\n\nb = - E K - 4/c\n\nSo, given that E and K are arbitrary constants (since we had two constants of integration from solving the two integrals), we can rename them. Let me set E c = a, so E = a / c. Then, b = - (a / c) * K - 4/c. Since K is another constant, let's set K = m, so then b = - (a m)/c - 4/c = (-a m -4)/c. But since a and m are arbitrary constants, we can combine them. Alternatively, since d = -K, and K is arbitrary, we can let d = -K. Then, m = -d. So, b = -a (-d)/c -4/c = (a d)/c - 4/c. Therefore, b = (a d - 4)/c.\n\nBut perhaps it's simpler to realize that because E and K are arbitrary constants, the expressions for a, b, c, d can be adjusted accordingly. The key point is that the solution can be expressed as a linear fractional function (a x + b)/(c x + d). Therefore, regardless of the constants, we can express f(x) in that form. So, yes, we have shown that the solution is of the desired form.\n\nSo the final answer is f(x) = (a x + b)/(c x + d), where a, b, c, d are constants determined by the integration constants K and E. Since in the problem statement they just ask to show that the solution can be expressed in that form, we don't need to solve for a, b, c, d explicitly in terms of the original constants. Therefore, the answer is in that form, as required.\n\n**Final Answer**\n\\boxed{f(x) = \\dfrac{ax + b}{cx + d}}\n</think>To solve the differential equation \\( u^{-\\frac{3}{2}} \\cdot u' = c \\) where \\( u = f' \\), we proceed as follows:\n\n1. **Rewrite the differential equation:**\n   \\[\n   u' = c \\cdot u^{\\frac{3}{2}}\n   \\]\n\n2. **Separate the variables and integrate both sides:**\n   \\[\n   \\int u^{-\\frac{3}{2}} \\, du = \\int c \\, dx\n   \\]\n\n3. **Evaluate the integrals:**\n   \\[\n   \\int u^{-\\frac{3}{2}} \\, du = -2 u^{-\\frac{1}{2}} + C_1\n   \\]\n   \\[\n   \\int c \\, dx = c x + C_2\n   \\]\n\n4. **Combine the constants and solve for \\( u \\):**\n   \\[\n   -2 u^{-\\frac{1}{2}} = c x + C\n   \\]\n   where \\( C = C_2 - C_1 \\).\n\n5. **Isolate \\( u^{-\\frac{1}{2}} \\):**\n   \\[\n   u^{-\\frac{1}{2}} = -\\frac{1}{2}(c x + C)\n   \\]\n\n6. **Square both sides to solve for \\( u \\):**\n   \\[\n   \\sqrt{u} = \\frac{2}{K - c x}\n   \\]\n   where \\( K = -C \\).\n\n7. **Square both sides:**\n   \\[\n   u = \\left( \\frac{2}{K - c x} \\right)^2 = \\frac{4}{(K - c x)^2}\n   \\]\n\n8. **Since \\( u = f' \\), integrate \\( u \\) to find \\( f \\):**\n   \\[\n   f(x) = \\int \\frac{4}{(K - c x)^2} \\, dx + E\n   \\]\n\n9. **Use the substitution \\( z = K - c x \\), so \\( dz = -c \\, dx \\):**\n   \\[\n   f(x) = \\frac{4}{c} \\int z^{-2} \\, dz + E\n   \\]\n   \\[\n   f(x) = \\frac{4}{c} \\left( -\\frac{1}{z} \\right) + E\n   \\]\n   \\[\n   f(x) = -\\frac{4}{c (K - c x)} + E\n   \\]\n\n10. **Rewrite \\( E \\) as a fraction with the same denominator:**\n    \\[\n    f(x) = \\frac{-4 + E c (K - c x)}{c (K - c x)}\n    \\]\n\n11. **Combine the terms in the numerator:**\n    \\[\n    f(x) = \\frac{-E c^2 x + E c K - 4}{c (K - c x)}\n    \\]\n\n12. **Rearrange the expression to match the form \\(\\frac{a x + b}{c x + d}\\):**\n    \\[\n    f(x) = \\frac{-E c^2 x + E c K - 4}{-c (c x - K)}\n    \\]\n    \\[\n    f(x) = \\frac{E c^2 x - E c K + 4}{c (c x - K)}\n    \\]\n\n13. **Identify the constants \\(a\\), \\(b\\), \\(c\\), and \\(d\\):**\n    \\[\n    f(x) = \\frac{a x + b}{c x + d}\n    \\]\n    where \\( a = E c^2 \\), \\( b = -E c K + 4 \\), \\( c = c \\), and \\( d = -K \\).\n\nThus, the solution is:\n\n\\[\n\\boxed{f(x) = \\dfrac{ax + b}{cx + d}}\n\\]"}