{"row_number": 6270, "expected_answer": "\\( n(n-1) \\)", "problem": "Find the expression for the $n$th term of the sequence $0, 2, 6, 12, 20, \\dots$.", "generated_solution": "<think>\nOkay, so I need to find the nth term of the sequence 0, 2, 6, 12, 20, and so on. Let me start by looking at the differences between the terms to see if there's a pattern there. \n\nThe first term is 0. The second term is 2, so the difference between the first and second term is 2 - 0 = 2. Then from 2 to 6, that's a difference of 4. From 6 to 12, the difference is 6, and from 12 to 20, it's 8. So the differences are 2, 4, 6, 8... Hmm, those are even numbers increasing by 2 each time. That looks like the sequence of even numbers starting from 2. So the first difference is 2×1, then 2×2, 2×3, 2×4, etc. \n\nIf I consider the differences between the terms, maybe the nth term is related to the sum of these differences. Let's test that. Let's see:\n\nThe first term is 0. Let's think of it as the sum up to the zeroth difference? Or maybe the second term is the first term plus 2. Let's break it down:\n\nTerm 1: 0  \nTerm 2: 0 + 2 = 2  \nTerm 3: 2 + 4 = 6  \nTerm 4: 6 + 6 = 12  \nTerm 5: 12 + 8 = 20  \n\nSo each term is the previous term plus 2 times the position. Wait, for the second term, the difference is 2, which is 2×1. For the third term, the difference is 4=2×2, then 6=2×3, etc. So the difference between the nth term and the (n-1)th term is 2(n-1). Wait, let's check:\n\nTerm 2 - Term 1 = 2 = 2×1 (since n=2, so n-1=1)  \nTerm 3 - Term 2 = 4 = 2×2 (n=3, n-1=2)  \nYes, that works. So the difference between term n and term n-1 is 2(n-1). Therefore, to get the nth term, we can start from term 1 and add up all the differences up to n. \n\nSo the nth term would be the sum from k=1 to k=n-1 of 2k. Wait, let me check:\n\nIf n=2, the sum from k=1 to 1 of 2k is 2×1=2. Adding to term 1 (0) gives 2. Correct.\n\nFor n=3, sum from k=1 to 2 of 2k: 2×1 + 2×2=2+4=6. Starting from term 1 (0) + 6 gives 6. But wait, term 3 is 6. Hmm, but in the sequence, term 1 is 0, term 2 is 2, term 3 is 6. So actually, the nth term is the sum from k=1 to k=(n-1) of 2k. Because the first term (n=1) is 0, which is the sum from k=1 to 0, which is 0. That makes sense.\n\nBut wait, the sum from k=1 to k=m of 2k is 2 times the sum of the first m natural numbers. The formula for the sum of first m natural numbers is m(m+1)/2. So if we multiply by 2, that's 2*(m(m+1)/2) = m(m+1). \n\nSo the sum from k=1 to m of 2k is m(m+1). In our case, m = n-1. Therefore, the nth term is (n-1)(n). So the nth term is n(n-1). Let's check:\n\nn=1: 1×0=0 ✔️  \nn=2: 2×1=2 ✔️  \nn=3: 3×2=6 ✔️  \nn=4: 4×3=12 ✔️  \nn=5:5×4=20 ✔️  \nYes, that works. So the nth term is n(n-1).\n\nWait, but let me check another way. Let me consider the sequence and see if there's another pattern. The terms are 0, 2, 6, 12, 20... Let's see if they can be related to squares or something else. Let's see:\n\n0 = 0  \n2 = 2  \n6 = 6  \n12 = 12  \n20 = 20  \nHmm, not obvious. But if I think about n squared minus n: n² - n. Let's check for n=1: 1-1=0 ✔️. n=2:4-2=2 ✔️. n=3:9-3=6 ✔️. n=4:16-4=12 ✔️. n=5:25-5=20 ✔️. Yes, that works too. So that's the same as n(n-1). Because n(n-1) is n² - n. So both ways of writing it. So the nth term is n(n-1).\n\nAlternatively, maybe there's another way to approach it, but I think this is correct. Let me confirm once more with another term. Let's compute term 6. If the pattern continues, the next difference would be 10 (since 2,4,6,8,10...), so term 6 is 20 +10=30. Using the formula: 6×5=30. Correct. So yes, it works. Therefore, the nth term is n(n-1).\n</think>To find the expression for the \\( n \\)th term of the sequence \\( 0, 2, 6, 12, 20, \\dots \\), we start by examining the differences between consecutive terms:\n\n\\[\n\\begin{align*}\n\\text{Term 2} - \\text{Term 1} &= 2 - 0 = 2, \\\\\n\\text{Term 3} - \\text{Term 2} &= 6 - 2 = 4, \\\\\n\\text{Term 4} - \\text{Term 3} &= 12 - 6 = 6, \\\\\n\\text{Term 5} - \\text{Term 4} &= 20 - 12 = 8.\n\\end{align*}\n\\]\n\nThe differences form the sequence \\( 2, 4, 6, 8, \\dots \\), which are even numbers increasing by 2 each time. This can be expressed as \\( 2 \\times 1, 2 \\times 2, 2 \\times 3, 2 \\times 4, \\dots \\).\n\nTo find the \\( n \\)th term of the original sequence, we need to sum these differences up to the \\( (n-1) \\)th term. The \\( k \\)th difference is \\( 2k \\), so the \\( n \\)th term can be written as the sum of the first \\( (n-1) \\) terms of the sequence \\( 2k \\):\n\n\\[\n\\text{Term}_n = \\sum_{k=1}^{n-1} 2k.\n\\]\n\nWe know that the sum of the first \\( m \\) natural numbers is given by the formula:\n\n\\[\n\\sum_{k=1}^m k = \\frac{m(m+1)}{2}.\n\\]\n\nTherefore, the sum of the first \\( (n-1) \\) terms of \\( 2k \\) is:\n\n\\[\n\\sum_{k=1}^{n-1} 2k = 2 \\sum_{k=1}^{n-1} k = 2 \\left( \\frac{(n-1)n}{2} \\right) = (n-1)n.\n\\]\n\nThus, the \\( n \\)th term of the sequence is:\n\n\\[\n\\boxed{n(n-1)}.\n\\]\n\nWe can verify this by checking a few terms:\n- For \\( n = 1 \\): \\( 1(1-1) = 0 \\),\n- For \\( n = 2 \\): \\( 2(2-1) = 2 \\),\n- For \\( n = 3 \\): \\( 3(3-1) = 6 \\),\n- For \\( n = 4 \\): \\( 4(4-1) = 12 \\),\n- For \\( n = 5 \\): \\( 5(5-1) = 20 \\).\n\nAll these match the given sequence, confirming that the \\( n \\)th term is indeed \\( n(n-1) \\)."}