{"row_number": 6258, "expected_answer": "\\( \\frac{1}{2\\sqrt{2}} \\)", "problem": "Find the radius of convergence of the power series\n\n\\[ \\sum_{n=1}^{\\infty} 2^n \\sqrt[4]{\\frac{(2n-1)!}{(n!)^2}} x^n \\]", "generated_solution": "<think>\nOkay, so I need to find the radius of convergence for this power series: the sum from n=1 to infinity of 2^n times the fourth root of [(2n-1)! divided by (n!)^2], all multiplied by x^n. Hmm, right. Radius of convergence usually involves the ratio test or the root test. Let me recall that the radius of convergence R is 1 over the limit as n approaches infinity of the nth root of |a_n|, using the root test. Or, using the ratio test, it's the limit of |a_n/a_{n+1}| as n approaches infinity. I wonder which one would be easier here. The terms involve factorials, which might get messy with the ratio test because of the (2n-1)! and (n!)^2. But maybe the root test is manageable. Let's see.\n\nFirst, let's write the general term a_n as 2^n times [(2n-1)!/(n!)^2]^{1/4}. So the coefficient a_n is 2^n multiplied by the fourth root of (2n-1)! divided by (n!)^2. To find the radius of convergence, I can use the formula R = 1 / lim sup_{n→∞} |a_n|^{1/n}. Alternatively, using the ratio test, R = lim |a_n / a_{n+1}|. Let's try both and see which is easier.\n\nStarting with the root test: Take |a_n|^{1/n}, which would be (2^n)^{1/n} times [(2n-1)!/(n!)^2]^{1/(4n)}. Simplifying the first part, (2^n)^{1/n} is 2. Then, the second part is [(2n-1)!/(n!^2)]^{1/(4n)}. So we need to evaluate the limit as n approaches infinity of 2 times [(2n-1)!/(n!^2)]^{1/(4n)}. Then R would be 1 divided by that limit. Hmm, factorials in the limit. Maybe Stirling's approximation would help here. Stirling's formula approximates n! as n^n e^{-n} sqrt(2πn). So maybe I can approximate the factorial terms using Stirling's formula.\n\nLet me write Stirling's formula for (2n-1)! and n!:\n\n(2n-1)! ≈ (2n-1)^{2n-1} e^{-(2n-1)} sqrt(2π(2n-1))\n\nn! ≈ n^n e^{-n} sqrt(2πn)\n\nSo substituting these into the expression (2n-1)!/(n!)^2:\n\nNumerator: (2n-1)^{2n-1} e^{-(2n-1)} sqrt(2π(2n-1))\n\nDenominator: [n^n e^{-n} sqrt(2πn)]^2 = n^{2n} e^{-2n} (2πn)\n\nSo dividing numerator by denominator:\n\n[(2n-1)^{2n-1} / n^{2n}] * [e^{-(2n-1)} / e^{-2n}] * [sqrt(2π(2n-1)) / (2πn)]\n\nSimplify each part:\n\nFirst part: (2n-1)^{2n-1} / n^{2n} = [ (2n -1)/n ]^{2n -1} * (1/n)\n\nWhich is [2 - 1/n]^{2n -1} * (1/n)\n\nSecond part: e^{-(2n -1) + 2n} = e^{1} = e\n\nThird part: sqrt(2π(2n -1)) / (2πn) ≈ sqrt(4πn) / (2πn) for large n, since 2n -1 ≈ 2n. So sqrt(4πn) is 2 sqrt(πn), so the third part is (2 sqrt(πn)) / (2πn) ) = 1 / sqrt(πn)\n\nSo putting all together:\n\n[ (2 - 1/n)^{2n -1} * (1/n) ] * e * (1 / sqrt(πn)) )\n\nAs n approaches infinity, (2 - 1/n)^{2n -1} ≈ (2 - 1/n)^{2n} ≈ (2)^{2n} * [1 - 1/(2n)]^{2n} ≈ 2^{2n} * e^{-1}, using the limit (1 - k/n)^n → e^{-k}.\n\nWait, let me check that step. If we have (2 - 1/n)^{2n}, can we write it as [2(1 - 1/(2n))]^{2n} = 2^{2n} (1 - 1/(2n))^{2n} → 2^{2n} e^{-1}.\n\nYes, because (1 - a/(n))^{n} → e^{-a} as n→∞. So here a = 1, so (1 - 1/(2n))^{2n} → e^{-1}. So the first part becomes approximately 2^{2n} * e^{-1} * (1/n), but wait, the exponent was 2n -1, but perhaps for large n, the difference between 2n -1 and 2n is negligible in the exponential term. Hmm. Wait, let me re-examine:\n\nOriginal first part: [ (2n -1)/n ]^{2n -1} * (1/n) = [2 - 1/n]^{2n -1} * (1/n). Let's approximate [2 - 1/n]^{2n -1}. Let me take logarithm:\n\nln([2 - 1/n]^{2n -1}) = (2n -1) ln(2 - 1/n) ≈ (2n -1)(ln 2 - 1/(2n)) using the expansion ln(2 - 1/n) ≈ ln 2 - 1/(2n) for large n. Because ln(2 - 1/n) = ln[2(1 - 1/(2n))] = ln 2 + ln(1 - 1/(2n)) ≈ ln 2 - 1/(2n) - [1/(2n)]^2 /2 + ... So up to the first order, ln(2 - 1/n) ≈ ln 2 - 1/(2n). Then:\n\n(2n -1)(ln 2 - 1/(2n)) = (2n)(ln2) - (ln2) - (2n -1)/(2n)\n\n≈ 2n ln2 - ln2 - (1 - 1/(2n)) ≈ 2n ln2 - ln2 -1 + 1/(2n)\n\nSo exponentiating gives e^{2n ln2 - ln2 -1 + 1/(2n)} = e^{2n ln2} * e^{-ln2} * e^{-1} * e^{1/(2n)} ≈ (2^{2n}) * (1/2) * e^{-1} * 1, since e^{1/(2n)} approaches 1 as n→∞. Therefore, the first part [2 - 1/n]^{2n -1} ≈ 2^{2n} * (1/(2e)). So then the first term becomes approximately 2^{2n}/(2e) * (1/n).\n\nSo putting back into the expression:\n\nFirst part: 2^{2n}/(2e n)\n\nSecond part: e\n\nThird part: 1/sqrt(πn)\n\nSo multiplying all together: (2^{2n}/(2e n)) * e * (1/sqrt(πn)) ) = (2^{2n} / (2n)) * (1/sqrt(πn)) )\n\nSimplify: 2^{2n}/(2n) * 1/(sqrt(πn)) ) = 2^{2n}/(2 n^{3/2} sqrt(π)) ) = (4^n)/(2 sqrt(π) n^{3/2})\n\nTherefore, the expression (2n -1)! / (n!)^2 ≈ (4^n)/(2 sqrt(π) n^{3/2})\n\nBut we had [(2n -1)!/(n!)^2]^{1/4} in the original a_n. So taking the fourth root:\n\n[ (4^n)/(2 sqrt(π) n^{3/2}) ]^{1/4} = (4^n)^{1/4} / [ (2 sqrt(π) n^{3/2} )^{1/4} ]\n\nWhich simplifies to:\n\n4^{n/4} / [ 2^{1/4} (π)^{1/8} n^{3/8} ) ]\n\nSince 4^{1/4} is 2^{1/2}, so 4^{n/4} is 2^{n/2}. Therefore:\n\n2^{n/2} / (2^{1/4} π^{1/8} n^{3/8}) ) = 2^{n/2 -1/4} π^{-1/8} n^{-3/8}\n\nSo then a_n is 2^n times this expression, right? Because the original a_n is 2^n multiplied by the fourth root of [(2n-1)!/(n!)^2]. So:\n\na_n = 2^n * [2^{n/2 -1/4} π^{-1/8} n^{-3/8}] = 2^{n + n/2 -1/4} π^{-1/8} n^{-3/8} = 2^{(3n/2 -1/4)} π^{-1/8} n^{-3/8}\n\nSimplify exponents: 3n/2 is 1.5n, so 2^{1.5n} * 2^{-1/4} * π^{-1/8} * n^{-3/8}\n\nBut when we take |a_n|^{1/n} for the root test, let's compute that:\n\n|a_n|^{1/n} = [2^{1.5n} * 2^{-1/4} * π^{-1/8} * n^{-3/8}]^{1/n} = 2^{1.5} * 2^{-1/(4n)} * π^{-1/(8n)} * n^{-3/(8n)}\n\nAs n approaches infinity, 2^{-1/(4n)} approaches 1, since the exponent goes to 0. Similarly, π^{-1/(8n)} approaches 1, and n^{-3/(8n)} also approaches 1 because ln(n^{-3/(8n)}) = -3/(8n) ln n → 0 as n→infty. So all those terms go to 1, and we're left with 2^{1.5} = 2^{3/2} = sqrt(8) = 2*sqrt(2). Wait, 2^{3/2} is 2^(1 + 1/2) = 2 * sqrt(2). So approximately 2.828...\n\nTherefore, the limit of |a_n|^{1/n} is 2*sqrt(2). Therefore, the radius of convergence R is 1/(2*sqrt(2)).\n\nWait, but let me confirm again. If |a_n|^{1/n} approaches 2^{3/2}, then R = 1 / (2^{3/2}) ) = 2^{-3/2} = 1/(2*sqrt(2)).\n\nAlternatively, 1/(2*sqrt(2)) can be written as sqrt(2)/4, but both are equivalent. However, let me check if there are any miscalculations in the approximations.\n\nWait, maybe I missed a step. Let me retrace. We had:\n\nAfter substituting Stirling's approx:\n\n(2n-1)!/(n!)^2 ≈ (4^n)/(2 sqrt(π) n^{3/2})\n\nThen, the fourth root of that is:\n\n(4^n)^{1/4} / [ (2 sqrt(π) n^{3/2})^{1/4} ]\n\nWhich is 2^{n/2} / (2^{1/4} π^{1/8} n^{3/8})\n\nTherefore, multiplying by 2^n (from the original a_n):\n\na_n = 2^n * [2^{n/2} / (2^{1/4} π^{1/8} n^{3/8})] = 2^{n + n/2 - 1/4} π^{-1/8} n^{-3/8} = 2^{3n/2 -1/4} π^{-1/8} n^{-3/8}\n\nSo |a_n|^{1/n} = [2^{3n/2} * 2^{-1/4} * π^{-1/8} * n^{-3/8} ]^{1/n} = 2^{3/2} * 2^{-1/(4n)} * π^{-1/(8n)} * n^{-3/(8n)}\n\nAs n→infty, the terms with 1/n in the exponent go to 1. So limit is 2^{3/2} = 2*sqrt(2). Therefore R = 1/(2*sqrt(2)) = sqrt(2)/4 ≈ 0.3535.\n\nBut let me check using the ratio test to confirm. Maybe that's easier.\n\nThe ratio test says R = lim |a_n / a_{n+1}|.\n\nCompute a_{n}/a_{n+1} = [2^n * [(2n-1)!/(n!^2)]^{1/4}] / [2^{n+1} * [(2(n+1)-1)!/( (n+1)!^2 ) ]^{1/4} ]\n\nSimplify this:\n\n= (1/2) * [ (2n -1)! / (n!^2) ) / ( (2n +1)! / ( (n+1)!^2 ) ) ]^{1/4}\n\n= (1/2) * [ (2n -1)! (n+1)!^2 / ( (2n +1)! n!^2 ) ]^{1/4}\n\nSimplify inside the brackets:\n\n(2n -1)! / (2n +1)! = 1 / [ (2n)(2n +1) )\n\nAnd (n+1)!^2 / n!^2 = (n+1)^2\n\nTherefore, the ratio becomes:\n\n(1/2) * [ (n+1)^2 / (2n (2n +1)) ) ]^{1/4}\n\nSo the expression inside the brackets is (n+1)^2 / [2n(2n +1)].\n\nLet me write that as (n^2 + 2n +1) / [4n^2 + 2n] ≈ (n^2) / (4n^2) ) for large n. So approximately 1/4. Therefore, the entire ratio is (1/2) * (1/4)^{1/4} = (1/2) * (1/2^{2})^{1/4} ) = (1/2) * (1/2^{0.5}) ) = 1/(2 * sqrt(2)).\n\nBut wait, the ratio test says R = lim |a_n / a_{n+1}|. So if this limit is 1/(2*sqrt(2)), then R is 1/(2*sqrt(2)), same as before. Wait, but that's the same result as the root test. So both methods give R = 1/(2*sqrt(2)), which is sqrt(2)/4. So the radius of convergence is sqrt(2)/4. Let me verify once again.\n\nWait, actually in the ratio test, R = lim |a_n / a_{n+1}|. But according to the computation above, the limit as n→∞ of |a_n / a_{n+1}| is (1/2) * [ (n+1)^2 / (2n(2n +1)) ) ]^{1/4}. Let's compute the limit inside:\n\nTake the expression (n+1)^2 / [2n(2n+1)].\n\nDivide numerator and denominator by n^2:\n\n[1 + 2/n + 1/n^2] / [2n * 2n (1 + 1/(2n)) / n^2] → [1 + 0 + 0] / [4 * 1] = 1/4.\n\nTherefore, [1/4]^{1/4} = (1/4)^{1/4} = (2^{-2})^{1/4} = 2^{-0.5} = 1/sqrt(2). Therefore, the entire limit is (1/2) * (1/sqrt(2)) = 1/(2 sqrt(2)). Therefore, R = 1/(2 sqrt(2)), so the radius is 1/(2 sqrt(2)), which can be rationalized as sqrt(2)/4. So yes, both methods agree.\n\nTherefore, the radius of convergence is sqrt(2)/4. Thus, the answer should be \\boxed{\\dfrac{\\sqrt{2}}{4}}.\n\n**Final Answer**\nThe radius of convergence is \\boxed{\\dfrac{\\sqrt{2}}{4}}.\n</think>To find the radius of convergence of the power series\n\n\\[ \\sum_{n=1}^{\\infty} 2^n \\sqrt[4]{\\frac{(2n-1)!}{(n!)^2}} x^n, \\]\n\nwe use the root test. The general term \\( a_n \\) is given by:\n\n\\[ a_n = 2^n \\sqrt[4]{\\frac{(2n-1)!}{(n!)^2}}. \\]\n\nUsing Stirling's approximation \\( n! \\approx n^n e^{-n} \\sqrt{2\\pi n} \\), we approximate the factorials:\n\n\\[ (2n-1)! \\approx (2n-1)^{2n-1} e^{-(2n-1)} \\sqrt{2\\pi (2n-1)}, \\]\n\\[ (n!)^2 \\approx (n^n e^{-n} \\sqrt{2\\pi n})^2 = n^{2n} e^{-2n} (2\\pi n). \\]\n\nSubstituting these into the expression for \\( \\frac{(2n-1)!}{(n!)^2} \\):\n\n\\[ \\frac{(2n-1)!}{(n!)^2} \\approx \\frac{(2n-1)^{2n-1} \\sqrt{2\\pi (2n-1)}}{n^{2n} \\sqrt{(2\\pi n)^2}}. \\]\n\nFor large \\( n \\), this simplifies to:\n\n\\[ \\frac{(2n-1)^{2n-1}}{n^{2n}} \\approx \\frac{(2n)^{2n} \\cdot 2^{-1} e^{-1}}{n^{2n}} = \\frac{4^n \\cdot e^{-1}}{2n}. \\]\n\nThus,\n\n\\[ \\frac{(2n-1)!}{(n!)^2} \\approx \\frac{4^n}{2 \\sqrt{\\pi} n^{3/2}}. \\]\n\nTaking the fourth root:\n\n\\[ \\sqrt[4]{\\frac{(2n-1)!}{(n!)^2}} \\approx \\frac{2^{n/2}}{2^{1/4} \\pi^{1/8} n^{3/8}}. \\]\n\nTherefore, the coefficient \\( a_n \\) becomes:\n\n\\[ a_n \\approx 2^n \\cdot \\frac{2^{n/2}}{2^{1/4} \\pi^{1/8} n^{3/8}} = 2^{3n/2 - 1/4} \\pi^{-1/8} n^{-3/8}. \\]\n\nApplying the root test:\n\n\\[ \\lim_{n \\to \\infty} |a_n|^{1/n} = \\lim_{n \\to \\infty} \\left( 2^{3n/2 - 1/4} \\pi^{-1/8} n^{-3/8} \\right)^{1/n} = 2^{3/2} = 2\\sqrt{2}. \\]\n\nThus, the radius of convergence \\( R \\) is:\n\n\\[ R = \\frac{1}{2\\sqrt{2}} = \\frac{\\sqrt{2}}{4}. \\]\n\nThe final answer is:\n\n\\[ \\boxed{\\frac{\\sqrt{2}}{4}}. \\]"}