{"row_number": 6415, "expected_answer": "\\(\\frac{1}{2}(x - \\sqrt{1 - x^2}) \\cdot e^{\\arccos x} + C\\)", "problem": "Evaluate $\\int{e^{\\arccos{x}}}{dx}$.", "generated_solution": "<think>\nOkay, so I need to solve the integral of e raised to the arccosine of x with respect to x. Hmm, let me think. I've done some integration by parts before, but this one might be a bit tricky. Let me write down the integral first: ∫ e^{arccos(x)} dx. \n\nAlright, integration by parts usually requires choosing a part of the integrand to set as u and another as dv. The formula is ∫u dv = uv - ∫v du. So, I need to pick u and dv here. The integrand is e^{arccos(x)}, which is an exponential function with an inverse trigonometric function in the exponent. That complicates things. Maybe substitution first?\n\nWait, substitution. Let me try letting t = arccos(x). Then, dt/dx = -1/√(1 - x²), right? Because the derivative of arccos(x) is -1 over sqrt(1 - x²). So, that means dx = -sqrt(1 - x²) dt. But if t = arccos(x), then x = cos(t). So, sqrt(1 - x²) would be sin(t), since sqrt(1 - cos²(t)) = sin(t). So dx = -sin(t) dt. Wait, but substitution would require expressing the integral in terms of t. Let's try that.\n\nSo, substituting t = arccos(x), then the integral becomes ∫ e^t * (-sin(t) dt). But wait, the original integral is e^{arccos(x)} dx, so replacing dx with -sin(t) dt, we get -∫ e^t sin(t) dt. Hmm, that seems manageable. So now the problem is to compute -∫ e^t sin(t) dt, and then substitute back to x.\n\nBut integrating e^t sin(t) dt is a standard integral. Let me recall. When you have e^at sin(bt) dt, you can use integration by parts twice and solve for the integral. Let me try that. Let me set u = sin(t), dv = e^t dt. Then du = cos(t) dt, and v = e^t. So integration by parts gives uv - ∫v du = e^t sin(t) - ∫ e^t cos(t) dt.\n\nNow, the integral ∫ e^t cos(t) dt. Again, integration by parts. Let u = cos(t), dv = e^t dt. Then du = -sin(t) dt, v = e^t. So this becomes e^t cos(t) - ∫ e^t (-sin(t)) dt = e^t cos(t) + ∫ e^t sin(t) dt.\n\nSo putting it all together: the original integral ∫ e^t sin(t) dt = e^t sin(t) - [e^t cos(t) + ∫ e^t sin(t) dt]. So, let's denote I = ∫ e^t sin(t) dt. Then,\n\nI = e^t sin(t) - e^t cos(t) - I.\n\nBringing the I from the right side to the left:\n\nI + I = e^t sin(t) - e^t cos(t)\n\n2I = e^t (sin(t) - cos(t))\n\nSo, I = (e^t / 2)(sin(t) - cos(t)) + C.\n\nTherefore, going back to the original substitution. Remember we had:\n\nOriginal integral was -∫ e^t sin(t) dt = -I. So substituting I:\n\n- [ (e^t / 2)(sin(t) - cos(t)) ] + C = - (e^t / 2)(sin(t) - cos(t)) + C.\n\nBut t is arccos(x), so let's replace t with arccos(x). Also, sin(t) when t = arccos(x) is sqrt(1 - x²). Because t = arccos(x), so cos(t) = x, so sin(t) = sqrt(1 - x²). So, substituting back:\n\n- (e^{arccos(x)} / 2)(sqrt(1 - x²) - x) + C.\n\nLet me check the signs again. Wait, when we did substitution t = arccos(x), then dx = - sin(t) dt. So the integral became -∫ e^t sin(t) dt, which we integrated to -I. Then I = (e^t / 2)(sin(t) - cos(t)) + C, so the integral is -I + C, so - (e^t / 2)(sin(t) - cos(t)) + C.\n\nBut let me verify the algebra. Let's expand it. So, -I would be - [ (e^t / 2)(sin(t) - cos(t)) ] + C. So yes, the negative sign applies to the entire expression. Therefore:\n\n- (e^t / 2)(sin(t) - cos(t)) + C = (- e^t sin(t) + e^t cos(t)) / 2 + C = (e^t (cos(t) - sin(t)) ) / 2 + C.\n\nBut substituting t = arccos(x):\n\n= (e^{arccos(x)} (x - sqrt(1 - x²)) ) / 2 + C.\n\nHmm, so simplifying, that's (e^{arccos(x)} (x - sqrt(1 - x²)) ) / 2 + C. Let me see if this makes sense.\n\nAlternatively, maybe I made a sign error during substitution. Let me check again. Starting from the substitution:\n\nOriginal integral: ∫ e^{arccos(x)} dx.\n\nSet t = arccos(x), so dt = -1 / sqrt(1 - x²) dx. Therefore, dx = - sqrt(1 - x²) dt. But since t = arccos(x), x = cos(t), so sqrt(1 - x²) = sqrt(1 - cos²(t)) = sin(t). Since t is in the range [0, π], sin(t) is non-negative, so sqrt(1 - x²) = sin(t). Thus, dx = - sin(t) dt. Wait, is that negative sign correct? Wait, dt = - dx / sqrt(1 - x²), so solving for dx gives dx = - sqrt(1 - x²) dt. But sqrt(1 - x²) is sin(t), so dx = - sin(t) dt. Therefore, substituting into the integral:\n\n∫ e^{arccos(x)} dx = ∫ e^t (- sin(t) dt) = - ∫ e^t sin(t) dt. That's correct.\n\nThen integrating by parts as before. Then after integrating, the integral of e^t sin(t) dt is (e^t / 2)(sin(t) - cos(t)) + C. So multiplying by -1 gives - (e^t / 2)(sin(t) - cos(t)) + C. Then substitute back t = arccos(x), so:\n\n- (e^{arccos(x)} / 2)(sin(arccos(x)) - cos(arccos(x))) + C.\n\nBut sin(arccos(x)) is sqrt(1 - x²), and cos(arccos(x)) is x. Therefore:\n\n- (e^{arccos(x)} / 2)(sqrt(1 - x²) - x) + C.\n\nWhich simplifies to:\n\n- (e^{arccos(x)} (sqrt(1 - x²) - x) ) / 2 + C = (e^{arccos(x)} (x - sqrt(1 - x²)) ) / 2 + C.\n\nSo that's the antiderivative. Let me check differentiation to verify.\n\nLet’s differentiate the result to see if we get back the original integrand. Let me compute d/dx [ (e^{arccos(x)} (x - sqrt(1 - x²)) ) / 2 + C ].\n\nFirst, let’s denote y = (e^{arccos(x)} (x - sqrt(1 - x²)) ) / 2. Then dy/dx = [ derivative of e^{arccos(x)} times (x - sqrt(1 - x²)) + e^{arccos(x)} times derivative of (x - sqrt(1 - x²)) ] / 2.\n\nFirst term: derivative of e^{arccos(x)} is e^{arccos(x)} * (-1 / sqrt(1 - x²)).\n\nSo, first part: e^{arccos(x)} * (-1 / sqrt(1 - x²)) * (x - sqrt(1 - x²)).\n\nSecond part: e^{arccos(x)} times [ derivative of x (which is 1) - derivative of sqrt(1 - x²) (which is (1/(2 sqrt(1 - x²))) * (-2x) ) ].\n\nSo, derivative of sqrt(1 - x²) is (-x)/sqrt(1 - x²). Therefore, the second part inside is 1 - (-x)/sqrt(1 - x²) = 1 + x / sqrt(1 - x²).\n\nPutting it all together:\n\ndy/dx = [ e^{arccos(x)} (-1 / sqrt(1 - x²)) (x - sqrt(1 - x²)) + e^{arccos(x)} (1 + x / sqrt(1 - x²)) ] / 2.\n\nLet me factor out e^{arccos(x)} / 2:\n\n= (e^{arccos(x)} / 2) [ (- (x - sqrt(1 - x²)) / sqrt(1 - x²) ) + 1 + x / sqrt(1 - x²) ) ].\n\nSimplify the terms inside the brackets:\n\nFirst term: - (x - sqrt(1 - x²)) / sqrt(1 - x²) = -x / sqrt(1 - x²) + (sqrt(1 - x²)) / sqrt(1 - x²) = -x / sqrt(1 - x²) + 1.\n\nSecond term: 1 + x / sqrt(1 - x²).\n\nAdding them together:\n\n[ (-x / sqrt(1 - x²) + 1 ) + 1 + x / sqrt(1 - x²) ) ] =\n\nThe -x/sqrt and +x/sqrt cancel, leaving 1 + 1 = 2.\n\nTherefore, dy/dx = (e^{arccos(x)} / 2) * 2 = e^{arccos(x)}. Which matches the integrand! So the antiderivative is correct.\n\nTherefore, the solution is:\n\n( e^{arccos(x)} (x - sqrt(1 - x²)) ) / 2 + C.\n\nBut let me check if there's another way to write this. Maybe factor differently? For example, x - sqrt(1 - x²) is already simplified. So the answer should be that expression divided by 2, plus the constant.\n\nTherefore, the integral of e^{arccos(x)} dx is (x - sqrt(1 - x²)) e^{arccos(x)} / 2 + C.\n\nAlternatively, writing it as [x - sqrt(1 - x²)] e^{arccos x} / 2 + C.\n\nI think that's the final answer. Let me just make sure I didn't mix up any signs. The substitution steps and the differentiation check both worked out, so it's likely correct. So the answer should be \\boxed{\\dfrac{e^{\\arccos{x}}(x - \\sqrt{1 - x^2})}{2} + C}.\n</think>To evaluate the integral \\(\\int e^{\\arccos{x}} \\, dx\\), we use substitution and integration by parts.\n\n1. **Substitution**:\n   Let \\( t = \\arccos{x} \\). Then,\n   \\[\n   dt = -\\frac{1}{\\sqrt{1 - x^2}} \\, dx \\quad \\Rightarrow \\quad dx = -\\sqrt{1 - x^2} \\, dt.\n   \\]\n   Since \\( x = \\cos{t} \\), we have \\( \\sqrt{1 - x^2} = \\sin{t} \\). Thus,\n   \\[\n   dx = -\\sin{t} \\, dt.\n   \\]\n   The integral becomes:\n   \\[\n   \\int e^{\\arccos{x}} \\, dx = -\\int e^t \\sin{t} \\, dt.\n   \\]\n\n2. **Integration by Parts**:\n   We use integration by parts to solve \\(\\int e^t \\sin{t} \\, dt\\). Let:\n   \\[\n   u = \\sin{t} \\quad \\text{and} \\quad dv = e^t \\, dt.\n   \\]\n   Then,\n   \\[\n   du = \\cos{t} \\, dt \\quad \\text{and} \\quad v = e^t.\n   \\]\n   Applying integration by parts:\n   \\[\n   \\int e^t \\sin{t} \\, dt = e^t \\sin{t} - \\int e^t \\cos{t} \\, dt.\n   \\]\n   Now, we need to integrate \\(\\int e^t \\cos{t} \\, dt\\). Let:\n   \\[\n   u = \\cos{t} \\quad \\text{and} \\quad dv = e^t \\, dt.\n   \\]\n   Then,\n   \\[\n   du = -\\sin{t} \\, dt \\quad \\text{and} \\quad v = e^t.\n   \\]\n   Applying integration by parts again:\n   \\[\n   \\int e^t \\cos{t} \\, dt = e^t \\cos{t} - \\int e^t (-\\sin{t}) \\, dt = e^t \\cos{t} + \\int e^t \\sin{t} \\, dt.\n   \\]\n   Substituting this back into the previous equation:\n   \\[\n   \\int e^t \\sin{t} \\, dt = e^t \\sin{t} - (e^t \\cos{t} + \\int e^t \\sin{t} \\, dt).\n   \\]\n   Simplifying:\n   \\[\n   \\int e^t \\sin{t} \\, dt = e^t \\sin{t} - e^t \\cos{t} - \\int e^t \\sin{t} \\, dt.\n   \\]\n   Adding \\(\\int e^t \\sin{t} \\, dt\\) to both sides:\n   \\[\n   2 \\int e^t \\sin{t} \\, dt = e^t (\\sin{t} - \\cos{t}).\n   \\]\n   Solving for \\(\\int e^t \\sin{t} \\, dt\\):\n   \\[\n   \\int e^t \\sin{t} \\, dt = \\frac{e^t (\\sin{t} - \\cos{t})}{2}.\n   \\]\n\n3. **Substitute Back**:\n   Substitute \\( t = \\arccos{x} \\) and \\(\\sin{t} = \\sqrt{1 - x^2}\\):\n   \\[\n   -\\int e^t \\sin{t} \\, dt = -\\frac{e^t (\\sin{t} - \\cos{t})}{2} = -\\frac{e^{\\arccos{x}} (\\sqrt{1 - x^2} - x)}{2}.\n   \\]\n   Simplifying:\n   \\[\n   -\\frac{e^{\\arccos{x}} (\\sqrt{1 - x^2} - x)}{2} = \\frac{e^{\\arccos{x}} (x - \\sqrt{1 - x^2})}{2}.\n   \\]\n\n4. **Final Answer**:\n   The integral is:\n   \\[\n   \\boxed{\\frac{e^{\\arccos{x}} (x - \\sqrt{1 - x^2})}{2} + C}.\n   \\]"}