{"row_number": 19179, "expected_answer": "\\( 6Bx^{2B-1} - 6Bx^{3B-1} \\)", "problem": "Suppose that a random sample of three observations is drawn from a distribution with the following probability density function (pdf):\n\n\\[ f(x) = Bx^{B-1} \\text{ for } 0 < x < 1 \\text{ and } 0 \\text{ otherwise, where } B > 0. \\]\n\nDetermine the pdf of the sample median.", "generated_solution": "<think>\nOkay, so I need to find the probability density function (pdf) of the sample median when we take a random sample of three observations from a distribution with the given pdf f(x) = Bx^(B-1) for 0 < x < 1 and B > 0. Hmm, let's start by recalling how to find the distribution of the median for a sample.\n\nFirst, I remember that for order statistics, the pdf of the k-th order statistic in a sample of size n is given by:\n\nf_{X_{(k)}}(x) = (n! / [(k-1)!(n - k)!]) * [F(x)]^{k-1} * [1 - F(x)]^{n - k} * f(x)\n\nWhere F(x) is the cumulative distribution function (CDF) of the original distribution. Since we're dealing with the median in a sample of three, the median is the second order statistic (k=2, n=3). So, substituting those values in, the formula becomes:\n\nf_{X_{(2)}}(x) = (3! / [1!1!]) * [F(x)]^{1} * [1 - F(x)]^{1} * f(x)\n\nSimplifying, that's 6 * F(x) * (1 - F(x)) * f(x).\n\nSo, I need to find F(x), the CDF corresponding to the given pdf f(x). Let's compute that.\n\nThe CDF F(x) is the integral from 0 to x of f(t) dt. So:\n\nF(x) = ∫₀ˣ B t^{B-1} dt\n\nThe integral of B t^{B-1} is t^B, evaluated from 0 to x, so:\n\nF(x) = x^B - 0^B = x^B for 0 < x < 1. That's straightforward.\n\nSo substituting back into the formula for the median's pdf:\n\nf_{X_{(2)}}(x) = 6 * x^B * (1 - x^B) * B x^{B - 1}\n\nLet me check that again. Wait, 3! / (1!1!) = 6, yes. Then [F(x)]^{1} is x^B, [1 - F(x)] is (1 - x^B), and f(x) is B x^{B-1}.\n\nMultiplying all together:\n\n6 * x^B * (1 - x^B) * B x^{B - 1}\n\nLet's simplify this expression step by step.\n\nFirst, multiply the constants: 6 * B.\n\nThen multiply the x terms: x^B * x^{B - 1} = x^{2B - 1}.\n\nSo so far, we have 6B x^{2B - 1} * (1 - x^B).\n\nExpanding that, the pdf would be 6B x^{2B - 1} - 6B x^{3B - 1}.\n\nWait, but maybe it's better to leave it in the factored form unless instructed otherwise. Let me check if there's a simplification I missed, but perhaps the problem just wants the expression in terms of B and x.\n\nSo combining all the terms, the pdf of the median is:\n\nf_{X_{(2)}}(x) = 6B x^{2B - 1} (1 - x^B) for 0 < x < 1, and 0 otherwise.\n\nLet me verify this makes sense. First, when B=1, the original distribution is uniform on (0,1), so f(x) = 1. Then the pdf of the median should be 6x(1 - x), which is the standard result for the median of three uniform variables. Indeed, that's correct. For B=1, the expression becomes 6*1*x^{2*1 -1}(1 - x^1) = 6x(1 - x), which matches. So that checks out.\n\nAnother check: When B approaches 0, but since B > 0, maybe we don't need to check that. For B=2, for example, the original pdf is 2x, so the CDF is x². Then the median pdf would be 6*2 x^{3} (1 - x²) = 12x³(1 - x²). Let's see if integrating this over 0 to 1 gives 1.\n\nIntegral from 0 to 1 of 12x³(1 - x²) dx = 12 ∫ (x³ - x^5) dx = 12[ (x^4/4 - x^6/6) from 0 to 1 ] = 12[(1/4 - 1/6) - 0] = 12*(1/12) = 1. So yes, that's a valid pdf. So that seems correct.\n\nTherefore, the final answer should be 6B x^{2B - 1}(1 - x^B) for 0 < x < 1. Let me just write that in the required format.\n</think>To determine the probability density function (pdf) of the sample median for a random sample of three observations from the given distribution with the pdf \\( f(x) = Bx^{B-1} \\) for \\( 0 < x < 1 \\) and \\( B > 0 \\), we follow these steps:\n\n1. **Identify the Distribution of the Median**:\n   The median of a sample of size 3 is the second order statistic \\( X_{(2)} \\). The pdf of the \\( k \\)-th order statistic \\( X_{(k)} \\) in a sample of size \\( n \\) is given by:\n   \\[\n   f_{X_{(k)}}(x) = \\frac{n!}{(k-1)!(n-k)!} [F(x)]^{k-1} [1 - F(x)]^{n-k} f(x)\n   \\]\n   For the median in a sample of size 3, \\( k = 2 \\) and \\( n = 3 \\). Thus, the formula becomes:\n   \\[\n   f_{X_{(2)}}(x) = \\frac{3!}{1!1!} [F(x)]^1 [1 - F(x)]^1 f(x) = 6 F(x) (1 - F(x)) f(x)\n   \\]\n\n2. **Find the Cumulative Distribution Function (CDF)**:\n   The CDF \\( F(x) \\) is the integral of the pdf \\( f(x) \\):\n   \\[\n   F(x) = \\int_0^x B t^{B-1} \\, dt = \\left[ t^B \\right]_0^x = x^B\n   \\]\n   Therefore, the CDF is:\n   \\[\n   F(x) = x^B \\quad \\text{for} \\quad 0 < x < 1\n   \\]\n\n3. **Substitute the CDF and pdf into the Order Statistic Formula**:\n   Using \\( F(x) = x^B \\) and \\( f(x) = B x^{B-1} \\), we substitute into the formula for the median's pdf:\n   \\[\n   f_{X_{(2)}}(x) = 6 x^B (1 - x^B) B x^{B-1}\n   \\]\n\n4. **Simplify the Expression**:\n   Simplify the expression by combining the terms:\n   \\[\n   f_{X_{(2)}}(x) = 6 B x^B (1 - x^B) x^{B-1} = 6 B x^{2B-1} (1 - x^B)\n   \\]\n\nThus, the probability density function (pdf) of the sample median is:\n\\[\n\\boxed{6B x^{2B-1} (1 - x^B) \\text{ for } 0 < x < 1}\n\\]"}