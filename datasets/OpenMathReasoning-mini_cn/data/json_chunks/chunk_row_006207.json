{"row_number": 6207, "expected_answer": "\\(\\frac{\\pi}{2}\\)", "problem": "Evaluate $\\int_{0}^{\\infty} \\frac{\\sin^{2} x}{x^{2}}\\ dx$.", "generated_solution": "<think>\nOkay, so I need to evaluate the integral from 0 to infinity of (sin²x)/x² dx. Hmm, let's see. I remember that integrals involving sinx/x are related to the sinc function, and their integrals over the entire real line often involve π. But this is sin squared x over x squared, so maybe there's a similar approach.\n\nFirst, maybe I can rewrite sin²x using a trigonometric identity. Let me recall: sin²x = (1 - cos2x)/2. So substituting that in, the integral becomes ∫₀^∞ (1 - cos2x)/(2x²) dx. That simplifies to (1/2) ∫₀^∞ (1 - cos2x)/x² dx. \n\nWait, but integrating (1 - cosax)/x² from 0 to infinity might be a standard integral. Let me check if I remember the value of that. I think ∫₀^∞ (1 - cosax)/x² dx = (π a)/2. Let me verify this. If a=2, then it would be π*2/2 = π. So if that's the case, then our integral becomes (1/2)*π, so π/2? But wait, that seems too straightforward. Let me make sure.\n\nAlternatively, maybe integration by parts would work here. Let me try that. Let’s set u = (1 - cos2x) and dv = dx/x². Then du = 2 sin2x dx, and v = -1/x. Integration by parts formula is uv| from 0 to ∞ minus ∫ v du. \n\nSo applying that, we have:\n\n(1/2) [ (- (1 - cos2x)/x ) from 0 to ∞ + 2 ∫₀^∞ (sin2x)/x dx ]\n\nWait, let's compute each part step by step. First, the boundary terms:\n\nAt x approaching infinity, (1 - cos2x)/x tends to 0 because the numerator oscillates between 0 and 2, and the denominator goes to infinity. At x approaching 0, we have (1 - cos2x) ~ 2x², so (1 - cos2x)/x ~ 2x, which tends to 0. So the boundary term is 0 - 0 = 0. \n\nTherefore, the integral simplifies to (1/2)*(0 + 2 ∫₀^∞ (sin2x)/x dx) = (1/2)*2 ∫₀^∞ (sin2x)/x dx = ∫₀^∞ (sin2x)/x dx.\n\nBut ∫₀^∞ (sinax)/x dx is known to be π/2 for a > 0. So here a = 2, so ∫₀^∞ (sin2x)/x dx = π/2. Hence, the original integral becomes π/2. Wait, but hold on. The original integral was ∫₀^∞ sin²x/x² dx. After substitution and integration by parts, we ended up with π/2. But is that correct?\n\nWait, let me check with another approach. Alternatively, maybe using the Fourier transform or differentiation under the integral sign. Let me consider integrating sin²x/x². Another approach: Let me recall that ∫₀^∞ sin²x/x² dx can be related to the integral of sinc squared x. The integral of sinc squared x from 0 to infinity is π/2. Wait, but sinc x is sinx/x, so sinc squared x is sin²x/x². So if ∫₀^∞ (sinx/x)^2 dx = π/2. Then that would mean the answer is π/2. But I need to confirm this.\n\nWait, let's verify with substitution. Let me take the integral ∫₀^∞ (sinx/x)^2 dx. Let’s set t = x, then dt = dx. Alternatively, use integration by parts. Let me try integrating by parts again. Let u = sin²x and dv = dx/x². Then du = 2 sinx cosx dx = sin2x dx, and v = -1/x. Then, integrating by parts:\n\nuv|₀^∞ - ∫₀^∞ v du. The uv term is -sin²x/x evaluated from 0 to ∞. At infinity, sin²x is bounded by 1, so -sin²x/x tends to 0. At 0, sin²x ~ x², so sin²x/x ~ x, which tends to 0. So the boundary term is 0. Then, we have -∫₀^∞ (-1/x) sin2x dx = ∫₀^∞ sin2x/(2x) dx * 2? Wait, maybe I messed up the constants. Wait, du = sin2x dx, so the integral becomes -∫₀^∞ (-1/x) sin2x dx = ∫₀^∞ (sin2x)/x dx. Then, ∫₀^∞ (sin2x)/x dx is equal to π/2, as before. Therefore, the original integral is equal to π/2. So, yes, that seems correct.\n\nAlternatively, maybe using the integral formula for sin²x. The integral of sin²x/x² from 0 to infinity. Let's recall that ∫₀^∞ sin²x dx can be connected via Parseval's theorem for Fourier transforms. Hmm, but maybe that's more advanced. Alternatively, using the integral ∫₀^∞ sin²x/x² dx = ∫₀^∞ sinx/x * sinx/x dx. The convolution maybe? But not sure.\n\nAlternatively, consider integrating over the entire real line. Since the function is even, ∫₀^∞ (sin²x)/x² dx = (1/2) ∫_{-∞}^∞ (sin²x)/x² dx. Then, using the Fourier transform: the Fourier transform of sinx/x is a rectangular function, but I need to recall the exact relation. Alternatively, write sin²x as (1 - cos2x)/2. So (sin²x)/x² = (1 - cos2x)/(2x²). Then, the integral becomes (1/2) ∫_{-∞}^∞ (1 - cos2x)/(2x²) dx. Wait, but 1/(2x²) is even, so integrating from 0 to ∞ would be half of that. Hmm, maybe this is getting complicated.\n\nAlternatively, use the known integral ∫₀^∞ (1 - cos(ax))/x² dx = (π a)/2 for a > 0. If that's the case, then here a=2, so the integral becomes (π * 2)/2 = π. Then, since the original integral is (1/2) of that, because we have (1 - cos2x)/(2x²), so (1/2)*(π) = π/2. That matches our previous result.\n\nTherefore, it seems that the answer is π/2.\n\nBut let me check with another method to confirm. Let's use differentiation under the integral sign. Let me consider the integral I(a) = ∫₀^∞ (sin²x)/(x²) e^{-a x} dx, and then take a approaches 0. Wait, but maybe that's overcomplicating. Alternatively, consider integrating with respect to a parameter. Let’s let I(a) = ∫₀^∞ (sin²(ax))/x² dx, then take derivative with respect to a. Hmm, maybe.\n\nLet’s compute dI/da = ∫₀^∞ (2 sin(ax) cos(ax) * x)/x² dx = ∫₀^∞ (sin(2ax))/x dx. Then, ∫₀^∞ sin(2ax)/x dx = π/2 for a > 0. So dI/da = π/2. Integrating with respect to a gives I(a) = (π/2) a + C. When a approaches 0, I(a) approaches 0 because sin²(ax)/x² ~ a² x² /x² = a², which integrates to a² * ∫₀^∞ dx, but wait, that seems divergent. Wait, maybe I made a mistake here.\n\nWait, when a approaches 0, sin(ax) ~ ax, so sin²(ax)/x² ~ a² x² /x² = a². Then, the integral becomes ∫₀^∞ a² dx, which diverges. Hmm, maybe this approach isn't working. Alternatively, perhaps using a different parameterization.\n\nAlternatively, since we already have two different methods leading to π/2, and cross-verifying with known integrals, maybe it's safe to conclude that the answer is π/2. But let me check one more source. If I recall, the integral of sinc squared x over the entire real line is π, so from 0 to infinity, it's π/2. Yes, that's correct. Since sinc squared x is (sinx/x)^2, and since the function is even, ∫_{-infty}^infty (sinx/x)^2 dx = π, so ∫₀^∞ is π/2. Therefore, the answer is π/2.\n\n**Final Answer**\n\\boxed{\\dfrac{\\pi}{2}}\n</think>To evaluate the integral \\(\\int_{0}^{\\infty} \\frac{\\sin^2 x}{x^2} \\, dx\\), we start by using the trigonometric identity \\(\\sin^2 x = \\frac{1 - \\cos 2x}{2}\\). This transforms the integral into:\n\n\\[\n\\int_{0}^{\\infty} \\frac{\\sin^2 x}{x^2} \\, dx = \\frac{1}{2} \\int_{0}^{\\infty} \\frac{1 - \\cos 2x}{x^2} \\, dx\n\\]\n\nNext, we use integration by parts on the integral \\(\\int_{0}^{\\infty} \\frac{1 - \\cos 2x}{x^2} \\, dx\\). Let \\(u = 1 - \\cos 2x\\) and \\(dv = \\frac{1}{x^2} \\, dx\\). Then, \\(du = 2 \\sin 2x \\, dx\\) and \\(v = -\\frac{1}{x}\\). Applying integration by parts, we have:\n\n\\[\n\\int_{0}^{\\infty} \\frac{1 - \\cos 2x}{x^2} \\, dx = \\left[ -\\frac{1 - \\cos 2x}{x} \\right]_{0}^{\\infty} + 2 \\int_{0}^{\\infty} \\frac{\\sin 2x}{x} \\, dx\n\\]\n\nThe boundary term \\(\\left[ -\\frac{1 - \\cos 2x}{x} \\right]_{0}^{\\infty}\\) evaluates to 0 because as \\(x \\to \\infty\\), \\(\\frac{1 - \\cos 2x}{x} \\to 0\\), and as \\(x \\to 0\\), \\(\\frac{1 - \\cos 2x}{x} \\to 0\\). This simplifies the integral to:\n\n\\[\n2 \\int_{0}^{\\infty} \\frac{\\sin 2x}{x} \\, dx\n\\]\n\nThe integral \\(\\int_{0}^{\\infty} \\frac{\\sin ax}{x} \\, dx\\) is known to be \\(\\frac{\\pi}{2}\\) for \\(a > 0\\). Here, \\(a = 2\\), so:\n\n\\[\n\\int_{0}^{\\infty} \\frac{\\sin 2x}{x} \\, dx = \\frac{\\pi}{2}\n\\]\n\nThus, the integral becomes:\n\n\\[\n2 \\cdot \\frac{\\pi}{2} = \\pi\n\\]\n\nSubstituting back into the original expression, we get:\n\n\\[\n\\frac{1}{2} \\cdot \\pi = \\frac{\\pi}{2}\n\\]\n\nTherefore, the value of the integral is:\n\n\\[\n\\boxed{\\frac{\\pi}{2}}\n\\]"}