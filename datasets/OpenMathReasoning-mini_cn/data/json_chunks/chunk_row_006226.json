{"row_number": 6226, "expected_answer": "\\(\\frac{n}{2}\\)", "problem": "Given that \\( z_0, z_1, z_2, \\ldots, z_n \\) are the roots of the equation \\( z^{n+1} = 1 \\), calculate \\( \\frac{1}{1-z_1} + \\frac{1}{1-z_2} + \\cdots + \\frac{1}{1-z_n} \\).", "generated_solution": "<think>\nOkay, so I have this problem here where I need to find the sum of 1/(1 - z_k) for k from 1 to n, where z_0, z_1, ..., z_n are the roots of the equation z^{n+1} = 1. Hmm, let me start by recalling that the roots of z^{n+1} = 1 are the (n+1)th roots of unity. So each z_k is a complex number on the unit circle, right? They can be written as e^(2πik/(n+1)) for k from 0 to n.\n\nWait, the problem mentions z_0 to z_n, so there are n+1 roots. But the sum is from z_1 to z_n, so it's excluding z_0. Let me check that. Yeah, the sum is from 1 to n, so there are n terms. The first root z_0 is 1, since when k=0, e^(0) = 1. So we are summing 1/(1 - z_k) for each non-1 root. \n\nI need to compute S = 1/(1 - z_1) + 1/(1 - z_2) + ... + 1/(1 - z_n). Maybe there's a clever way to relate this sum to some properties of roots of unity. Let me think about how to approach this. I remember that for roots of unity, there are some summation formulas that might help. Also, perhaps partial fractions or derivatives could come into play here.\n\nAlternatively, maybe we can express each term 1/(1 - z_k) as something else. Let's note that for each root z_k (k ≠ 0), since z_k^{n+1} = 1, we have that 1 - z_k^{n+1} = 0. But I don't see immediately how that helps. Wait, maybe we can relate this to the derivative of the polynomial. Let's consider the polynomial P(z) = z^{n+1} - 1. Then, the roots of P(z) are exactly the z_k. The derivative P'(z) is (n+1)z^n. Then, using the formula for the sum over roots: sum_{k=0}^n 1/(z - z_k) = P'(z)/P(z). Wait, so in this case, if we set z = 1, then P(1) = 1^{n+1} - 1 = 0. But z=1 is a root, so P(z) has a root at z=1. Therefore, P(z) = (z - 1)(z^n + z^{n-1} + ... + z + 1). So the derivative P'(z) would be (n+1)z^n. Alternatively, expanding the product, P(z) = (z - 1)Q(z), where Q(z) = z^n + z^{n-1} + ... + z + 1. Then, P'(z) = Q(z) + (z - 1)Q'(z). So at z=1, P'(1) = Q(1) + 0 = Q(1). But Q(1) = 1 + 1 + ... + 1 (n+1 terms?), wait no, Q(z) is from z^n down to z^0, so Q(1) = n+1? Wait, but if Q(z) = z^n + z^{n-1} + ... + z + 1, then Q(1) = n+1. So P'(1) = Q(1) = n+1. But also, P'(1) = (n+1)*1^n = n+1. That's consistent.\n\nBut how does this relate to the sum we need? The sum in question is sum_{k=1}^n 1/(1 - z_k). Let's note that 1/(1 - z_k) = 1/( - (z_k - 1) ) = -1/(z_k - 1). So the sum S = - sum_{k=1}^n 1/(z_k - 1). But if we consider the sum over all roots from k=0 to n, that would include z_0 = 1. So sum_{k=0}^n 1/(1 - z_k) would be 1/(1 - 1) + sum_{k=1}^n 1/(1 - z_k). But 1/(1 - 1) is undefined. However, maybe there's a way to relate the sum from k=1 to n to the sum over all roots except z_0.\n\nAlternatively, going back to the formula for the sum over roots: sum_{k=0}^n 1/(1 - z_k) = P'(1)/P(1). But wait, P(1) is zero because z=1 is a root, so that expression is undefined. Hmm. But perhaps using the fact that P(z) = (z - 1)Q(z), then the logarithmic derivative P'(z)/P(z) = 1/(z - 1) + Q'(z)/Q(z). So, at z=1, we can write P'(1)/P(1) would involve division by zero, but maybe we can consider the derivative expression.\n\nAlternatively, since P(z) = z^{n+1} - 1, then P'(z) = (n+1)z^n. Then, the logarithmic derivative is P'(z)/P(z) = (n+1)z^n / (z^{n+1} - 1). Then, sum_{k=0}^n 1/(z - z_k) = P'(z)/P(z). Therefore, if we take z=1, we get sum_{k=0}^n 1/(1 - z_k) = P'(1)/P(1). But P(1) = 0 and P'(1) = n+1. So this is (n+1)/0, which is undefined. But perhaps we can take the limit as z approaches 1? Let's consider the limit as z approaches 1 of sum_{k=0}^n 1/(z - z_k) = lim_{z→1} (n+1)z^n / (z^{n+1} - 1). Let me compute this limit. Let's set z = 1 + ε, where ε is approaching 0. Then, z^{n+1} ≈ 1 + (n+1)ε, and z^n ≈ 1 + nε. Then, the numerator is approximately (n+1)(1 + nε), and the denominator is (1 + (n+1)ε) - 1 = (n+1)ε. So the whole expression is approximately (n+1)(1 + nε)/[(n+1)ε] = (1 + nε)/ε ≈ 1/ε + n. But as ε approaches 0, this goes to infinity. However, if we subtract the term corresponding to z_0=1, then maybe the remaining sum is finite? Let's see. If we write sum_{k=0}^n 1/(1 - z_k) = 1/(1 - 1) + sum_{k=1}^n 1/(1 - z_k) = undefined + S. But the limit as z approaches 1 of sum_{k=0}^n 1/(z - z_k) is the same as the limit of (n+1)z^n/(z^{n+1} - 1). Let's compute this using L’Hospital’s Rule since it's of the form infinity/infinity. Taking the derivative of the numerator and denominator with respect to z: numerator derivative is (n+1)*n z^{n-1}, denominator derivative is (n+1) z^n. So the limit becomes (n(n+1) z^{n-1}) / ((n+1) z^n)) = n/z. As z approaches 1, this limit is n. Therefore, the original limit is n. But the sum sum_{k=0}^n 1/(z - z_k) as z approaches 1 approaches n, but since z=1 is one of the roots, the term 1/(z - z_0) = 1/(z - 1) tends to infinity. Therefore, the remaining sum sum_{k=1}^n 1/(1 - z_k) must approach n minus the divergent term. Wait, that seems contradictory. But perhaps this approach is not the best.\n\nAlternatively, perhaps we can use the formula for the sum of 1/(1 - z_k) where z_k are roots of z^{n+1} -1 =0 except z=1. Let me think. Suppose we have all the roots z_0, z_1, ..., z_n. Then, excluding z_0=1, the other roots are the primitive (n+1)th roots of unity. Now, maybe I can use some known summation formulas over roots of unity.\n\nAlternatively, note that 1/(1 - z_k) can be related to a geometric series. Since |z_k|=1, but maybe this isn't helpful here.\n\nAlternatively, consider that 1/(1 - z_k) = (1 + z_k + z_k^2 + ... + z_k^m + ... ) but this is an infinite series. However, since z_k^{n+1} =1, the series would repeat every n+1 terms, but perhaps summing all terms would not converge. Hmm, maybe not the right approach.\n\nWait, another idea: consider the sum S = sum_{k=1}^n 1/(1 - z_k). Let's write each term as 1/(1 - z_k) = conjugate(1 - z_k)/|1 - z_k|^2, but not sure if that helps. Alternatively, note that for each z_k, if we take w_k = 1 - z_k, then sum 1/w_k. Maybe we can find a relation between the product or sum of w_k. But since z_k are roots of z^{n+1} -1, then w_k = 1 - z_k are roots of (1 - w)^{n+1} -1 =0. Which simplifies to (-1)^{n+1}w^{n+1} + ... + ... =0. Maybe expanding (1 - w)^{n+1} =1. Then, (1 - w)^{n+1} -1 =0. So the roots w_k satisfy (1 - w_k)^{n+1} =1. Therefore, 1 - w_k are the (n+1)th roots of unity, which would imply that w_k =1 - e^{2πi m/(n+1)} for m=0,...,n. Wait, but this seems similar to our original z_k. Wait, maybe it's the same set shifted by 1. So if w_k =1 - z_k, then the w_k are roots of (1 - w)^{n+1}=1, so 1 - w is an (n+1)th root of unity, so w =1 - e^{2πi m/(n+1)}. So the w_k are 1 - z_m where z_m is a root of z^{n+1}=1. Therefore, the roots w_k correspond to the same roots as 1 - z_m. But in our problem, we have w_k =1 - z_k for k=1,...,n. So maybe the product or sum of these terms can be related to the coefficients of the polynomial whose roots are w_k. Let's explore that.\n\nLet’s define the polynomial Q(w) = product_{k=1}^n (w - (1 - z_k)). Then, the sum S = sum_{k=1}^n 1/(1 - z_k) = sum_{k=1}^n 1/w_k. If we can find Q(0) and Q'(0), perhaps via Vieta's formula. The sum of 1/w_k is equal to - the coefficient of w^{n-1} in Q(w) divided by the coefficient of w^n in Q(w). Because Q(w) = product_{k=1}^n (w - w_k) = w^n + a_{n-1}w^{n-1} + ... + a_0. Then, the sum of 1/w_k is sum_{k=1}^n 1/w_k = (-a_{n-1})/a_n, but a_n is 1 here. So sum is -a_{n-1}. Therefore, if I can compute the coefficient a_{n-1} of Q(w), then S = -a_{n-1}.\n\nBut how to find Q(w)? Q(w) is the product over k=1 to n of (w - (1 - z_k)). Note that z_0=1, z_1,..., z_n are roots of z^{n+1} -1=0. Therefore, the roots w_k =1 - z_k for k=1,...,n correspond to 1 - z where z runs over the non-1 roots of z^{n+1}=1. So to find Q(w), we can write Q(w) = product_{k=1}^n (w - (1 - z_k)). Let's note that if we set w =1 - z, then Q(1 - z) = product_{k=1}^n (1 - z - (1 - z_k)) = product_{k=1}^n (z_k - z) = product_{k=1}^n ( - (z - z_k) ) = (-1)^n product_{k=1}^n (z - z_k). But the product_{k=1}^n (z - z_k) is equal to (z^{n+1} -1)/(z -1). Because the polynomial z^{n+1} -1 factors as (z -1)(z - z_1)...(z - z_n). Therefore, product_{k=1}^n (z - z_k) = (z^{n+1} -1)/(z -1). Therefore, Q(1 - z) = (-1)^n * (z^{n+1} -1)/(z -1). \n\nSo, Q(w) = (-1)^n * ( (1 - w)^{n+1} -1 ) / ( (1 - w) -1 ) = (-1)^n * ( (1 - w)^{n+1} -1 ) / (-w). Let's check that. If we set w =1 - z, then Q(w) = Q(1 - z) = (-1)^n * (z^{n+1} -1)/(z -1). But let's express this in terms of w:\n\nSo, z =1 - w. Therefore, Q(w) = (-1)^n * [ (1 - w)^{n+1} -1 ] / ( (1 - w) -1 ) = (-1)^n * [ (1 - w)^{n+1} -1 ] / (-w). Simplify the denominator: (1 - w) -1 = -w. Therefore, Q(w) = (-1)^n * [ (1 - w)^{n+1} -1 ] / (-w) = (-1)^{n} * [ (1 - w)^{n+1} -1 ] * (-1)/w = (-1)^{n+1} * [ (1 - w)^{n+1} -1 ] / w.\n\nTherefore, Q(w) = (-1)^{n+1} [ (1 - w)^{n+1} -1 ] / w.\n\nNow, expand (1 - w)^{n+1} using the binomial theorem: (1 - w)^{n+1} = 1 - (n+1)w + \\binom{n+1}{2}w^2 - ... + (-1)^{n+1}w^{n+1}.\n\nThen, (1 - w)^{n+1} -1 = - (n+1)w + \\binom{n+1}{2}w^2 - ... + (-1)^{n+1}w^{n+1}.\n\nDivide this by w: [ (1 - w)^{n+1} -1 ] / w = - (n+1) + \\binom{n+1}{2}w - ... + (-1)^{n+1}w^n.\n\nMultiply by (-1)^{n+1} to get Q(w):\n\nQ(w) = (-1)^{n+1} [ - (n+1) + \\binom{n+1}{2}w - ... + (-1)^{n+1}w^n ] = (n+1)(-1)^{n} + ... + (-1)^{2n+1}w^n.\n\nBut I need the coefficients of Q(w). Let's write Q(w) as:\n\nQ(w) = [ - (n+1)(-1)^{n} ] + [ \\binom{n+1}{2}(-1)^{n+1} ]w + ... + (-1)^{n+1}(-1)^{n+1}w^n. Wait, perhaps this is getting too complicated. Maybe instead, note that Q(w) is a polynomial of degree n. Let's express Q(w) as:\n\nQ(w) = (-1)^{n+1} * [ (1 - w)^{n+1} - 1 ] / w.\n\nLet me compute this expression:\n\nFirst, expand (1 - w)^{n+1}:\n\n(1 - w)^{n+1} = sum_{k=0}^{n+1} (-1)^k \\binom{n+1}{k} w^k.\n\nSubtract 1:\n\n(1 - w)^{n+1} -1 = sum_{k=1}^{n+1} (-1)^k \\binom{n+1}{k} w^k.\n\nDivide by w:\n\n[ (1 - w)^{n+1} -1 ] / w = sum_{k=1}^{n+1} (-1)^k \\binom{n+1}{k} w^{k-1} = sum_{m=0}^{n} (-1)^{m+1} \\binom{n+1}{m+1} w^m.\n\nTherefore, Q(w) = (-1)^{n+1} * sum_{m=0}^{n} (-1)^{m+1} \\binom{n+1}{m+1} w^m.\n\nSimplify the coefficients:\n\nEach term in the sum is (-1)^{m+1} \\binom{n+1}{m+1} w^m. Multiply by (-1)^{n+1}:\n\nCoefficient becomes (-1)^{n+1} * (-1)^{m+1} \\binom{n+1}{m+1} = (-1)^{n+1 + m +1} \\binom{n+1}{m+1} = (-1)^{n + m} \\binom{n+1}{m+1}.\n\nTherefore, Q(w) = sum_{m=0}^n (-1)^{n + m} \\binom{n+1}{m+1} w^m.\n\nBut we need the polynomial Q(w) to have the form w^n + a_{n-1} w^{n-1} + ... + a_0.\n\nWait, but let's note that in the expression above, when m=0, the term is (-1)^n * \\binom{n+1}{1} w^0 = (-1)^n (n+1). For m=1, it's (-1)^{n+1} * \\binom{n+1}{2} w^1, and so on, up to m=n, which is (-1)^{2n} \\binom{n+1}{n+1} w^n = (1) * 1 * w^n. Therefore, the leading term (w^n) has coefficient 1, as expected.\n\nTherefore, the coefficients of Q(w) are:\n\nFor w^m (from m=0 to n):\n\nCoefficient of w^m: (-1)^{n + m} \\binom{n+1}{m+1}.\n\nTherefore, the coefficient a_{n-1} (the coefficient of w^{n-1}) is (-1)^{n + (n - 1)} \\binom{n+1}{(n -1) +1} = (-1)^{2n -1} \\binom{n+1}{n} = (-1)^{2n -1} (n+1).\n\nBut (-1)^{2n -1} = (-1)^{2n} * (-1)^{-1} = (1)^n * (-1) = -1. Therefore, a_{n-1} = - (n+1).\n\nSimilarly, the constant term (w^0) is (-1)^n (n+1).\n\nBut recall that S = sum_{k=1}^n 1/w_k = -a_{n-1}, since in Q(w) = product_{k=1}^n (w - w_k), the sum of 1/w_k is -a_{n-1}/a_n. Since a_n is 1, it's just -a_{n-1}.\n\nFrom above, a_{n-1} = - (n+1), so S = - (- (n+1)) = n+1.\n\nWait, but that seems too straightforward. Wait, let me check again. The coefficient a_{n-1} is the coefficient of w^{n-1} in Q(w), which we found to be - (n+1). Therefore, sum_{k=1}^n 1/w_k = -a_{n-1} = -(- (n+1)) = n+1. So the answer is n+1?\n\nBut that seems counterintuitive. Let me test for small n.\n\nTake n=1: the equation is z^2 =1, so roots are 1 and -1. The sum is 1/(1 - (-1)) = 1/2. The answer should be 1/2. But according to the formula, n=1, so sum is n+1=2. That contradicts. Wait, that can't be right. So there must be a mistake here.\n\nWait, hold on. If n=1, then the sum is from z_1 to z_n=z_1, which is just one term: 1/(1 - (-1)) = 1/2. But according to the formula above, it would be 1 +1=2, which is clearly wrong. Therefore, my conclusion must be incorrect. So where's the mistake?\n\nLet me check the earlier steps. When n=1, Q(w) is the product of (w - (1 - z_1)), where z_1=-1. So Q(w) = w - (1 - (-1)) = w -2. Therefore, Q(w) = w -2. Then, sum of 1/w_k is 1/(2). But according to the coefficient a_{n-1}: here, n=1, so a_{0} is the coefficient of w^0 (the constant term), which is -2. Then, sum of 1/w_k = -a_{0}/a_1, since sum 1/w_k = -a_{n-1}/a_n. Wait, for n=1, the polynomial Q(w) is of degree 1: Q(w) = w -2. So a_1=1 (coefficient of w^1), a_0=-2. Then sum_{k=1}^1 1/w_k = -a_0 / a_1 = -(-2)/1=2. But actual sum is 1/(1 - (-1))=1/2. So there's a discrepancy. Therefore, my earlier reasoning has a flaw.\n\nWait, why is there this discrepancy? Let's revisit the step where S = sum 1/w_k = -a_{n-1}/a_n. Wait, for a general polynomial Q(w) = product_{k=1}^n (w - w_k) = w^n + a_{n-1} w^{n-1} + ... + a_0, then the sum of 1/w_k is sum_{k=1}^n 1/w_k = (-1)^{n-1} a_1 / a_n, by Vieta's formula. Wait, maybe I messed up the Vieta's formula here.\n\nWait, Vieta's formula relates the sum of reciprocals of roots. For a polynomial Q(w) = w^n + a_{n-1}w^{n-1} + ... + a_0, the sum of the reciprocals of the roots is sum_{k=1}^n 1/w_k = (-1)^{n-1} a_1 / a_n. Since a_n=1, it's (-1)^{n-1} a_1. So in our case, S = sum_{k=1}^n 1/w_k = (-1)^{n-1} a_1.\n\nBut in the previous calculation, for n=1, Q(w) = w -2, so a_1=1, a_0=-2. Therefore, sum 1/w_k = (-1)^{0} a_1 = 1*1=1. But actual sum is 1/(1 - (-1))=1/2. So again discrepancy. So this approach is wrong.\n\nTherefore, my initial assumption that S = -a_{n-1} is incorrect. Maybe I need to revisit Vieta's formula.\n\nWait, Vieta's formula for polynomial Q(w) = w^n + c_{n-1}w^{n-1} + ... + c_0. Then, the sum of the reciprocals of the roots is sum_{k=1}^n 1/w_k = (-1)^{n-1} c_1 / c_n. Since c_n=1, it's (-1)^{n-1} c_1. But in our case, the polynomial is Q(w) = product_{k=1}^n (w - w_k). Wait, no, the standard Vieta's formula for a monic polynomial with roots r_1,...,r_n is Q(w) = (w - r_1)...(w - r_n) = w^n - (sum r_1) w^{n-1} + ... + (-1)^n product r_i. Therefore, sum of reciprocals of roots is sum 1/r_i = (-1)^{n-1} (sum of products of roots taken n-1 at a time) / (product of roots). Because in the coefficients, c_{n-1} = -sum r_i, c_{1} = (-1)^{n-1} sum (products of r_i taken n-1 at a time), and c_0 = (-1)^n product r_i. Therefore, sum 1/r_i = sum (products of r_i taken n-1 at a time) / product r_i = [ (-1)^{n-1} c_1 ] / [ (-1)^n c_0 ] = -c_1 / c_0.\n\nTherefore, the correct formula is sum_{k=1}^n 1/r_k = -c_1 / c_0. Since for Q(w) = w^n + c_{n-1}w^{n-1} + ... + c_0, the sum 1/r_k is -c_1 / c_0.\n\nTherefore, in our case, we need to compute c_1 and c_0 for the polynomial Q(w). From earlier, we have:\n\nQ(w) = sum_{m=0}^n (-1)^{n + m} \\binom{n+1}{m+1} w^m.\n\nTherefore, c_0 is the constant term when m=0: (-1)^{n + 0} \\binom{n+1}{0+1} = (-1)^n \\binom{n+1}{1} = (-1)^n (n+1).\n\nSimilarly, c_1 is the coefficient of w^1, which is when m=1: (-1)^{n +1} \\binom{n+1}{2}.\n\nTherefore, sum_{k=1}^n 1/w_k = -c_1 / c_0 = - [ (-1)^{n+1} \\binom{n+1}{2} ] / [ (-1)^n (n+1) ].\n\nSimplify numerator and denominator:\n\nNumerator: - [ (-1)^{n+1} \\binom{n+1}{2} ] = - [ (-1)^{n} (-1) \\binom{n+1}{2} ] = - [ - (-1)^n \\binom{n+1}{2} ] = (-1)^n \\binom{n+1}{2}.\n\nDenominator: (-1)^n (n+1).\n\nSo the ratio is [ (-1)^n \\binom{n+1}{2} ] / [ (-1)^n (n+1) ] = \\binom{n+1}{2} / (n+1) ) = [ (n+1)n / 2 ] / (n+1) ) = n / 2.\n\nTherefore, sum_{k=1}^n 1/w_k = n / 2.\n\nWait, but in our earlier n=1 case, this gives 1/2, which matches the actual sum. For n=2, the sum should be 2/2=1. Let's check that. If n=2, the roots of z^3=1 are 1, e^(2πi/3), e^(4πi/3). So sum is 1/(1 - e^(2πi/3)) + 1/(1 - e^(4πi/3)). Let's compute these terms. First, e^(2πi/3) = -1/2 + i√3/2, 1 - e^(2πi/3) = 3/2 - i√3/2. The reciprocal is (3/2 + i√3/2)/ ( (3/2)^2 + (√3/2)^2 ) = (3/2 + i√3/2)/ (9/4 + 3/4 ) = (3/2 + i√3/2)/3 = (1/2 + i√3/6). Similarly for the other term: 1/(1 - e^(4πi/3)) = 1/(1 - (-1/2 - i√3/2)) = 1/(3/2 + i√3/2) = same as above but with conjugate denominator: (3/2 - i√3/2)/3 = 1/2 - i√3/6. Adding them: (1/2 + i√3/6) + (1/2 - i√3/6) = 1. Which matches n/2=2/2=1. So this seems correct.\n\nTherefore, the correct answer is n/2. Therefore, the sum S = sum_{k=1}^n 1/(1 - z_k) = n/2.\n\nSo where did I go wrong earlier? It seems like the initial approach using the derivative had an issue because the direct application gave an incorrect result, but when properly applying Vieta's formula considering the correct polynomial, we arrived at n/2.\n\nTherefore, the answer should be n divided by 2, boxed as \\boxed{\\dfrac{n}{2}}.\n\n**Final Answer**\nThe sum is equal to \\boxed{\\dfrac{n}{2}}.\n</think>To find the sum \\( \\sum_{k=1}^n \\frac{1}{1 - z_k} \\) where \\( z_0, z_1, \\ldots, z_n \\) are the \\((n+1)\\)th roots of unity, we proceed as follows:\n\n1. **Roots of Unity**: The roots \\( z_0, z_1, \\ldots, z_n \\) of the equation \\( z^{n+1} = 1 \\) are the \\((n+1)\\)th roots of unity. Specifically, \\( z_k = e^{2\\pi i k / (n+1)} \\) for \\( k = 0, 1, \\ldots, n \\), with \\( z_0 = 1 \\).\n\n2. **Polynomial Transformation**: Consider the polynomial \\( P(z) = z^{n+1} - 1 \\). The roots of this polynomial are \\( z_0, z_1, \\ldots, z_n \\). We are interested in the sum \\( \\sum_{k=1}^n \\frac{1}{1 - z_k} \\).\n\n3. **Transformed Polynomial**: Define \\( w_k = 1 - z_k \\). The polynomial \\( Q(w) \\) whose roots are \\( w_k \\) is given by:\n   \\[\n   Q(w) = \\prod_{k=1}^n (w - (1 - z_k)) = \\prod_{k=1}^n (w - w_k).\n   \\]\n   Using the substitution \\( w = 1 - z \\), we find:\n   \\[\n   Q(w) = (-1)^{n+1} \\frac{(1 - w)^{n+1} - 1}{w}.\n   \\]\n\n4. **Expansion and Coefficients**: Expand \\( (1 - w)^{n+1} \\) using the binomial theorem:\n   \\[\n   (1 - w)^{n+1} = \\sum_{m=0}^{n+1} \\binom{n+1}{m} (-w)^m.\n   \\]\n   Therefore,\n   \\[\n   (1 - w)^{n+1} - 1 = \\sum_{m=1}^{n+1} \\binom{n+1}{m} (-w)^m.\n   \\]\n   Dividing by \\( w \\), we get:\n   \\[\n   \\frac{(1 - w)^{n+1} - 1}{w} = \\sum_{m=1}^{n+1} \\binom{n+1}{m} (-w)^{m-1} = \\sum_{m=0}^{n} \\binom{n+1}{m+1} (-w)^m.\n   \\]\n   Thus,\n   \\[\n   Q(w) = (-1)^{n+1} \\sum_{m=0}^{n} \\binom{n+1}{m+1} (-1)^m w^m = \\sum_{m=0}^{n} (-1)^{n+1+m} \\binom{n+1}{m+1} w^m.\n   \\]\n\n5. **Vieta's Formula**: The sum \\( \\sum_{k=1}^n \\frac{1}{1 - z_k} \\) is the sum of the reciprocals of the roots of \\( Q(w) \\). By Vieta's formulas, the sum of the reciprocals of the roots of a polynomial \\( w^n + a_{n-1} w^{n-1} + \\cdots + a_0 \\) is given by \\( -\\frac{a_1}{a_0} \\).\n\n   For \\( Q(w) \\), the constant term \\( a_0 \\) is:\n   \\[\n   a_0 = (-1)^{n+1} \\binom{n+1}{1} = (-1)^{n+1} (n+1).\n   \\]\n   The coefficient of \\( w^1 \\) (i.e., \\( a_1 \\)) is:\n   \\[\n   a_1 = (-1)^{n+2} \\binom{n+1}{2} = (-1)^{n+2} \\frac{(n+1)n}{2}.\n   \\]\n\n6. **Final Calculation**: The sum is:\n   \\[\n   \\sum_{k=1}^n \\frac{1}{1 - z_k} = -\\frac{a_1}{a_0} = -\\frac{(-1)^{n+2} \\frac{(n+1)n}{2}}{(-1)^{n+1} (n+1)} = \\frac{n}{2}.\n   \\]\n\nThus, the sum \\( \\sum_{k=1}^n \\frac{1}{1 - z_k} \\) is \\(\\boxed{\\frac{n}{2}}\\)."}