{"row_number": 6052, "expected_answer": "155", "problem": "In how many ways can eight identical 5 ounce weights be distributed among five scales (A, B, C, D, and E) such that the combined weight on any one scale does not exceed 15 ounces?", "generated_solution": "<think>\nOkay, let's try to figure out how to solve this problem. So, we have eight identical 5-ounce weights that need to be distributed among five scales: A, B, C, D, and E. The key constraint is that no scale can have more than 15 ounces. Since each weight is 5 ounces, that means each scale can hold at most 3 weights because 3*5=15. If we tried to put 4 weights on a scale, that would be 20 ounces, which is over the limit. So each scale can have 0, 1, 2, or 3 weights.\n\nNow, the problem is similar to a classic stars and bars problem where we distribute identical items into different bins with some restrictions. The standard stars and bars formula for distributing n identical items into k bins is C(n + k - 1, k - 1), but here we have an upper limit on each bin. So we need to adjust for that.\n\nFirst, let's rephrase the problem. Since each weight is 5 ounces, the number of weights per scale directly translates to the ounces: 0 weights is 0 ounces, 1 is 5, 2 is 10, 3 is 15. So each scale can have 0, 1, 2, or 3 weights. We have to distribute 8 weights among 5 scales with each scale having at most 3 weights.\n\nWithout any restrictions, the number of ways would be C(8 + 5 - 1, 5 - 1) = C(12, 4) = 495. But we need to subtract the distributions where at least one scale has more than 3 weights.\n\nBut here's the thing: since we're only distributing 8 weights, and each over-limit would require at least 4 weights on a scale. However, 8 weights can't have more than two scales exceeding the limit (since 4+4=8, but we have 5 scales). Wait, but actually, even if we tried to put 4 weights on one scale, that's 4, and the remaining 4 weights could be spread among the other scales. Hmm, but maybe the inclusion-exclusion principle is the way to go here.\n\nInclusion-exclusion for upper limits. Let's recall the formula. The number of non-negative integer solutions to x1 + x2 + ... + x5 = 8 where each xi ≤ 3 is equal to:\n\nC(8 + 5 -1, 5 -1) - C(5,1)*C(8 - 4 + 5 -1, 5 -1) + C(5,2)*C(8 - 8 + 5 -1, 5 -1) - ... etc.\n\nWait, but let's check if that makes sense. The standard inclusion-exclusion for upper bounds. Each term accounts for subtracting the cases where one variable is ≥4, then adding back cases where two variables are ≥4, etc.\n\nBut in our case, since we have 8 weights total, if we try to subtract distributions where one scale has 4 or more, we have to compute the number of distributions where at least one scale has ≥4. Let me try to formalize this.\n\nLet S be the set of all distributions without any restrictions, which is C(12, 4) = 495.\n\nNow, let A_i be the set of distributions where scale i has at least 4 weights. We need to compute |S| - |A1 ∪ A2 ∪ A3 ∪ A4 ∪ A5|.\n\nBy the inclusion-exclusion principle:\n\n|A1 ∪ ... ∪ A5| = Σ|Ai| - Σ|Ai ∩ Aj| + Σ|Ai ∩ Aj ∩ Ak| - ... + (-1)^{m+1}|A1 ∩ ... ∩ Am}|...\n\nBut since we have only 8 weights, how many scales can have ≥4? If one scale has 4, the remaining 4 weights can be distributed among the other 4 scales. If two scales have 4 each, that's 8 weights total, so the rest would have 0. So we can have at most two scales exceeding the limit (if two scales each have 4, but 4+4=8, so exactly two scales with 4 and the rest with 0). Alternatively, if one scale has 5, but 5 weights would be 25 ounces, but the total weights are 8, so that's not possible. Wait, actually, since we have only 8 weights total, the maximum number of scales that can have ≥4 is two, as 4+4=8. So intersections beyond two scales (like three scales each having ≥4) would sum to at least 12 weights, which is more than 8. So those terms would be zero.\n\nSo let's compute |Ai|: For each Ai, number of distributions where scale i has at least 4. Let's model this as: set yi = xi - 4, where yi ≥0. Then the equation becomes y1 + x2 + x3 + x4 + x5 = 8 -4 =4. So the number of solutions is C(4 +5 -1,5 -1) = C(8,4) = 70. Since there are 5 scales, Σ|Ai| = 5 *70 = 350.\n\nNext, |Ai ∩ Aj|: distributions where scales i and j each have at least 4. Let’s set yi = xi -4 and yj = xj -4. Then total remaining weights: 8 -4 -4 =0. So the equation is yi + yj + xk + xl + xm =0. There's only one solution here, which is all variables are 0. So the number of solutions is 1. Since we have to choose 2 scales, there are C(5,2) =10 such pairs. So Σ|Ai ∩ Aj| =10*1=10.\n\nHigher intersections (three or more scales) would require at least 12 weights, which is impossible, so their contributions are 0.\n\nTherefore, by inclusion-exclusion:\n\n|A1 ∪ ... ∪ A5| = 350 -10 +0 -0 +...= 340.\n\nWait, no, inclusion-exclusion alternates signs. So:\n\n|A1 ∪ ... ∪ A5| = Σ|Ai| - Σ|Ai ∩ Aj| + Σ|Ai ∩ Aj ∩ Ak| - ... \n\nBut since for three or more, the terms are zero, it's just 350 -10 =340.\n\nTherefore, the number of valid distributions is total S minus |A1 ∪ ... ∪ A5|:\n\n495 -340=155.\n\nWait, so 155. Hmm.\n\nBut let me verify this with another approach. Maybe generating functions?\n\nThe generating function for each scale is (1 + x + x^2 + x^3), since each scale can have 0,1,2,3 weights. We need the coefficient of x^8 in (1 + x + x^2 + x^3)^5.\n\nLet me compute this. Alternatively, maybe expand (1 + x + x^2 + x^3)^5.\n\nAlternatively, note that (1 + x + x^2 + x^3) = (1 - x^4)/(1 - x). So the generating function is (1 - x^4)^5 * (1 - x)^{-5}.\n\nThen, the coefficient of x^8 is the same as the coefficient of x^8 in (1 - 5x^4 + 10x^8 - ...) * sum_{k=0}^∞ C(k +4,4) x^k.\n\nSo expanding this, the coefficient of x^8 is C(8 +4,4) -5*C(4 +4,4) +10*C(0 +4,4). Let me compute that.\n\nC(12,4) -5*C(8,4) +10*C(4,4).\n\nCompute each term:\n\nC(12,4)= 495.\n\nC(8,4)=70.\n\nC(4,4)=1.\n\nSo 495 -5*70 +10*1 =495 -350 +10=155. Yep, same result. So that's confirming the inclusion-exclusion result. So the answer is 155.\n\nAlternatively, perhaps even brute-force by considering possible distributions. But since the two methods gave the same answer, I think that's solid. So I think 155 is the correct answer.\n\n**Final Answer**\nThe number of ways is \\boxed{155}.\n</think>To solve the problem of distributing eight identical 5-ounce weights among five scales (A, B, C, D, and E) such that no scale exceeds 15 ounces (i.e., each scale can hold at most 3 weights), we use the principle of inclusion-exclusion and generating functions.\n\n### Step-by-Step Solution:\n\n1. **Stars and Bars without Restrictions**:\n   First, we calculate the number of ways to distribute 8 weights among 5 scales without any restrictions using the stars and bars theorem:\n   \\[\n   \\binom{8 + 5 - 1}{5 - 1} = \\binom{12}{4} = 495\n   \\]\n\n2. **Inclusion-Exclusion Principle**:\n   We need to subtract the cases where any scale exceeds 3 weights.\n\n   - **Single Scale Exceeding Limit**:\n     For each scale having at least 4 weights, we subtract these cases. If one scale has at least 4 weights, we are left with 4 weights to distribute among the remaining 4 scales:\n     \\[\n     5 \\times \\binom{8 - 4 + 5 - 1}{5 - 1} = 5 \\times \\binom{8}{4} = 5 \\times 70 = 350\n     \\]\n\n   - **Two Scales Exceeding Limit**:\n     For each pair of scales having at least 4 weights, we add these cases back. If two scales each have at least 4 weights, we are left with 0 weights to distribute among the remaining 3 scales:\n     \\[\n     \\binom{5}{2} \\times \\binom{8 - 4 - 4 + 5 - 1}{5 - 1} = 10 \\times \\binom{0 + 4}{4} = 10 \\times 1 = 10\n     \\]\n\n   Applying the inclusion-exclusion principle:\n   \\[\n   |A_1 \\cup A_2 \\cup A_3 \\cup A_4 \\cup A_5| = 350 - 10 = 340\n   \\]\n\n3. **Valid Distributions**:\n   Subtract the invalid distributions from the total:\n   \\[\n   495 - 340 = 155\n   \\]\n\n4. **Verification with Generating Functions**:\n   The generating function for each scale is \\((1 + x + x^2 + x^3)\\). We need the coefficient of \\(x^8\\) in \\((1 + x + x^2 + x^3)^5\\):\n   \\[\n   (1 + x + x^2 + x^3)^5 = \\left(\\frac{1 - x^4}{1 - x}\\right)^5 = (1 - x^4)^5 \\cdot (1 - x)^{-5}\n   \\]\n   The coefficient of \\(x^8\\) in this expansion is:\n   \\[\n   \\binom{12}{4} - 5 \\binom{8}{4} + 10 \\binom{4}{4} = 495 - 350 + 10 = 155\n   \\]\n\nThus, the number of ways to distribute the weights is \\(\\boxed{155}\\)."}