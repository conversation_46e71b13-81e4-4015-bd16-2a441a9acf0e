{"row_number": 6130, "expected_answer": "\\( m = 14 \\)", "problem": "Determine a natural number \\( m \\) for which the fractional part of \\( m \\) is equal to the fractional part of \\( \\sqrt{m + 2011} \\).", "generated_solution": "<think>\nOkay, so I need to find a natural number m such that the fractional part of m is equal to the fractional part of the square root of (m + 2011). Hmm, wait, m is a natural number, which means it's a positive integer. But the fractional part of m? Wait, but m is an integer, so its fractional part should be zero, right? Because integers don't have a fractional component. So, if the fractional part of m is zero, then the fractional part of sqrt(m + 2011) must also be zero. That would mean sqrt(m + 2011) is an integer. Because if the fractional part is zero, then the number itself is an integer. So, maybe the problem is asking for m such that sqrt(m + 2011) is an integer. Because the fractional part of m is zero, so we need sqrt(m + 2011) to also have a fractional part of zero, meaning it's an integer. Therefore, m + 2011 must be a perfect square. So, m is a natural number, so m + 2011 is a perfect square greater than 2011. Therefore, m = k^2 - 2011, where k is an integer such that k^2 > 2011. But we need to check if m is a natural number, so k^2 - 2011 must be positive. So k must be greater than sqrt(2011). Let me compute sqrt(2011). Let's see, 44^2 is 1936, 45^2 is 2025. So sqrt(2011) is between 44 and 45. So k must be at least 45. Then m would be 45^2 - 2011 = 2025 - 2011 = 14. Then m = 14. Wait, but let me check that. If m = 14, then m + 2011 = 2025, sqrt(2025) is 45, which is an integer, so fractional part is zero. The fractional part of m is fractional part of 14, which is zero. So they are equal. So that works. But wait, is there another k? For example, k=46, then m = 46^2 -2011 = 2116 -2011 = 105. Then m=105. Then sqrt(105 +2011)=sqrt(2116)=46. The fractional part is zero, which equals fractional part of m=105, which is zero. So that works too. Similarly, k=47 would give m=47^2 -2011=2209 -2011=198. So m=198. Then sqrt(198+2011)=sqrt(2209)=47, which is integer. So fractional part zero. Therefore, there are infinitely many m satisfying this? Wait, but the problem says \"determine a natural number m\". So maybe they just want one example. But maybe there's something wrong here. Wait, let's read the problem again.\n\n\"Determine a natural number m for which the fractional part of m is equal to the fractional part of sqrt(m + 2011).\"\n\nBut since m is a natural number, fractional part of m is zero, so fractional part of sqrt(m +2011) must be zero. Therefore, sqrt(m +2011) must be integer. So m +2011 must be a perfect square, so m = k^2 -2011 for some integer k where k^2 >2011. So the minimal such m is 45^2 -2011=14, then 46^2-2011=105, etc. But perhaps the problem is looking for any such m, so maybe 14 is the answer. Wait, but let me confirm. Let me check m=14. The fractional part of m is 0. The sqrt(14 + 2011) is sqrt(2025)=45, which also has fractional part 0. So yes, 0=0. Similarly for 105, 198, etc. So there are infinitely many solutions. But the problem says \"determine a natural number m\". So maybe any of them is acceptable, so the answer is m=14. Alternatively, maybe I'm missing something. Wait, perhaps I made a mistake in interpreting the problem.\n\nWait, the problem says \"the fractional part of m is equal to the fractional part of sqrt(m + 2011)\". But m is a natural number, so fractional part is zero. Therefore, sqrt(m +2011) must have fractional part zero. Therefore, m +2011 must be a perfect square. Therefore, m = k^2 -2011. So the answer would be m=14, as per k=45. So is the answer 14? But let me check if there's another interpretation. Wait, could the problem have a typo? Maybe the fractional part of m is equal to the fractional part of sqrt(m) +2011? But the problem says \"the fractional part of m is equal to the fractional part of sqrt(m + 2011)\". So the way it's written, sqrt(m +2011). So I think my initial approach is correct. Therefore, the answer is m=14. But maybe the problem is intended to have a unique solution, but according to the logic, there are infinitely many solutions. But since the problem says \"determine a natural number\", maybe they just need one such number. So the smallest one would be 14. Let me check with m=14. Then sqrt(14+2011)=sqrt(2025)=45. The fractional parts of 14 and 45 are both zero. So yes, it works. Therefore, the answer is 14. Alternatively, maybe there's another approach where the fractional parts are not zero? Wait, but m is a natural number, so its fractional part must be zero. Therefore, the fractional part of sqrt(m +2011) must also be zero, so sqrt(m +2011) must be an integer. So yes, m must be a perfect square minus 2011. Therefore, the answer is 14. Hmm. Maybe the problem wants the minimal solution. So 14 is the answer. So I think that's the answer.\n</think>To determine a natural number \\( m \\) for which the fractional part of \\( m \\) is equal to the fractional part of \\( \\sqrt{m + 2011} \\), we start by noting that the fractional part of a natural number \\( m \\) is always zero. Therefore, the fractional part of \\( \\sqrt{m + 2011} \\) must also be zero, implying that \\( \\sqrt{m + 2011} \\) is an integer. Let \\( \\sqrt{m + 2011} = k \\) for some integer \\( k \\). Then,\n\n\\[\nm + 2011 = k^2 \\implies m = k^2 - 2011\n\\]\n\nTo ensure \\( m \\) is a natural number, \\( k^2 \\) must be greater than 2011. The smallest integer \\( k \\) that satisfies this condition is \\( k = 45 \\) because \\( 44^2 = 1936 \\) and \\( 45^2 = 2025 \\). Thus,\n\n\\[\nm = 45^2 - 2011 = 2025 - 2011 = 14\n\\]\n\nWe verify that \\( m = 14 \\) satisfies the original condition. The fractional part of \\( m = 14 \\) is zero, and\n\n\\[\n\\sqrt{14 + 2011} = \\sqrt{2025} = 45\n\\]\n\nThe fractional part of 45 is also zero. Therefore, the natural number \\( m \\) that satisfies the given condition is\n\n\\[\n\\boxed{14}\n\\]"}