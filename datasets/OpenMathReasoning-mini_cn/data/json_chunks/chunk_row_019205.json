{"row_number": 19205, "expected_answer": "\\(2\\pi i\\)", "problem": "Evaluate the integral $\\int_{C}\\frac{e^z}{z}dz$ over the contour $C=|z|=1$, described once, counterclockwise.", "generated_solution": "<think>\nOkay, so I need to evaluate the integral of e^z divided by z over the contour C, which is the circle with radius 1 centered at the origin, traversed once counterclockwise. Hmm, let's see. I remember that in complex analysis, integrals around closed contours can often be evaluated using <PERSON><PERSON><PERSON>'s integral formula or the residue theorem. Let me recall what those say.\n\n<PERSON><PERSON><PERSON>'s integral formula states that if a function f(z) is analytic inside and on a simple closed contour C, and a point a is inside C, then the integral of f(z)/(z - a) around C is 2πi times f(a). The residue theorem generalizes this for functions with isolated singularities, where the integral is 2πi times the sum of residues inside the contour.\n\nIn this problem, the integrand is e^z / z. So, let me write that as f(z)/ (z - a) where f(z) = e^z and a = 0. Wait, yes, because z is the denominator, so it's like (z - 0), so a is 0. Now, the contour is |z| = 1, which encloses the point z = 0. \n\nSince e^z is entire (analytic everywhere), it's definitely analytic inside and on the contour C. Therefore, by <PERSON><PERSON><PERSON>'s integral formula, the integral should be 2πi times f(0). Since f(z) = e^z, f(0) is e^0 which is 1. Therefore, the integral is 2πi * 1 = 2πi.\n\nWait, let me double-check if there are any singularities. The integrand has a singularity at z = 0 because the denominator becomes zero there. Since the contour C is |z| = 1, which encloses the singularity at 0, so the integral is indeed covered by Cauchy's formula. The residue at z=0 would be the coefficient of 1/z in the Laurent series of e^z around 0. But since the Laurent series of e^z is the same as its Taylor series because e^z is entire. The expansion is 1 + z + z²/2! + z³/3! + ... So, dividing by z gives 1/z + 1 + z/2! + z²/3! + ... So, the residue is the coefficient of 1/z, which is 1. Then, by the residue theorem, the integral is 2πi times 1, which again gives 2πi. So both methods agree. So I think the answer is 2πi. \n\nI don't see any mistakes here. The key points are recognizing that the integral is in the form required by Cauchy's formula, that the function e^z is analytic everywhere, and that the residue at 0 is 1. The contour encloses the singularity at 0, so everything checks out. Therefore, the value of the integral should be 2πi.\n</think>To evaluate the integral \\(\\int_{C} \\frac{e^z}{z} \\, dz\\) over the contour \\(C = |z| = 1\\), described once counterclockwise, we can use Cauchy's integral formula. \n\nCauchy's integral formula states that for a function \\( f(z) \\) analytic inside and on a simple closed contour \\( C \\) and a point \\( a \\) inside \\( C \\),\n\\[\n\\int_{C} \\frac{f(z)}{z - a} \\, dz = 2\\pi i f(a).\n\\]\n\nIn this problem, the integrand is \\(\\frac{e^z}{z}\\). We can write this as \\(\\frac{f(z)}{z - 0}\\) where \\( f(z) = e^z \\) and \\( a = 0 \\). The contour \\( C \\) is \\( |z| = 1 \\), which encloses the point \\( z = 0 \\).\n\nSince \\( e^z \\) is an entire function (analytic everywhere), it is analytic inside and on the contour \\( C \\). By Cauchy's integral formula, the integral becomes\n\\[\n\\int_{C} \\frac{e^z}{z} \\, dz = 2\\pi i \\cdot e^0.\n\\]\n\nSince \\( e^0 = 1 \\), we have\n\\[\n\\int_{C} \\frac{e^z}{z} \\, dz = 2\\pi i \\cdot 1 = 2\\pi i.\n\\]\n\nTo confirm, we can also use the residue theorem. The residue theorem states that for a function \\( f(z) \\) with isolated singularities inside a closed contour \\( C \\),\n\\[\n\\int_{C} f(z) \\, dz = 2\\pi i \\sum \\text{Res}(f, a_i),\n\\]\nwhere the sum is over all residues of \\( f(z) \\) at the singularities \\( a_i \\) inside \\( C \\).\n\nThe function \\(\\frac{e^z}{z}\\) has a simple pole at \\( z = 0 \\). The residue of \\(\\frac{e^z}{z}\\) at \\( z = 0 \\) is the coefficient of the \\(\\frac{1}{z}\\) term in the Laurent series expansion of \\( e^z \\) around \\( z = 0 \\). The Laurent series of \\( e^z \\) around \\( z = 0 \\) is\n\\[\ne^z = 1 + z + \\frac{z^2}{2!} + \\frac{z^3}{3!} + \\cdots.\n\\]\n\nDividing by \\( z \\), we get\n\\[\n\\frac{e^z}{z} = \\frac{1}{z} + 1 + \\frac{z}{2!} + \\frac{z^2}{3!} + \\cdots.\n\\]\n\nThe coefficient of \\(\\frac{1}{z}\\) is \\( 1 \\). Therefore, the residue at \\( z = 0 \\) is \\( 1 \\). By the residue theorem,\n\\[\n\\int_{C} \\frac{e^z}{z} \\, dz = 2\\pi i \\cdot 1 = 2\\pi i.\n\\]\n\nThus, the value of the integral is\n\\[\n\\boxed{2\\pi i}.\n\\]"}