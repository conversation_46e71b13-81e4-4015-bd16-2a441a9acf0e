{"row_number": 6419, "expected_answer": "30", "problem": "Find \\( n \\) such that \\( 120^4 + 272^4 + 315^4 + n^4 = 353^4 \\).", "generated_solution": "<think>\nOkay, let me try to figure out how to solve this problem. So we need to find n such that 120^4 + 272^4 + 315^4 + n^4 equals 353^4. Hmm, that seems like a Di<PERSON>hantine equation where we have to find an integer n that satisfies this equation. \n\nFirst, I think the straightforward approach is to compute each term, sum them up, and then subtract the sum of the known terms from 353^4 to find n^4. Then take the fourth root to get n. But since these numbers are pretty big, calculating them directly might be tedious. Let's see if there's a smarter way or maybe some pattern or theorem that can help here.\n\nBut maybe I should just start by calculating each term step by step. Let me start by computing 120^4. Since 120 squared is 14,400, then 14,400 squared is 120^4. Wait, 14,400 squared. Let me compute that. 14,400 * 14,400. 14,400 is 144 * 100, so squared is (144)^2 * (100)^2 = 20736 * 10,000 = 207,360,000. So 120^4 is 207,360,000.\n\nNext, 272^4. Let me compute 272 squared first. 272 * 272. Let's break it down: 270^2 + 2*270*2 + 2^2. Wait, maybe it's easier to compute directly. 272*200 = 54,400. 272*70 = 19,040. 272*2 = 544. So 54,400 + 19,040 = 73,440 + 544 = 73,984. So 272 squared is 73,984. Then 73,984 squared is 272^4. Hmm, that's a big number. Let's see. Let me compute 73,984 * 73,984. Maybe we can use a formula or break it down. Alternatively, maybe approximate first and check.\n\nWait, but maybe I should just compute it step by step. Alternatively, since 73,984 is 73,984, let's see:\n\n(70,000 + 3,984)^2 = 70,000^2 + 2*70,000*3,984 + 3,984^2\n\n70,000^2 is 4,900,000,000.\n\n2*70,000*3,984 = 140,000*3,984. Let me compute 140,000*3,984. 140,000*3,000 = 420,000,000; 140,000*984 = 140,000*(900 + 84) = 140,000*900 = 126,000,000; 140,000*84 = 11,760,000. So 126,000,000 + 11,760,000 = 137,760,000. So total for 2*70,000*3,984 is 420,000,000 + 137,760,000 = 557,760,000.\n\nNow 3,984^2: 3,984*3,984. Let's compute that. Let's think (4,000 - 16)^2 = 4,000^2 - 2*4,000*16 + 16^2 = 16,000,000 - 128,000 + 256 = 16,000,000 - 128,000 is 15,872,000; plus 256 is 15,872,256.\n\nSo adding up the three parts: 4,900,000,000 + 557,760,000 = 5,457,760,000. Then plus 15,872,256 gives 5,457,760,000 + 15,872,256 = 5,473,632,256. Wait, but wait, no. Let me check again: 4,900,000,000 + 557,760,000 is 5,457,760,000. Then adding 15,872,256 gives 5,457,760,000 + 15,872,256 = 5,473,632,256. So 272^4 is 5,473,632,256. Hmm, that's a big number.\n\nNow 315^4. Let's compute that. First, 315 squared. 315*315. Let's do 300*300 = 90,000. Then 300*15 + 15*300 = 4,500 + 4,500 = 9,000. Then 15*15 = 225. So total is 90,000 + 9,000 + 225 = 99,225. So 315 squared is 99,225. Then 99,225 squared is 315^4.\n\nCalculating 99,225 squared. Let's write it as (100,000 - 775)^2. That would be 100,000^2 - 2*100,000*775 + 775^2. Compute each term:\n\n100,000^2 = 10,000,000,000\n\n2*100,000*775 = 200,000*775. Let's compute 200,000 * 700 = 140,000,000; 200,000*75 = 15,000,000. So total 140,000,000 + 15,000,000 = 155,000,000.\n\n775^2: 700^2 + 2*700*75 + 75^2 = 490,000 + 105,000 + 5,625 = 600,625.\n\nSo putting it all together: 10,000,000,000 - 155,000,000 = 9,845,000,000. Then subtract 155,000,000 from 10,000,000,000, which is 9,845,000,000. Then add 600,625? Wait, no: the expansion is 10,000,000,000 - 155,000,000 + 600,625. Wait, no, the formula is (a - b)^2 = a^2 - 2ab + b^2. So here, it's 100,000^2 - 2*100,000*775 + 775^2. So subtract 155,000,000 and add 600,625. Therefore:\n\n10,000,000,000 - 155,000,000 = 9,845,000,000. Then 9,845,000,000 + 600,625 = 9,845,600,625. So 315^4 is 9,845,600,625.\n\nNow, let's compute the sum of the three known terms:\n\n120^4: 207,360,000\n\n272^4: 5,473,632,256\n\n315^4: 9,845,600,625\n\nAdding them up. Let's add 207,360,000 + 5,473,632,256 first. 207,360,000 + 5,473,632,256. Let's break it down:\n\n5,473,632,256 + 200,000,000 = 5,673,632,256\n\nThen add 7,360,000: 5,673,632,256 + 7,360,000 = 5,680,992,256\n\nSo total so far is 5,680,992,256.\n\nNow add 9,845,600,625 to this. Let's compute 5,680,992,256 + 9,845,600,625.\n\nAdding 5,680,992,256 + 9,845,600,625. Let's line them up:\n\n5,680,992,256\n\n+9,845,600,625\n\n= (5,680,992,256 + 9,845,600,625) = 15,526,592,881.\n\nWait, 5,680,992,256 + 9,845,600,625. Let's do the addition step by step.\n\nFirst, 5,680,992,256 + 9,000,000,000 = 14,680,992,256\n\nThen add 845,600,625: 14,680,992,256 + 845,600,625. \n\nAdding 14,680,992,256 + 800,000,000 = 15,480,992,256\n\nThen 45,600,625: 15,480,992,256 + 45,600,625 = 15,526,592,881.\n\nSo the sum of the three terms is 15,526,592,881.\n\nNow, the total equation is 15,526,592,881 + n^4 = 353^4.\n\nSo n^4 = 353^4 - 15,526,592,881.\n\nTherefore, I need to compute 353^4 first.\n\nLet's compute 353^2 first. 353*353.\n\nBreak it down as (300 + 53)^2 = 300^2 + 2*300*53 + 53^2.\n\n300^2 = 90,000.\n\n2*300*53 = 600*53 = 31,800.\n\n53^2 = 2,809.\n\nSo 90,000 + 31,800 = 121,800 + 2,809 = 124,609. So 353 squared is 124,609.\n\nNow, 353^4 is (353^2)^2 = 124,609^2. Let's compute that. That's a big number. Let's see if I can compute it efficiently.\n\n124,609^2. Let's try using the identity (a + b)^2 where a = 124,000 and b = 609.\n\nThen (a + b)^2 = a^2 + 2ab + b^2.\n\nCompute each term:\n\na^2 = (124,000)^2 = (124)^2 * (1,000)^2 = 15,376 * 1,000,000 = 15,376,000,000.\n\n2ab = 2 * 124,000 * 609. Let's compute 124,000 * 609 first.\n\n124,000 * 600 = 74,400,000\n\n124,000 * 9 = 1,116,000\n\nTotal: 74,400,000 + 1,116,000 = 75,516,000\n\nThen multiply by 2: 151,032,000.\n\nb^2 = 609^2. Let's compute that: 600^2 + 2*600*9 + 9^2 = 360,000 + 10,800 + 81 = 370,881.\n\nSo adding up the three parts:\n\n15,376,000,000 + 151,032,000 = 15,527,032,000\n\nThen adding 370,881: 15,527,032,000 + 370,881 = 15,527,402,881.\n\nSo 353^4 is 15,527,402,881.\n\nNow, going back to the equation: n^4 = 353^4 - sum of the other three terms. So n^4 = 15,527,402,881 - 15,526,592,881.\n\nLet's compute that subtraction: 15,527,402,881 - 15,526,592,881.\n\nSubtract 15,526,592,881 from 15,527,402,881. The difference is (15,527,402,881 - 15,526,592,881) = 810,000.\n\nSo n^4 = 810,000. Now we need to find n such that n^4 = 810,000.\n\nWait a minute, 810,000. Let's see. Let's factor 810,000 to find the fourth root. 810,000 is 81 * 10,000. 81 is 9^2, 10,000 is 100^2. So 810,000 = 9^2 * 100^2 = (9*100)^2 = 900^2. So 810,000 is 900 squared, which is 30^4. Wait, wait, 30^4 is 30*30*30*30 = 900*900 = 810,000. So yes, 30^4 is 810,000. Therefore, n^4 = 30^4, so n = ±30. But since the problem is about fourth powers, which are always non-negative, and given that the original problem probably expects a positive integer, n = 30.\n\nWait, but let me verify. Let me compute 30^4. 30^2 = 900. 900^2 = 810,000. Yep, so 30^4 = 810,000. So that checks out. Therefore, n = 30.\n\nWait, but let me check if there could be any other solutions. Since we're dealing with real numbers, technically, n could also be negative, but since the problem doesn't specify, and given that in most contexts, especially if n is a natural number, the answer is 30. So I think that's the solution.\n\nBut let me verify all calculations again to make sure I didn't make any errors. Because sometimes when dealing with such large numbers, it's easy to slip up.\n\nFirst, 120^4: 120^2 is 14,400, squared is 207,360,000. That's correct.\n\n272^2: 272*272. Let me verify that. 272*200 = 54,400; 272*70 = 19,040; 272*2 = 544. Adding those: 54,400 + 19,040 = 73,440 + 544 = 73,984. Correct. Then 73,984 squared. Wait, I used the formula and got 5,473,632,256. Let me check with a calculator. Wait, but since I can't use a calculator, maybe compute a part again. Wait, 73,984 * 73,984. Hmm, maybe this is correct. Since 70,000 squared is 4.9e9, which we added to 557,760,000 and 15,872,256. Wait, maybe that's correct. Let me take 73,984^2 = 5,473,632,256. Let me confirm by another method. Let's consider 73,984 * 73,984.\n\nBut this might take too long. Let's tentatively accept that step.\n\n315^4: 315^2 is 99,225. Then 99,225^2: we used (100,000 - 775)^2 and got 9,845,600,625. Let me check that. 100,000^2 is 10,000,000,000. Then subtracting 2*100,000*775 which is 155,000,000, so 10,000,000,000 - 155,000,000 = 9,845,000,000. Then adding 775^2 which is 600,625. So total 9,845,000,000 + 600,625 = 9,845,600,625. Correct.\n\nSum of 120^4 + 272^4 + 315^4: 207,360,000 + 5,473,632,256 + 9,845,600,625.\n\nAdding first two: 207,360,000 + 5,473,632,256 = 5,680,992,256. Then adding 9,845,600,625: 5,680,992,256 + 9,845,600,625. Let me check again:\n\n5,680,992,256 + 9,845,600,625. Since 5,680,992,256 + 9,845,600,625. Let's add the billions: 5 + 9 = 14 billion. Then the millions: 680 million + 845 million = 1,525 million. Then the thousands and units: 992,256 + 600,625 = 1,592,881. Wait, no, perhaps I should align properly:\n\n5,680,992,256\n\n+9,845,600,625\n\n= (5,680,992,256 + 9,845,600,625) = \n\nStarting from the units:\n\n6 + 5 = 11, but okay, maybe better to think as:\n\n5,680,992,256\n\n+9,845,600,625\n\n= 5,680,992,256 + 9,845,600,625 = \n\nFirst, 5,680,992,256 + 9,000,000,000 = 14,680,992,256\n\nThen add 845,600,625: 14,680,992,256 + 800,000,000 = 15,480,992,256\n\nThen add 45,600,625: 15,480,992,256 + 45,600,625 = 15,526,592,881. Correct.\n\nThen 353^4 is 15,527,402,881. Subtracting 15,526,592,881 gives 810,000. Correct.\n\nTherefore, n^4 = 810,000. Wait, but that's 30^4. Wait, 30^4 is 30*30*30*30 = 810,000. Yes. So n = 30. That makes sense.\n\nSo the answer is 30. Let me just check once more. If we plug n = 30 into the original equation:\n\n120^4 + 272^4 + 315^4 + 30^4 = 353^4.\n\nWe've already calculated the sum of the first three terms as 15,526,592,881. Adding 30^4 (810,000) gives 15,526,592,881 + 810,000 = 15,527,402,881, which is equal to 353^4 as calculated. Therefore, it checks out. So n is indeed 30.\n</think>!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!"}