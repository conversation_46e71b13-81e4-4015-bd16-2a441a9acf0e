{"row_number": 6409, "expected_answer": "\\(\\frac{1}{2}\\)", "problem": "Calculate the sum\n\\[\n\\sum_{n=0}^{\\infty} \\frac{n}{n^4 + n^2 + 1}\n\\]", "generated_solution": "<think>\nOkay, so I have this infinite series to solve: the sum from n=0 to infinity of n divided by (n^4 + n^2 + 1). Hmm, let's see how to approach this. I remember that when dealing with sums of rational functions, partial fractions might be useful. But first, maybe I should factor the denominator to see if it can be broken down into simpler terms.\n\nThe denominator is n^4 + n^2 + 1. Hmm, quartic equation. Let me try to factor it. Maybe it factors into quadratics. Let me suppose that n^4 + n^2 + 1 can be written as (n^2 + an + b)(n^2 + cn + d). Expanding that would give n^4 + (a + c)n^3 + (ac + b + d)n^2 + (ad + bc)n + bd. Comparing coefficients with n^4 + 0n^3 + 1n^2 + 0n + 1, so:\n\n1. The coefficient of n^3: a + c = 0\n2. The coefficient of n^2: ac + b + d = 1\n3. The coefficient of n: ad + bc = 0\n4. The constant term: bd = 1\n\nFrom equation 1: c = -a.\n\nPlugging c = -a into equation 3: ad + b(-a) = 0 => a(d - b) = 0. So either a=0 or d = b.\n\nIf a=0, then c=0. Then equation 2: 0 + b + d =1. And equation 4: bd=1. If a=0, then we have two equations: b + d =1 and bd=1. Let's see if that's possible. If b + d =1 and bd=1, then the quadratic equation x^2 - x +1=0. The discriminant is 1 -4 = -3 <0, so no real solutions. Therefore, a can't be 0. So the other possibility is d = b.\n\nSo d = b. Then equation 4: b^2=1, so b=1 or b=-1.\n\nCase 1: b=1, d=1. Then equation 2: ac +1 +1 =1 => ac = -1. But since c=-a, then a*(-a) = -a^2 = -1 => a^2 =1 => a=1 or a=-1.\n\nIf a=1, then c=-1. Let's check equation 3: ad + bc =1*1 +1*(-1)=1 -1=0. Which works. Similarly, a=-1, c=1. Then ad + bc=(-1)*1 +1*1= -1 +1=0. Also works. So both possibilities for a. Therefore, the factorization is (n^2 +n +1)(n^2 -n +1). Let me check that:\n\n(n^2 +n +1)(n^2 -n +1) = n^4 -n^3 +n^2 +n^3 -n^2 +n +n^2 -n +1 = n^4 + ( -n^3 +n^3 ) + (n^2 -n^2 +n^2 ) + (n -n ) +1 = n^4 +n^2 +1. Yes, that works! Nice, so denominator factors into (n^2 +n +1)(n^2 -n +1).\n\nSo now, our term is n/( (n^2 +n +1)(n^2 -n +1) ). The idea is to express this as partial fractions. Let me set:\n\nn / [ (n^2 +n +1)(n^2 -n +1) ] = (An + B)/(n^2 +n +1) + (Cn + D)/(n^2 -n +1).\n\nMultiply both sides by (n^2 +n +1)(n^2 -n +1):\n\nn = (An + B)(n^2 -n +1) + (Cn + D)(n^2 +n +1)\n\nExpand both terms on the right:\n\nFirst term: An*(n^2 -n +1) + B*(n^2 -n +1) = An^3 -An^2 +An + Bn^2 - Bn + B\n\nSecond term: Cn*(n^2 +n +1) + D*(n^2 +n +1) = Cn^3 + Cn^2 + Cn + Dn^2 + Dn + D\n\nCombine like terms:\n\nn^3 terms: (A + C)n^3\n\nn^2 terms: (-A + B + C + D)n^2\n\nn terms: (A - B + C + D)n\n\nconstant term: B + D\n\nLeft side is n, which can be written as 0n^3 +0n^2 +1n +0.\n\nTherefore, set up equations by equating coefficients:\n\n1. Coefficient of n^3: A + C =0\n\n2. Coefficient of n^2: -A + B + C + D =0\n\n3. Coefficient of n: A - B + C + D =1\n\n4. Constant term: B + D =0\n\nFrom equation 1: C = -A.\n\nFrom equation 4: D = -B.\n\nSubstitute C = -A and D = -B into equations 2 and 3.\n\nEquation 2: -A + B + (-A) + (-B) =0\n\nSimplify: -A -A + B -B = -2A =0 => A=0.\n\nIf A=0, then C=0.\n\nThen equation 3: 0 - B + 0 + (-B) = -2B =1 => B= -1/2.\n\nThen D= -B = 1/2.\n\nSo the partial fractions decomposition is:\n\n(0*n + (-1/2))/(n^2 +n +1) + (0*n +1/2)/(n^2 -n +1) = (-1/2)/(n^2 +n +1) +1/2/(n^2 -n +1)\n\nSo our term simplifies to (1/2)[ 1/(n^2 -n +1) -1/(n^2 +n +1) ]\n\nTherefore, the sum becomes (1/2) sum_{n=0}^\\infty [1/(n^2 -n +1) -1/(n^2 +n +1)]\n\nHmm, this seems like a telescoping series? Maybe we can shift the index for one of the terms. Let's see.\n\nLet me write the sum as (1/2) [ sum_{n=0}^\\infty 1/(n^2 -n +1) - sum_{n=0}^\\infty 1/(n^2 +n +1) ]\n\nBut maybe shifting index in the first sum. Let me set m = n -1 in the first sum. But if n starts at 0, then m starts at -1, which is a bit tricky. Alternatively, maybe write the first sum as sum_{n=0}^\\infty 1/(n^2 -n +1) and notice that when n=0, the term is 1/(0 -0 +1)=1. When n=1, it's 1/(1 -1 +1)=1. For n=2, 1/(4 -2 +1)=1/3, etc. Hmm, but the second sum is sum_{n=0}^\\infty 1/(n^2 +n +1). Let's compute terms for the first few n:\n\nFirst sum (S1): n=0:1, n=1:1, n=2:1/3, n=3:1/7, n=4:1/13,...\n\nSecond sum (S2): n=0:1, n=1:1/(1+1+1)=1/3, n=2:1/(4+2+1)=1/7, n=3:1/13,...\n\nWait a second, S1 and S2 terms look similar but shifted. For S1, starting at n=0:1, 1, 1/3, 1/7, 1/13,...\n\nFor S2, starting at n=0:1, 1/3, 1/7, 1/13,...\n\nSo perhaps S1 is S2 shifted by 1. Let's check:\n\nIf we let m = n -1 in S1:\n\nS1 = sum_{n=0}^\\infty 1/(n^2 -n +1) = 1/(0 -0 +1) + sum_{n=1}^\\infty 1/(n^2 -n +1)\n\nLet m = n -1 for n >=1, so m starts at 0:\n\nS1 = 1 + sum_{m=0}^\\infty 1/( (m+1)^2 - (m+1) +1 ) = 1 + sum_{m=0}^\\infty 1/(m^2 +2m +1 -m -1 +1) = 1 + sum_{m=0}^\\infty 1/(m^2 + m +1 )\n\nTherefore, S1 = 1 + S2\n\nSo then S1 - S2 = 1 + S2 - S2 =1. But wait, this would imply that the difference S1 - S2 is 1, but our sum is (1/2)(S1 - S2) = 1/2. But this can't be right because when we sum from n=0 to infinity, S1 - S2 is 1, so the total sum is 1/2. But wait, let me check again.\n\nWait, S1 = 1 + sum_{m=0}^\\infty 1/(m^2 + m +1 ). But the second term here is S2. Therefore, S1 = 1 + S2. Therefore, S1 - S2 =1. Therefore, (1/2)(S1 - S2)=1/2.\n\nBut wait, but that would mean that the original sum converges to 1/2. But let's test with partial sums.\n\nTake the partial sums up to N:\n\nSum_{n=0}^N [1/(n^2 -n +1) -1/(n^2 +n +1) ]\n\nBut according to the telescoping idea, when we write this as sum_{n=0}^N [1/(n^2 -n +1) -1/(n^2 +n +1) ].\n\nNotice that for each term, 1/(n^2 -n +1) can be associated with the next term's 1/( (n+1)^2 - (n+1) +1 )\n\nWait, let's check for n and n+1:\n\nFor a term 1/(n^2 -n +1) in S1 and 1/(n^2 +n +1) in S2.\n\nBut when n is replaced by n+1 in S2, we get 1/((n+1)^2 + (n+1) +1) =1/(n^2 +3n +3). Hmm, not sure if that telescopes.\n\nAlternatively, perhaps if we index the first sum differently. Let me think:\n\nOriginal difference:\n\nSum_{n=0}^N [1/(n^2 -n +1) -1/(n^2 +n +1) ]\n\nLet me write this as Sum_{n=0}^N 1/(n^2 -n +1) - Sum_{n=0}^N 1/(n^2 +n +1)\n\nBut the first sum, as before, can be written as 1 + Sum_{n=1}^N 1/(n^2 -n +1 )\n\nChanging variable m =n -1 in the first sum:\n\nSum_{n=1}^N 1/(n^2 -n +1 ) = Sum_{m=0}^{N-1} 1/( (m+1)^2 - (m+1) +1 ) = Sum_{m=0}^{N-1} 1/(m^2 + 2m +1 -m -1 +1 ) = Sum_{m=0}^{N-1} 1/(m^2 +m +1 )\n\nTherefore, the first sum becomes 1 + Sum_{m=0}^{N-1} 1/(m^2 +m +1 )\n\nThe second sum is Sum_{n=0}^N 1/(n^2 +n +1 )\n\nSo the difference becomes:\n\n[1 + Sum_{m=0}^{N-1} 1/(m^2 +m +1 )] - [Sum_{n=0}^N 1/(n^2 +n +1 )]\n\n=1 + Sum_{m=0}^{N-1} 1/(m^2 +m +1 ) - Sum_{n=0}^N 1/(n^2 +n +1 )\n\n=1 - 1/(N^2 +N +1 )\n\nBecause the Sum_{m=0}^{N-1} is the same as Sum_{n=0}^{N-1} 1/(n^2 +n +1 ), so when subtracted from Sum_{n=0}^N, only the last term remains with a negative sign.\n\nTherefore, the difference S1 - S2 up to N is 1 - 1/(N^2 +N +1 ). Then taking the limit as N approaches infinity, the term 1/(N^2 +N +1 ) tends to 0, so the total difference is 1. Therefore, the original sum is (1/2)*1 = 1/2.\n\nWait, so the answer is 1/2? Let me verify with partial sums.\n\nCompute partial sums for small N.\n\nFor N=0:\n\nTerm is (1/(0^2 -0 +1) -1/(0^2 +0 +1 )) = (1 -1) =0. Sum=0.\n\nWait, but according to the previous, for N=0:\n\nDifference is 1 -1/(0^2 +0 +1 ) =1 -1=0. Hmm, maybe I made a mistake there. Wait no, for N=0:\n\nThe first sum S1 up to N=0 is 1/(0^2 -0 +1) =1.\n\nThe second sum S2 up to N=0 is 1/(0^2 +0 +1)=1. So the difference is 1 -1=0. Then (1/2)*0=0.\n\nFor N=1:\n\nSum_{n=0}^1 [1/(n^2 -n +1) -1/(n^2 +n +1) ] = [1/(0) +1/(1 -1 +1)] - [1/(0) +1/(1 +1 +1)] = [1 +1] - [1 +1/3] =2 -4/3=2/3. But according to the telescoping sum formula, the difference should be 1 -1/(1^2 +1 +1 )=1 -1/3=2/3. Then (1/2)*(2/3)=1/3. So sum up to N=1 would be 1/3. Wait, but actual calculation for the partial sum is (1/2)(2/3)=1/3? Wait, let's do step by step.\n\nOriginal sum is (1/2)[ (S1 - S2) ]. For N=1, S1 is sum_{n=0}^1 1/(n^2 -n +1 ) =1 +1/(1 -1 +1)=1+1=2.\n\nS2 is sum_{n=0}^1 1/(n^2 +n +1 )=1 +1/(1 +1 +1)=1 +1/3=4/3.\n\nSo difference S1 - S2=2 -4/3=2/3. Multiply by 1/2 gives the partial sum 1/3.\n\nBut if we take N=1, the original sum is sum_{n=0}^1 n/(n^4 +n^2 +1 ) =0/(0+0+1) +1/(1 +1 +1 )=0 +1/3=1/3. Which matches.\n\nSimilarly, for N=2:\n\nOriginal sum: 0/1 +1/3 +2/(16 +4 +1 )=1/3 +2/21=1/3 +2/21=7/21 +2/21=9/21=3/7.\n\nAccording to telescoping formula:\n\nDifference S1 - S2 up to N=2:1 -1/(2^2 +2 +1 )=1 -1/7=6/7. Multiply by 1/2:3/7. Which matches the actual sum.\n\nSo the telescoping works. Therefore, as N approaches infinity, 1/(N^2 +N +1 ) approaches zero, so the difference S1 - S2 approaches 1. Therefore, the original sum is (1/2)*1=1/2.\n\nTherefore, the answer is 1/2.\n\n**Final Answer**\n\\boxed{\\dfrac{1}{2}}\n</think>To solve the infinite series \\(\\sum_{n=0}^{\\infty} \\frac{n}{n^4 + n^2 + 1}\\), we start by factoring the denominator \\(n^4 + n^2 + 1\\). We note that:\n\n\\[\nn^4 + n^2 + 1 = (n^2 + n + 1)(n^2 - n + 1)\n\\]\n\nNext, we decompose the fraction using partial fractions:\n\n\\[\n\\frac{n}{(n^2 + n + 1)(n^2 - n + 1)} = \\frac{A}{n^2 + n + 1} + \\frac{B}{n^2 - n + 1}\n\\]\n\nMultiplying both sides by the denominator \\((n^2 + n + 1)(n^2 - n + 1)\\), we get:\n\n\\[\nn = A(n^2 - n + 1) + B(n^2 + n + 1)\n\\]\n\nExpanding and combining like terms, we have:\n\n\\[\nn = (A + B)n^2 + (-A + B)n + (A + B)\n\\]\n\nEquating coefficients, we get the system of equations:\n\n1. \\(A + B = 0\\)\n2. \\(-A + B = 1\\)\n3. \\(A + B = 0\\)\n\nFrom the first and third equations, we have \\(A + B = 0\\), so \\(B = -A\\). Substituting \\(B = -A\\) into the second equation:\n\n\\[\n-A + (-A) = 1 \\implies -2A = 1 \\implies A = -\\frac{1}{2}, \\quad B = \\frac{1}{2}\n\\]\n\nThus, the partial fraction decomposition is:\n\n\\[\n\\frac{n}{(n^2 + n + 1)(n^2 - n + 1)} = \\frac{-\\frac{1}{2}}{n^2 + n + 1} + \\frac{\\frac{1}{2}}{n^2 - n + 1} = \\frac{1}{2} \\left( \\frac{1}{n^2 - n + 1} - \\frac{1}{n^2 + n + 1} \\right)\n\\]\n\nRewriting the series, we have:\n\n\\[\n\\sum_{n=0}^{\\infty} \\frac{n}{n^4 + n^2 + 1} = \\frac{1}{2} \\sum_{n=0}^{\\infty} \\left( \\frac{1}{n^2 - n + 1} - \\frac{1}{n^2 + n + 1} \\right)\n\\]\n\nWe observe that this series telescopes. To see this, consider the partial sum up to \\(N\\):\n\n\\[\n\\sum_{n=0}^{N} \\left( \\frac{1}{n^2 - n + 1} - \\frac{1}{n^2 + n + 1} \\right)\n\\]\n\nThe terms simplify as follows:\n\n\\[\n\\left( \\frac{1}{0^2 - 0 + 1} - \\frac{1}{0^2 + 0 + 1} \\right) + \\left( \\frac{1}{1^2 - 1 + 1} - \\frac{1}{1^2 + 1 + 1} \\right) + \\cdots + \\left( \\frac{1}{N^2 - N + 1} - \\frac{1}{N^2 + N + 1} \\right)\n\\]\n\nThis telescopes to:\n\n\\[\n1 - \\frac{1}{N^2 + N + 1}\n\\]\n\nTaking the limit as \\(N \\to \\infty\\):\n\n\\[\n\\lim_{N \\to \\infty} \\left( 1 - \\frac{1}{N^2 + N + 1} \\right) = 1\n\\]\n\nThus, the original series converges to:\n\n\\[\n\\frac{1}{2} \\times 1 = \\frac{1}{2}\n\\]\n\nThe final answer is:\n\n\\[\n\\boxed{\\frac{1}{2}}\n\\]"}