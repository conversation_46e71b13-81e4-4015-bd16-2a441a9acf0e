{"row_number": 6280, "expected_answer": "3", "problem": "Find the smallest integer \\( n \\) such that \\( \\left(x^2 + y^2 + z^2\\right)^2 \\leq n\\left(x^4 + y^4 + z^4\\right) \\) for all real numbers \\( x \\), \\( y \\), and \\( z \\).", "generated_solution": "<think>\nOkay, let's see. I need to find the smallest integer n such that (x² + y² + z²)² ≤ n(x⁴ + y⁴ + z⁴) for all real numbers x, y, z. Hmm, interesting problem. So, essentially, I have to determine the minimal n where this inequality holds true no matter what values x, y, and z take. \n\nFirst, maybe I should expand the left-hand side and see how it compares to the right-hand side. Let me do that. Expanding (x² + y² + z²)² gives x⁴ + y⁴ + z⁴ + 2x²y² + 2x²z² + 2y²z². So the inequality becomes:\n\nx⁴ + y⁴ + z⁴ + 2x²y² + 2x²z² + 2y²z² ≤ n(x⁴ + y⁴ + z⁴)\n\nIf I subtract the left-hand side from both sides, we get:\n\n(n - 1)(x⁴ + y⁴ + z⁴) - 2x²y² - 2x²z² - 2y²z² ≥ 0\n\nSo, the inequality can be written as:\n\n(n - 1)(x⁴ + y⁴ + z⁴) ≥ 2(x²y² + x²z² + y²z²)\n\nI need this to hold for all real x, y, z. Therefore, n must be chosen such that this inequality is always true. Maybe I can find the maximum value of the right-hand side divided by the left-hand side, and set n so that it's always less than or equal to 1? Wait, not exactly. Let me think.\n\nAlternatively, perhaps I can consider this as an optimization problem. Let me set up the ratio:\n\n[(x² + y² + z²)²] / [x⁴ + y⁴ + z⁴] ≤ n\n\nSo, the maximum value of this ratio over all real x, y, z will be the minimal n. Therefore, I need to maximize the numerator (x² + y² + z²)² while the denominator is fixed, or perhaps normalize variables to make it easier. Maybe I can assume that x⁴ + y⁴ + z⁴ = 1, and then maximize (x² + y² + z²)². Then, the maximum value would be the minimal n.\n\nAlternatively, since the inequality is homogeneous, we can normalize variables. Let me check that. Suppose we set t = 1, and scale x, y, z such that x⁴ + y⁴ + z⁴ = 1. Then, we need to find the maximum of (x² + y² + z²)² under this constraint. Then, n would be equal to that maximum.\n\nAlternatively, maybe use Lagrange multipliers. Let's consider f(x, y, z) = (x² + y² + z²)² and the constraint g(x, y, z) = x⁴ + y⁴ + z⁴ = 1. Then, set up the Lagrangian:\n\nL = (x² + y² + z²)² - λ(x⁴ + y⁴ + z⁴ - 1)\n\nTake partial derivatives with respect to x, y, z, and set them equal to zero.\n\nLet's compute ∂L/∂x:\n\nFirst, derivative of (x² + y² + z²)² with respect to x is 2(x² + y² + z²)(2x) = 4x(x² + y² + z²)\n\nDerivative of the constraint term is -λ(4x³)\n\nSo, setting ∂L/∂x = 0:\n\n4x(x² + y² + z²) - 4λx³ = 0\n\nSimilarly, for y and z:\n\n4y(x² + y² + z²) - 4λy³ = 0\n\n4z(x² + y² + z²) - 4λz³ = 0\n\nSimplify each equation by dividing by 4 (and note if variables are zero):\n\nFor x ≠ 0: x² + y² + z² = λx²\n\nSimilarly for y ≠ 0: x² + y² + z² = λy²\n\nAnd for z ≠ 0: x² + y² + z² = λz²\n\nSo, if x, y, z are all non-zero, then we have:\n\nλx² = λy² = λz² = x² + y² + z²\n\nWhich implies that x² = y² = z². Because if λ is non-zero, then x² = y² = z². Let’s denote x² = y² = z² = k. Then, x² + y² + z² = 3k, and the constraint x⁴ + y⁴ + z⁴ = 1 becomes 3k² = 1 ⇒ k = 1/√3. Then, x² + y² + z² = 3*(1/√3) = √3. Then, (x² + y² + z²)^2 = (√3)^2 = 3. So, in this case, the ratio is 3 / 1 = 3. So n would need to be at least 3 here.\n\nBut wait, is this the maximum? What if one of the variables is zero? Let's check other cases.\n\nCase 1: Two variables are zero. Say y = z = 0. Then, the left-hand side becomes (x²)^2 = x⁴, the right-hand side becomes n x⁴. So the inequality is x⁴ ≤ n x⁴ ⇒ 1 ≤ n. So n must be at least 1. But since in the previous case, we needed n ≥ 3, which is higher, so n would need to be at least 3. But wait, perhaps other cases?\n\nCase 2: One variable is zero. Let’s say z = 0, and x and y non-zero. Then, the inequality becomes (x² + y²)^2 ≤ n(x⁴ + y⁴). Let’s find the maximum of (x² + y²)^2 / (x⁴ + y⁴). Let’s set t = x², s = y². Then, (t + s)^2 / (t² + s²). Let’s let u = t/s, so u > 0. Then, the expression becomes (u + 1)^2 / (u² + 1). Let’s compute this: (u² + 2u + 1) / (u² + 1) = 1 + (2u)/(u² + 1). To maximize this, we can take derivative with respect to u:\n\nd/du [1 + 2u/(u² + 1)] = 2*(u² + 1) - 2u*(2u) / (u² + 1)^2 = [2u² + 2 - 4u²]/(u² + 1)^2 = (-2u² + 2)/(u² + 1)^2.\n\nSet derivative to zero: -2u² + 2 = 0 ⇒ u² = 1 ⇒ u = 1. So maximum at u = 1, which gives (1 + 1)^2 / (1 + 1) = 4 / 2 = 2. So maximum ratio is 2. Therefore, in this case, n needs to be at least 2. But since in the case where all variables are non-zero, the ratio was 3, which is higher. So 3 is larger, so n needs to be at least 3. \n\nBut wait, let's check this. If two variables are equal and the third is zero. For example, let’s take x = y and z = 0. Then, (2x²)^2 ≤ n(2x⁴). So 4x⁴ ≤ 2n x⁴ ⇒ 4 ≤ 2n ⇒ n ≥ 2. So again, maximum here is 2. But the three-variable case gives a higher ratio. Therefore, the maximum occurs when all three variables are equal. \n\nBut let me verify with specific numbers. Let’s take x = y = z = 1. Then, (1 + 1 + 1)^2 = 9. The denominator is 1 + 1 + 1 = 3. So 9 / 3 = 3. So the ratio is 3 here. If I take x = y = 1, z = 0: (1 + 1 + 0)^2 = 4, denominator 1 + 1 + 0 = 2, so 4 / 2 = 2. If x = 1, y = z = 0: ratio is 1 / 1 = 1. So the maximum ratio is indeed 3, achieved when all variables are equal. Therefore, n must be at least 3. But is 3 sufficient? Let's check.\n\nSuppose n = 3. Then, the inequality is (x² + y² + z²)^2 ≤ 3(x⁴ + y⁴ + z⁴). Let’s check for arbitrary x, y, z.\n\nAlternatively, perhaps this is a known inequality. Wait, the Cauchy-Schwarz inequality? Hmm. Let me think. Maybe not directly. Alternatively, maybe using the Power Mean inequality. Let's recall that for non-negative real numbers, the power mean inequality states that the square of the quadratic mean is less than or equal to the quartic mean. Wait, but quadratic mean squared would be (x² + y² + z²)/3, squared: (x² + y² + z²)^2 / 9. The quartic mean would be ((x⁴ + y⁴ + z⁴)/3)^(1/4). Hmm, not sure if that's directly applicable.\n\nAlternatively, maybe use the Cauchy-Schwarz inequality in some form. The left-hand side is (x² + y² + z²)^2. The right-hand side is 3(x⁴ + y⁴ + z⁴). Let's see if (x² + y² + z²)^2 ≤ 3(x⁴ + y⁴ + z⁴). Let's rearrange terms:\n\n3(x⁴ + y⁴ + z⁴) - (x² + y² + z²)^2 ≥ 0.\n\nExpanding the left-hand side as 3(x⁴ + y⁴ + z⁴) - (x⁴ + y⁴ + z⁴ + 2x²y² + 2x²z² + 2y²z²) = 2(x⁴ + y⁴ + z⁴) - 2x²y² - 2x²z² - 2y²z² = 2[(x⁴ + y⁴ + z⁴) - x²y² - x²z² - y²z²]\n\nSo we need to show that (x⁴ + y⁴ + z⁴) - x²y² - x²z² - y²z² ≥ 0. Let me check if this is true. Hmm, this expression is equal to ½[(x² - y²)^2 + (x² - z²)^2 + (y² - z²)^2]. Let me verify that. \n\nCompute (x² - y²)^2 + (x² - z²)^2 + (y² - z²)^2:\n\n= (x⁴ - 2x²y² + y⁴) + (x⁴ - 2x²z² + z⁴) + (y⁴ - 2y²z² + z⁴)\n\n= 2x⁴ + 2y⁴ + 2z⁴ - 2x²y² - 2x²z² - 2y²z²\n\nTherefore, ½ of that is x⁴ + y⁴ + z⁴ - x²y² - x²z² - y²z². So indeed, we have:\n\n(x⁴ + y⁴ + z⁴) - x²y² - x²z² - y²z² = ½[(x² - y²)^2 + (x² - z²)^2 + (y² - z²)^2] ≥ 0.\n\nSince squares are non-negative, this is always non-negative. Therefore, 3(x⁴ + y⁴ + z⁴) - (x² + y² + z²)^2 = 2*(non-negative) ≥ 0. Hence, the inequality holds with n = 3. So, since we found that when x = y = z, the ratio is 3, which is the maximum, and n = 3 satisfies the inequality for all real numbers. Therefore, the smallest integer n is 3.\n\nWait, just to be thorough, let me test another case where variables are different. Suppose x = 2, y = 1, z = 1. Then:\n\nLeft-hand side: (4 + 1 + 1)^2 = 36\n\nRight-hand side: 3*(16 + 1 + 1) = 3*18 = 54\n\n36 ≤ 54, which holds. How about x = 2, y = 1, z = 0:\n\nLeft-hand side: (4 + 1 + 0)^2 = 25\n\nRight-hand side: 3*(16 + 1 + 0) = 3*17 = 51\n\n25 ≤ 51, which holds. If n were 2, let's see. Take x = y = z = 1. Then left-hand side is 9, right-hand side would be 2*(3) = 6. But 9 ≤ 6 is false. So n must be at least 3, and 3 works. Therefore, the answer is 3.\n\n**Final Answer**\nThe smallest integer \\( n \\) is \\boxed{3}.\n</think>To find the smallest integer \\( n \\) such that \\( \\left(x^2 + y^2 + z^2\\right)^2 \\leq n\\left(x^4 + y^4 + z^4\\right) \\) for all real numbers \\( x \\), \\( y \\), and \\( z \\), we start by expanding the left-hand side:\n\n\\[\n\\left(x^2 + y^2 + z^2\\right)^2 = x^4 + y^4 + z^4 + 2x^2y^2 + 2x^2z^2 + 2y^2z^2\n\\]\n\nThe inequality then becomes:\n\n\\[\nx^4 + y^4 + z^4 + 2x^2y^2 + 2x^2z^2 + 2y^2z^2 \\leq n(x^4 + y^4 + z^4)\n\\]\n\nRearranging terms, we get:\n\n\\[\n(n - 1)(x^4 + y^4 + z^4) \\geq 2(x^2y^2 + x^2z^2 + y^2z^2)\n\\]\n\nWe need to find the smallest \\( n \\) such that this inequality holds for all real \\( x, y, z \\). To do this, we consider the ratio:\n\n\\[\n\\frac{(x^2 + y^2 + z^2)^2}{x^4 + y^4 + z^4}\n\\]\n\nTo find the maximum value of this ratio, we normalize the variables by setting \\( x^4 + y^4 + z^4 = 1 \\). We need to maximize \\( (x^2 + y^2 + z^2)^2 \\) under this constraint. \n\nConsider the case where \\( x = y = z \\). If \\( x = y = z \\), then:\n\n\\[\nx^4 + y^4 + z^4 = 3x^4 = 1 \\implies x^4 = \\frac{1}{3} \\implies x^2 = \\frac{1}{\\sqrt{3}}\n\\]\n\nThus,\n\n\\[\nx^2 + y^2 + z^2 = 3 \\cdot \\frac{1}{\\sqrt{3}} = \\sqrt{3}\n\\]\n\nSo,\n\n\\[\n(x^2 + y^2 + z^2)^2 = (\\sqrt{3})^2 = 3\n\\]\n\nTherefore, the ratio is:\n\n\\[\n\\frac{(x^2 + y^2 + z^2)^2}{x^4 + y^4 + z^4} = \\frac{3}{1} = 3\n\\]\n\nWe now need to check if \\( n = 3 \\) is sufficient. Substituting \\( n = 3 \\) into the inequality, we get:\n\n\\[\n3(x^4 + y^4 + z^4) - (x^2 + y^2 + z^2)^2 \\geq 0\n\\]\n\nExpanding and simplifying:\n\n\\[\n3(x^4 + y^4 + z^4) - (x^4 + y^4 + z^4 + 2x^2y^2 + 2x^2z^2 + 2y^2z^2) = 2(x^4 + y^4 + z^4) - 2(x^2y^2 + x^2z^2 + y^2z^2)\n\\]\n\nWe need to show:\n\n\\[\n(x^4 + y^4 + z^4) - (x^2y^2 + x^2z^2 + y^2z^2) \\geq 0\n\\]\n\nThis can be rewritten as:\n\n\\[\n\\frac{1}{2} \\left( (x^2 - y^2)^2 + (x^2 - z^2)^2 + (y^2 - z^2)^2 \\right) \\geq 0\n\\]\n\nSince squares are non-negative, this inequality always holds. Therefore, the smallest integer \\( n \\) such that the inequality holds for all real \\( x, y, z \\) is:\n\n\\[\n\\boxed{3}\n\\]"}