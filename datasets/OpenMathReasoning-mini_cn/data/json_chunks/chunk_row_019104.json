{"row_number": 19104, "expected_answer": "0", "problem": "Compute the limit \\(\\lim_{n \\to +\\infty} I_n\\) where \\(I_n = \\int_0^1 \\frac{e^{-nx}}{1 + e^{-x}} \\, dx\\).", "generated_solution": "<think>\nOkay, so I need to find the limit as n approaches infinity of the integral from 0 to 1 of (e^(-n x))/(1 + e^(-x)) dx. Hmm, let me think about how to approach this.\n\nFirst, the integral I_n is from 0 to 1, and the integrand is e^(-n x)/(1 + e^(-x)). As n becomes very large, the exponential term e^(-n x) might dominate the behavior of the integrand. Let me check what happens to e^(-n x) as n increases for different values of x in [0,1].\n\nWhen x is 0, e^(-n*0) = e^0 = 1, regardless of n. But when x is positive, even a little bit like 0.1, as n increases, e^(-0.1n) will go to zero very quickly. So except for x=0, the numerator is going to zero as n approaches infinity. However, the point x=0 is just a single point in the interval [0,1], and in integration, single points don't contribute to the integral. But wait, maybe there's something else here.\n\nAlternatively, maybe the integrand is sharply peaked near x=0 when n is large. So perhaps the main contribution to the integral comes from a small neighborhood around x=0. This might be a case where we can use the method of Laplace for integrals with exponential decay. <PERSON><PERSON>'s method is used to approximate integrals of the form ∫ e^{-n φ(x)} g(x) dx, where φ(x) has a minimum at some point, and as n becomes large, the integral is dominated by the neighborhood around that minimum.\n\nIn this case, φ(x) would be x, since we have e^{-n x}. The function φ(x) = x has its minimum at x=0 on the interval [0,1]. So yes, the main contribution should come from near x=0. So maybe we can approximate the integral by expanding the integrand around x=0.\n\nLet me try that. Let's write the integrand as e^{-n x}/(1 + e^{-x}). Let's expand 1/(1 + e^{-x}) around x=0. The denominator is 1 + e^{-x} ≈ 1 + (1 - x + x²/2 - ...) for small x. Wait, e^{-x} ≈ 1 - x + x²/2 - x³/6 + ... So 1 + e^{-x} ≈ 2 - x + x²/2 - x³/6 + ...\n\nBut maybe instead of expanding the denominator, it's better to consider a substitution for small x. Let me see.\n\nAlternatively, for x near 0, e^{-x} ≈ 1 - x, so 1 + e^{-x} ≈ 2 - x. But maybe this isn't the most efficient way. Alternatively, note that as x approaches 0, the denominator approaches 1 + e^0 = 2. So perhaps near x=0, the denominator is approximately 2. So maybe we can approximate the integrand as e^{-n x}/2 near x=0, and then integrate this approximation over [0,1]. However, integrating e^{-n x}/2 from 0 to 1 would give (1/(2n))(1 - e^{-n}). As n approaches infinity, e^{-n} goes to zero, so the integral would approach 1/(2n), which goes to zero. But wait, the original integral is e^{-n x}/(1 + e^{-x}) dx. But if the integrand is approximated by e^{-n x}/2 near zero, then the integral would be roughly 1/(2n), but as n approaches infinity, the integral approaches zero. However, maybe this isn't capturing the whole story because even though the denominator is near 2 at x=0, as x increases a bit, the denominator changes. Wait, but when n is large, the e^{-n x} term is only significant near x=0, so maybe the integral is dominated by the region very close to x=0, so that even if the denominator is varying, we can approximate it as a constant.\n\nAlternatively, maybe make a substitution to zoom in near x=0. Let me try substitution. Let t = n x. Then as n approaches infinity, t ranges from 0 to n, which for the integral's purposes, as n becomes large, the upper limit can be approximated as infinity.\n\nLet's try that substitution. Let t = n x, so x = t/n, dx = dt/n. Then the integral becomes:\n\nI_n = ∫_{t=0}^{t=n} e^{-t}/(1 + e^{-t/n}) * (dt/n) = (1/n) ∫_{0}^{n} e^{-t} / (1 + e^{-t/n}) dt.\n\nNow, as n approaches infinity, t/n approaches 0 for any fixed t. So e^{-t/n} ≈ 1 - t/n + (t^2)/(2 n²) - ... So the denominator 1 + e^{-t/n} ≈ 1 + 1 - t/n + ... = 2 - t/n + ... So approximately, 1/(1 + e^{-t/n}) ≈ 1/(2 - t/n) ≈ (1/2)(1 + t/(2n) + ... ) using the expansion 1/(2 - a) ≈ 1/(2) (1 + a/2 + (a/2)^2 + ...) for small a.\n\nBut since t/n is small as n becomes large, maybe we can approximate 1/(1 + e^{-t/n}) ≈ 1/2 * [1 + t/(2n)]^{-1}? Wait, maybe a better approach is to write:\n\n1/(1 + e^{-t/n}) = 1/(2 - t/n + (t/n)^2/2 - ... ) ≈ 1/2 * [1 + t/(2n) + ... ]\n\nBut perhaps even more simply, since t/n approaches 0, e^{-t/n} ≈ 1 - t/n, so 1 + e^{-t/n} ≈ 2 - t/n. So 1/(1 + e^{-t/n}) ≈ 1/(2 - t/n) ≈ 1/(2) [1 + t/(2n) + (t/(2n))^2 + ... ].\n\nBut maybe we can just approximate 1/(1 + e^{-t/n}) ≈ 1/2, since t/n is very small as n approaches infinity. So then the integrand becomes approximately (1/n) * e^{-t} / 2. Then, extending the upper limit to infinity, since for fixed t as n approaches infinity, the upper limit n approaches infinity, but e^{-t} decays rapidly. So:\n\nI_n ≈ (1/(2n)) ∫_{0}^{\\infty} e^{-t} dt = (1/(2n)) * 1 = 1/(2n), which tends to 0 as n approaches infinity. But this contradicts our initial thought that the integral might approach zero, but perhaps there's a better approximation. However, if we use the first-order term in the expansion of the denominator, maybe we get a different result.\n\nAlternatively, maybe the leading term is 1/(2) and the next term is a correction. So let's expand 1/(1 + e^{-t/n}) as follows:\n\nLet’s write 1 + e^{-t/n} = 2 - (t/n) + (t^2)/(2n²) - ... \n\nTherefore, 1/(1 + e^{-t/n}) = 1/[2 (1 - (t)/(2n) + (t²)/(4n²) - ...)] ≈ (1/2)[1 + (t)/(2n) + (t²)/(4n²) + ... ] using the expansion 1/(1 - ε) ≈ 1 + ε + ε² + ... for small ε.\n\nTherefore, substituting back into the integral:\n\nI_n ≈ (1/n) ∫_{0}^{n} e^{-t} * (1/2)[1 + t/(2n) + ... ] dt = (1/(2n)) ∫_{0}^{n} e^{-t} [1 + t/(2n) + ... ] dt\n\nNow, compute the integral term by term:\n\nFirst term: ∫_{0}^{n} e^{-t} dt = 1 - e^{-n} ≈ 1 for large n.\n\nSecond term: ∫_{0}^{n} e^{-t} t/(2n) dt = (1/(2n)) ∫_{0}^{n} t e^{-t} dt.\n\nBut ∫_{0}^{n} t e^{-t} dt approaches 1 as n approaches infinity because ∫_{0}^{\\infty} t e^{-t} dt = 1! (Gamma function Γ(2) = 1! = 1). So as n approaches infinity, this term becomes (1/(2n)) * 1.\n\nSo putting it all together:\n\nI_n ≈ (1/(2n)) [1 + 1/(2n) + ... ] ≈ 1/(2n) + 1/(4n²) + ... \n\nThus, as n approaches infinity, I_n ~ 1/(2n) → 0.\n\nBut wait, the problem states to compute the limit as n approaches infinity of I_n. If the leading term is 1/(2n), then the limit would be zero. But maybe this is correct. However, perhaps my approximation is missing something.\n\nAlternatively, maybe I should consider a different substitution or method. Let's think again.\n\nAlternatively, note that for each x in (0,1], e^{-n x} → 0 as n → ∞. So pointwise, the integrand tends to zero except at x=0. However, the integral's behavior depends on how the integrand behaves near x=0. So maybe we can use the Dominated Convergence Theorem. The Dominated Convergence Theorem says that if |f_n(x)| ≤ g(x) for some integrable g(x) and f_n(x) → f(x) pointwise a.e., then the integral of f_n converges to the integral of f.\n\nBut here, f_n(x) = e^{-n x}/(1 + e^{-x}) ≤ e^{-n x}, since 1 + e^{-x} ≥ 1. So |f_n(x)| ≤ e^{-n x}, and ∫_0^1 e^{-n x} dx = (1 - e^{-n})/n → 0 as n → ∞. Therefore, by the Dominated Convergence Theorem, the limit of I_n is zero. But wait, does that hold?\n\nWait, the Dominated Convergence Theorem requires that f_n(x) converges pointwise a.e. to f(x), and |f_n(x)| ≤ g(x) where g is integrable. Here, f_n(x) → 0 for each x in (0,1], and at x=0, f_n(0) = 1/(1 + 1) = 1/2. So except at x=0, the pointwise limit is zero. But since the set {0} has measure zero, the integral of f_n would converge to zero. Therefore, by the Dominated Convergence Theorem, the limit should be zero.\n\nBut wait, let me check that. If the pointwise limit is zero almost everywhere, and the dominated function is e^{-n x}, but wait, e^{-n x} is not a dominating function independent of n. The Dominated Convergence Theorem requires that |f_n(x)| ≤ g(x) for some integrable g(x) independent of n. Here, if we take g(x) = 1/(1 + e^{-x}), since 1 + e^{-x} ≥ 1, so 1/(1 + e^{-x}) ≤ 1. Therefore, |f_n(x)| = e^{-n x}/(1 + e^{-x}) ≤ e^{-n x}. But e^{-n x} is not dominated by an integrable function independent of n. For example, for each fixed x > 0, e^{-n x} approaches zero, but for x near zero, e^{-n x} can be close to 1. However, if we take g(x) = 1 (since 1/(1 + e^{-x}) ≤ 1 and e^{-n x} ≤ 1), but 1 is integrable on [0,1]. Wait, that's correct. Since e^{-n x}/(1 + e^{-x}) ≤ 1/(1 + e^{-x}) ≤ 1, and 1 is integrable over [0,1]. Therefore, by the Dominated Convergence Theorem, since f_n(x) → 0 a.e., the integral I_n should approach 0.\n\nBut wait, at x=0, f_n(0) = 1/(1 + 1) = 1/2, which is a single point, so measure zero. So yes, the pointwise limit is 0 a.e., and dominated by 1, which is integrable. Hence, by the DCT, the limit is 0.\n\nAlternatively, maybe the problem is more nuanced. Let me consider the integral again.\n\nAlternatively, perhaps integrate by substitution. Let me try to write the integral as ∫0^1 e^{-n x}/(1 + e^{-x}) dx.\n\nAlternatively, write the integrand as e^{-n x} * [1 - e^{-x}/(1 + e^{-x})]? Wait, not sure. Maybe a better substitution is to set t = e^{-x}, then dt = -e^{-x} dx, so dx = -dt/t. When x=0, t=1, and x=1, t=e^{-1}. So the integral becomes:\n\nI_n = ∫_{t=1}^{t=e^{-1}} t^n / (1 + t) * (-dt/t) = ∫_{t=e^{-1}}^{1} t^{n-1}/(1 + t) dt = ∫_{e^{-1}}^{1} t^{n-1}/(1 + t) dt.\n\nHmm, so now the integral is from e^{-1} to 1 of t^{n-1}/(1 + t) dt. As n approaches infinity, t^{n-1} goes to zero for t in [e^{-1}, 1) since t < 1. At t=1, t^{n-1} = 1. But again, the point t=1 has measure zero. So again, the integral would approach zero by the DCT, since t^{n-1}/(1 + t) is bounded by 1/(1 + t) which is integrable over [e^{-1}, 1]. So yes, the limit is zero.\n\nAlternatively, maybe split the integral into two parts: near t=1 and away from t=1. Let me see. For any ε > 0, split the integral into [1 - ε, 1] and [e^{-1}, 1 - ε]. On [e^{-1}, 1 - ε], t^{n-1} ≤ (1 - ε)^{n-1} which tends to zero as n increases. So the integral over [e^{-1}, 1 - ε] tends to zero. The integral over [1 - ε, 1] can be approximated. Let's make substitution s = 1 - t, so s goes from 0 to ε. Then t = 1 - s, dt = -ds, so the integral becomes ∫_{0}^{ε} (1 - s)^{n-1}/(1 + 1 - s) ds = ∫_{0}^{ε} (1 - s)^{n-1}/(2 - s) ds.\n\nFor small s, (1 - s)^{n-1} ≈ e^{-(n-1)s} and 2 - s ≈ 2. So the integral is approximately ∫0^ε e^{-(n-1)s}/2 ds = (1/2) * [1 - e^{-(n-1)ε}]/(n - 1). As n approaches infinity, this is approximately (1/(2(n - 1))) → 0. Therefore, both integrals tend to zero, so the overall integral I_n tends to zero.\n\nHence, regardless of the approach—whether substitution, DCT, or splitting the integral—the limit seems to be zero.\n\nWait, but let me check once more. Suppose I use the substitution t = n x, as before. Then, as n→infty, x = t/n, and the integral becomes (1/n) ∫0^n e^{-t}/(1 + e^{-t/n}) dt. For fixed t, as n→infty, e^{-t/n} ≈ 1 - t/n, so the denominator is approximately 2 - t/n. Thus, 1/(1 + e^{-t/n}) ≈ 1/(2 - t/n) ≈ 1/2 (1 + t/(2n) + ...). Then the integrand is approximately e^{-t}/2 (1 + t/(2n)) and the integral becomes (1/(2n)) ∫0^n e^{-t}(1 + t/(2n)) dt. The integral ∫0^n e^{-t} dt ≈ 1 and ∫0^n t e^{-t} dt ≈ 1 as n becomes large. So the integral is approximately (1/(2n))(1 + 1/(2n)) ≈ 1/(2n) + 1/(4n²), which again tends to zero. So again, the limit is zero.\n\nTherefore, all approaches point to the conclusion that the limit is zero. So I think the answer is zero.\n</think>To compute the limit \\(\\lim_{n \\to +\\infty} I_n\\) where \\(I_n = \\int_0^1 \\frac{e^{-nx}}{1 + e^{-x}} \\, dx\\), we will use the Dominated Convergence Theorem and a substitution.\n\n### Step 1: Pointwise Convergence\nFor each \\(x \\in (0, 1]\\), as \\(n \\to \\infty\\), \\(e^{-nx} \\to 0\\). Therefore, the integrand \\(\\frac{e^{-nx}}{1 + e^{-x}} \\to 0\\) pointwise for \\(x \\in (0, 1]\\). At \\(x = 0\\), the integrand is \\(\\frac{1}{2}\\), but this is a single point and thus has measure zero.\n\n### Step 2: Dominating Function\nThe integrand \\(\\frac{e^{-nx}}{1 + e^{-x}}\\) is bounded by \\(\\frac{1}{1 + e^{-x}}\\). Since \\(1 + e^{-x} \\geq 1\\), we have \\(\\frac{1}{1 + e^{-x}} \\leq 1\\). The function \\(1\\) is integrable on \\([0, 1]\\).\n\n### Step 3: Applying the Dominated Convergence Theorem\nBy the Dominated Convergence Theorem, since the integrand converges pointwise to \\(0\\) almost everywhere and is dominated by the integrable function \\(1\\), we have:\n\\[\n\\lim_{n \\to \\infty} \\int_0^1 \\frac{e^{-nx}}{1 + e^{-x}} \\, dx = \\int_0^1 \\lim_{n \\to \\infty} \\frac{e^{-nx}}{1 + e^{-x}} \\, dx = \\int_0^1 0 \\, dx = 0.\n\\]\n\n### Step 4: Substitution Verification\nTo further confirm, consider the substitution \\(t = nx\\), so \\(x = \\frac{t}{n}\\) and \\(dx = \\frac{dt}{n}\\). The integral becomes:\n\\[\nI_n = \\int_0^1 \\frac{e^{-nx}}{1 + e^{-x}} \\, dx = \\int_0^n \\frac{e^{-t}}{1 + e^{-t/n}} \\cdot \\frac{dt}{n}.\n\\]\nAs \\(n \\to \\infty\\), \\(e^{-t/n} \\to 1\\) for fixed \\(t\\), so:\n\\[\n\\frac{1}{1 + e^{-t/n}} \\to \\frac{1}{2}.\n\\]\nThus, the integrand approximates:\n\\[\n\\frac{e^{-t}}{2n}.\n\\]\nThe integral then becomes:\n\\[\nI_n \\approx \\frac{1}{2n} \\int_0^n e^{-t} \\, dt.\n\\]\nThe integral \\(\\int_0^n e^{-t} \\, dt\\) approaches \\(1\\) as \\(n \\to \\infty\\), so:\n\\[\nI_n \\approx \\frac{1}{2n} \\cdot 1 = \\frac{1}{2n} \\to 0 \\text{ as } n \\to \\infty.\n\\]\n\n### Conclusion\nBoth the Dominated Convergence Theorem and the substitution method confirm that the limit is:\n\\[\n\\boxed{0}.\n\\]"}