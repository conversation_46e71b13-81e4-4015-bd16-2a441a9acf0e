{"row_number": 6173, "expected_answer": "\\(1 - \\frac{1}{2021!}\\)", "problem": "Evaluate the sum $\\frac{1}{2!} + \\frac{2}{3!} + \\frac{3}{4!} + \\cdots + \\frac{2020}{2021!}$.", "generated_solution": "<think>\nOkay, so I need to find the sum of the series 1/(2!) + 2/(3!) + 3/(4!) + ... + 2020/(2021!). Hmm, let me think about how to approach this. I remember that factorials grow really fast, so maybe there's a pattern or a telescoping series here. \n\nFirst, let me write out the general term. The nth term of the series seems to be n divided by (n+1)!, right? Because when n=1, it's 1/2!, n=2 is 2/3!, etc., up to n=2020 which is 2020/2021!. So the general term is n/(n+1)!.\n\nNow, I need to sum this from n=1 to n=2020. Telescoping series usually involve terms that cancel out when you add them. Maybe I can express n/(n+1)! as a difference between two terms. Let me think... \n\nI recall that 1/n! - 1/(n+1)! might be related. Let me compute that difference. \n\n1/n! - 1/(n+1)! = [ (n+1) - 1 ] / (n+1)! = n / (n+1)!.\n\nWait, that's exactly the general term! So n/(n+1)! = 1/n! - 1/(n+1)!.\n\nOh, so each term in the series is actually the difference between consecutive reciprocals of factorials. That means when we add them up, most terms should cancel out. Let me verify this with the first few terms.\n\nFor n=1: 1/2! = 1/1! - 1/2! = 1 - 1/2\nFor n=2: 2/3! = 1/2! - 1/3! = 1/2 - 1/6\nFor n=3: 3/4! = 1/3! - 1/4! = 1/6 - 1/24\n...\nSo the sum becomes (1 - 1/2) + (1/2 - 1/6) + (1/6 - 1/24) + ... + (1/2020! - 1/2021!).\n\nWhen we add these up, the -1/2 cancels with the +1/2, the -1/6 cancels with the +1/6, and so on. All the intermediate terms should cancel, leaving us with the first term of the first expression and the last term of the last expression. \n\nSo the total sum is 1 - 1/2021!.\n\nWait, let me check for a smaller case to be sure. Let's take the sum up to n=2: 1/2! + 2/3! = 1/2 + 2/6 = 1/2 + 1/3 = 5/6. Using the formula, it should be 1 - 1/3! = 1 - 1/6 = 5/6. That matches. What about n=3? The sum would be 1/2 + 2/6 + 3/24 = 1/2 + 1/3 + 1/8. Let's compute that: 12/24 + 8/24 + 3/24 = 23/24. The formula gives 1 - 1/4! = 1 - 1/24 = 23/24. Perfect, that works. \n\nSo indeed, the sum telescopes to 1 - 1/(2021!). Therefore, the answer should be 1 minus 1 over 2021 factorial. Since the question asks for the sum up to n=2020, which corresponds to the term 2020/2021!, the formula still holds. \n\nI think that's it. Let me just confirm once more. Each term n/(n+1)! = 1/n! - 1/(n+1)!, so summing from n=1 to N gives 1 - 1/(N+1)!. Here, N is 2020, so the sum is 1 - 1/2021!.\n</think>To evaluate the sum \\(\\frac{1}{2!} + \\frac{2}{3!} + \\frac{3}{4!} + \\cdots + \\frac{2020}{2021!}\\), we start by expressing the general term of the series. The \\(n\\)-th term is given by \\(\\frac{n}{(n+1)!}\\).\n\nWe can rewrite \\(\\frac{n}{(n+1)!}\\) as follows:\n\\[\n\\frac{n}{(n+1)!} = \\frac{n****}{(n+1)!} = \\frac{n+1}{(n+1)!} - \\frac{1}{(n+1)!} = \\frac{1}{n!} - \\frac{1}{(n+1)!}.\n\\]\n\nThus, the series can be rewritten as:\n\\[\n\\sum_{n=1}^{2020} \\left( \\frac{1}{n!} - \\frac{1}{(n+1)!} \\right).\n\\]\n\nThis is a telescoping series. When we expand the sum, we get:\n\\[\n\\left( \\frac{1}{1!} - \\frac{1}{2!} \\right) + \\left( \\frac{1}{2!} - \\frac{1}{3!} \\right) + \\left( \\frac{1}{3!} - \\frac{1}{4!} \\right) + \\cdots + \\left( \\frac{1}{2020!} - \\frac{1}{2021!} \\right).\n\\]\n\nIn this series, all intermediate terms cancel out, leaving us with:\n\\[\n\\frac{1}{1!} - \\frac{1}{2021!} = 1 - \\frac{1}{2021!}.\n\\]\n\nTherefore, the sum of the series is:\n\\[\n\\boxed{1 - \\frac{1}{2021!}}.\n\\]"}