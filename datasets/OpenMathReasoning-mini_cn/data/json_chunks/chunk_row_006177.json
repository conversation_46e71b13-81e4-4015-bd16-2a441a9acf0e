{"row_number": 6177, "expected_answer": "98", "problem": "In a contest with 25 questions, you get 6 points for each correct answer, 1.5 points for each question left blank, and 0 points for each incorrect answer. How many different possible scores can you obtain between 0 and 150, inclusive?", "generated_solution": "<think>\nOkay, so I have this math problem here about a contest with 25 questions. The scoring is 6 points for each correct answer, 1.5 points for each blank question, and 0 for incorrect ones. The question is asking how many different possible scores can be obtained between 0 and 150, inclusive. Hmm, let me think through this step by step.\n\nFirst, let's parse the problem. There are 25 questions in total. For each correct answer, you get 6 points. If you leave a question blank, you get 1.5 points, and if you answer incorrectly, you get 0. So, the total score depends on the number of correct, blank, and incorrect answers. But since there are only 25 questions, the sum of correct, blank, and incorrect should be 25. So, maybe I can model this with variables.\n\nLet me denote:\n- c = number of correct answers\n- b = number of blank answers\n- i = number of incorrect answers\n\nSo, c + b + i = 25. But since we want the total score, the score S would be 6c + 1.5b + 0i. So, S = 6c + 1.5b. Since i = 25 - c - b, maybe we can write S in terms of c and b only. So, S = 6c + 1.5b.\n\nOur goal is to find the number of distinct values S can take, given that c and b are non-negative integers such that c + b ≤ 25 (since i is non-negative). So, c and b are integers from 0 up to 25, with the sum not exceeding 25. So, c can be from 0 to 25, and for each c, b can be from 0 to 25 - c.\n\nBut S is 6c + 1.5b. Now, 1.5 is equivalent to 3/2, so maybe it's better to write all terms with denominator 2 to avoid decimals. Let's see:\n\nS = 6c + (3/2)b. Multiplying numerator and denominator, we can write this as (12c + 3b)/2. So, S is a multiple of 1.5, since both 12c and 3b are divisible by 3. Wait, 12c is 12 times c, which is 3*4c, and 3b is 3 times b. So, the numerator is 3*(4c + b), so S = 3*(4c + b)/2. That means that S must be a multiple of 1.5, but more precisely, since 4c + b is an integer, S can be written as (3/2)*(4c + b). Therefore, 4c + b must be such that when multiplied by 3/2 gives us a real number. But since c and b are integers, 4c + b is also an integer, so S can be represented as a multiple of 1.5. Therefore, the possible scores are all numbers of the form 1.5k where k is an integer such that k = 4c + b for some non-negative integers c, b with c + b ≤ 25.\n\nWait, maybe that's a way to think about it. So instead of considering S directly, we can consider k = 4c + b, and then S = 1.5k. Therefore, each score S corresponds to a value of k where k is an integer between some minimum and maximum, and the number of possible k values would correspond to the number of possible S values. However, we need to ensure that for each k, there exist integers c and b such that k = 4c + b and c + b ≤25, with c ≥0, b ≥0.\n\nAlternatively, perhaps we can model this by considering possible values of c and b. Let's first see the possible range of scores. The minimum score is 0, which occurs when all answers are incorrect (c=0, b=0). The maximum score would be when all answers are correct, which is 6*25 = 150. So, the scores range from 0 to 150. But not all numbers in between are possible.\n\nGiven that each correct answer gives 6 points and each blank gives 1.5, the increments are in steps of 6 or 1.5. However, combining these can lead to different possible scores. The challenge is to find all possible sums of 6c + 1.5b where c and b are integers between 0 and 25 (with c + b ≤25).\n\nTo approach this, maybe we can consider the problem in terms of the possible values of c and b. Let's first fix the number of correct answers c, and then see how the blanks b affect the score.\n\nFor each c from 0 to 25, the maximum possible b is 25 - c. So, for each c, the score when b is maximum is 6c + 1.5*(25 - c) = 6c + 37.5 - 1.5c = 4.5c + 37.5. Similarly, when b is 0, the score is 6c. So, for each c, the possible scores range from 6c to 4.5c + 37.5, increasing by 1.5 each time we add a blank.\n\nBut since b can only take integer values from 0 to 25 - c, the number of scores for each c is (25 - c + 1), but each step increases the score by 1.5. Therefore, for each c, the possible scores are 6c + 1.5b, where b is 0,1,...,25 - c. So the scores for each c are spaced by 1.5.\n\nBut when c increases by 1, the range of scores shifts. The key is to see how these ranges overlap or create gaps. So, for example, when c=0, the scores go from 0 (b=0) to 37.5 (b=25). For c=1, the scores go from 6 (b=0) to 4.5*1 + 37.5 = 42. So, 6 to 42 in steps of 1.5. Similarly, for c=2, it's 12 to 49.5, etc.\n\nHowever, since each subsequent c's minimum score (6c) is 6 more than the previous c's minimum, but the maximum increases by 4.5 each time (since 4.5(c+1) + 37.5 - 4.5c - 37.5 = 4.5). So, the maximum increases by 4.5 each time c increases by 1.\n\nBut the steps between the scores for each c are 1.5. So, perhaps overlapping occurs between the ranges for different c's, which would mean some scores are attainable by multiple (c, b) pairs, but others might not. However, we just need to count the distinct scores, so overlaps don't matter except that they don't create gaps.\n\nAlternatively, maybe we can model the problem by considering that each score is of the form 6c + 1.5b where c and b are non-negative integers with c + b ≤25. Let's multiply all terms by 2 to eliminate the decimal. Then 2S = 12c + 3b. Let’s denote k = 12c + 3b, so k must be an integer, and k can range from 0 (when c=0, b=0) up to 12*25 + 3*25 = 300 + 75 = 375, but considering c + b ≤25. Wait, but when c + b ≤25, then the maximum 2S is 12c + 3b, with c + b ≤25.\n\nBut 12c + 3b = 3(4c + b). So, k = 3*(4c + b). Thus, k must be a multiple of 3. Therefore, 2S must be a multiple of 3, so S must be a multiple of 1.5. Therefore, all possible scores are multiples of 1.5 between 0 and 150. However, not all multiples of 1.5 are attainable. So, the problem reduces to finding how many numbers of the form 1.5k, where k is an integer, can be achieved by 4c + b with c + b ≤25, c, b non-negative integers.\n\nThus, instead of dealing with S, we can consider k = 4c + b, where c and b are non-negative integers with c + b ≤25. Then, since S = (3/2)k, so each k corresponds to S. Therefore, the number of possible S is equal to the number of possible k. So, we need to find the number of distinct integers k = 4c + b where c ≥0, b ≥0, and c + b ≤25.\n\nTherefore, the problem simplifies to: How many distinct values of 4c + b can be formed with c and b non-negative integers and c + b ≤25?\n\nSo, let's focus on that. Let’s denote k = 4c + b. We need to find the number of distinct k where c and b are non-negative integers such that c + b ≤25.\n\nThis seems like a linear Diophantine equation problem, but we need to count the number of distinct sums. Let's see.\n\nLet me consider for each possible c, the possible values of b.\n\nFor each c from 0 to 25, the maximum b is 25 - c. So for each c, the possible k values are:\n\nk = 4c + b, where b ranges from 0 to 25 - c.\n\nSo, for each c, the possible k's start at 4c and go up by 1 each time until 4c + (25 - c) = 3c +25.\n\nTherefore, the range of k for each c is from 4c to 3c +25, incrementing by 1. However, since we are increasing b by 1 each time, the step is 1. However, when c changes, the range shifts.\n\nSo, for each c, the minimum k is 4c, and the maximum k is 3c +25.\n\nBut as c increases, the minimum k (4c) increases, and the maximum k (3c +25) also increases, but at a different rate.\n\nWait, let's check for different c:\n\nc=0: k from 0 to 25 (b=0 to 25)\nc=1: k from 4 to 3 +25 =28 (since 3*1 +25=28)\nc=2: 8 to 6 +25=31\nc=3: 12 to 9 +25=34\n...\nc=25: 100 to 75 +25=100. So, when c=25, b can only be 0, so k=4*25 +0=100. So for c=25, k=100.\n\nWait, but for c=25, the maximum k would be 3*25 +25=100. Wait, that contradicts. Let me check:\n\nWait, if c + b ≤25, then for c=25, b must be ≤0, so b=0. Therefore, k=4*25 +0=100.\n\nSo, for each c, when c increases, the range of k starts at 4c and goes up to 3c +25. Wait, but when c increases by 1, 3c +25 increases by 3, so the upper bound for k increases by 3. However, the lower bound for k increases by 4. Therefore, there's an overlap in ranges for consecutive c's? Let's check.\n\nFor example, c=0: k=0 to 25\n\nc=1: k=4 to 28. So, the overlap between c=0 and c=1 is from 4 to 25. So, the union would be 0 to 28.\n\nSimilarly, c=2: k=8 to 31. Overlap with c=1's upper bound (28) starts at 8 to 28. So, union would be 0 to 31.\n\nBut as c increases, the lower bound 4c is going to overtake the previous upper bounds. Wait, when does 4c become greater than 3(c -1) +25?\n\nLet me solve 4c > 3(c -1) +25.\n\n4c > 3c -3 +25\n\n4c > 3c +22\n\nc > 22.\n\nSo, when c=23, 4*23=92, and 3*(22) +25=66 +25=91. So, for c=23, the minimum k is 92, which is greater than the previous upper bound for c=22, which would be 3*22 +25=66 +25=91. So, at c=23, there is a gap between 91 and 92. Similarly, c=24: 4*24=96, previous upper bound 3*23 +25=69 +25=94. So, 96 >94, so gap between 94 and 96. And c=25: 100, which is 4*25=100, previous upper bound 3*24 +25=72 +25=97. So, gap from 97 to 100.\n\nTherefore, for c from 0 to 22, the ranges of k overlap with the next c, but starting from c=23, the lower bound exceeds the previous upper bound, creating gaps.\n\nTherefore, to count the total number of distinct k's, we can split the problem into two parts: c from 0 to 22, where the ranges overlap, and c from 23 to 25, which are separate.\n\nFirst, let's handle c from 0 to 22.\n\nFor each c in 0 to 22, the range of k is from 4c to 3c +25. The next c's range starts at 4(c+1) and goes to 3(c+1) +25. So, let's check if there's overlap between consecutive c's.\n\nFor example, c and c+1:\n\nThe maximum k for c is 3c +25.\n\nThe minimum k for c+1 is 4(c+1) =4c +4.\n\nSo, 4c +4 vs 3c +25. When does 4c +4 ≤3c +25?\n\n4c +4 ≤3c +25 → c ≤21.\n\nTherefore, for c from 0 to 21, the next c+1's minimum is less than or equal to the current c's maximum. Therefore, the ranges overlap. For c=22, the maximum k is 3*22 +25=66 +25=91. The next c=23's minimum k is 4*23=92. So, no overlap here. Therefore, for c=0 to 21, the ranges overlap with the next one, but c=22 does not overlap with c=23.\n\nTherefore, for c=0 to 22, we can model the combined range as starting at 0 (when c=0) and going up to 3*22 +25=91.\n\nBut how many distinct k's are there in this combined range?\n\nSince the ranges overlap, we can think of it as a continuous range from 0 to 91, but need to check if there are any gaps.\n\nWait, but each c contributes a range from 4c to 3c +25. For c from 0 to 21, the next c+1's minimum k is 4(c+1) =4c +4. The previous c's maximum k is 3c +25. So, to check if there's a gap between c and c+1:\n\nGap would exist if 4c +4 > 3c +25 +1 (since k must be integers). Wait, but 3c +25 and 4c +4 are integers. If 4c +4 > 3c +25, then there's a gap. But 4c +4 >3c +25 implies c >21.\n\nBut as before, for c up to 21, the next c's minimum is less than or equal to the current c's maximum. So, for c=0 to 21, the next c+1's minimum is 4(c+1) ≤3c +25 → which is equivalent to c ≤21. Therefore, for c=0 to 21, there is no gap, meaning all the ranges overlap such that the entire span from 0 to 91 is covered without gaps.\n\nBut wait, even if the ranges overlap, it's possible that they don't cover every integer in between. For example, if the step between the ranges is larger than 1, then even with overlaps, some numbers might be skipped.\n\nWait, but each individual c's range (for a fixed c) is a sequence of integers from 4c to 3c +25 with step 1 (since b increases by 1 each time, and k=4c +b, so each increment of b increases k by 1). Therefore, for each c, the k's are consecutive integers from 4c to 3c +25.\n\nBut if consecutive c's have overlapping ranges (i.e., 4(c+1) ≤3c +25), then the combined ranges from c=0 to c=22 would cover the interval from 0 to 91 with possible overlaps but no gaps. Therefore, all integers from 0 up to 91 are attainable. Wait, but 3c +25 for c=22 is 3*22 +25=66 +25=91. So, from 0 to 91, inclusive? Wait, but for c=0, the maximum k is 25, then for c=1 up to 28, etc. So, how does the combination work?\n\nWait, perhaps not. For example, the first c=0 gives k from 0 to25. Then c=1 gives k from4 to28. So, combining these gives 0 to28. Then c=2 gives 8 to31. Combining with previous gives 0 to31. Continuing this way, each time expanding the upper bound. So, by the time we reach c=22, the upper bound is91.\n\nBut does every integer from0 to91 appear in at least one of these ranges?\n\nHmm. Let's take a sample in the middle. For example, take k=10. Is 10 achievable? Let's see. For c=2, 4*2=8, and b=2 would give k=8 +2=10. So yes. Similarly, k=7. For c=1, 4*1=4, and b=3 gives k=4 +3=7. So, yes. How about k=3? For c=0, b=3: 0*4 +3=3. Yes. So, seems like all integers from0 to91 are achievable. Wait, but what about k=1? Let's see. For c=0, b=1 gives k=1. Yes. k=2: c=0, b=2. Similarly, k=4: c=1, b=0. So, even the numbers in between 0 and25 are covered. Then for numbers above25, like26, let's see. For c=1, the maximum k is28. So, 26 would be c=1, b=22 (but c + b=1+22=23 ≤25). So, 4*1 +22=26. Yes. So, 26 is achievable. Similarly, 27: c=1, b=23; 28: c=1, b=24 (since 1+24=25). Wait, but c=1 and b=24: c + b=25, which is allowed. So, 4*1 +24=28. Then c=2, k=8 to31. So, 29,30,31 can be achieved. So, every integer in between is covered.\n\nTherefore, for c from0 to22, all integers from0 to91 are achievable.\n\nThen, for c=23,24,25, the k values are:\n\nc=23: k=4*23 +b=92 +b, where b can be0 to2 (since c +b ≤25 →b ≤2). So, b=0,1,2. Thus, k=92,93,94.\n\nc=24: k=4*24 +b=96 +b, where b can be0 or1 (since24 +b ≤25 →b ≤1). So, k=96,97.\n\nc=25: k=4*25 +0=100.\n\nTherefore, the k values for c=23,24,25 are 92,93,94,96,97,100.\n\nBut note that between the upper bound of c=22 (which is91) and c=23's minimum of92, there is a gap at91 to92. But 91 is the maximum for c=22. Then 92 is the next.\n\nAdditionally, c=23's maximum is94, c=24's minimum is96, so gap between94 and96. Then c=24's maximum is97, and c=25 is100, so gap between97 and100.\n\nTherefore, the k values beyond91 are 92,93,94,96,97,100. So, these are 6 distinct values.\n\nTherefore, total k values are from0 to91 (92 numbers) plus6 more: total 92 +6=98. But wait, from0 to91 inclusive, that's92 numbers (since 91 -0 +1=92). Then adding 6 more gives98. But wait, let's check:\n\nWait, the k values are:\n\nFrom0 to91 inclusive: 92 numbers.\n\nThen additional k=92,93,94,96,97,100. That's6 more. So, 92 +6=98.\n\nBut wait, is100 included? Yes, because when c=25, k=100. So, total distinct k values are98. Therefore, since S=1.5k, then S can be from0 to150, but only the values1.5*k where k is one of these98 numbers. Therefore, the number of distinct scores is98.\n\nBut wait, but let's verify this with some edge cases. Let's see: when c=22, the maximum k is3*22 +25=66 +25=91, which is correct. Then c=23, b=0:92, b=1:93, b=2:94. c=24:96,97. c=25:100. So, between91 and100, we have gaps at95,98,99. So, the k values after91 are92,93,94,96,97,100. So, 6 additional k's. So, 92 +6=98.\n\nBut does that mean the total number of scores is98? Let's check another way.\n\nAlternatively, the number of possible k's is the number of integers from0 to100 inclusive, minus the numbers that cannot be expressed as4c +b with c +b ≤25. But that might be more complicated.\n\nAlternatively, let's calculate how many k's there are. For c from0 to22, all k's from0 to91 are possible. That's92 values. Then, for c=23,24,25, we have additional k's:92,93,94,96,97,100. So, total98.\n\nBut let's check if there are any overlaps between the k's from c=23,24,25 and the previous ones. For example, is96 achievable by a lower c? Let's see. Let's take k=96. Could there be a c <23 such that4c +b=96, with c +b ≤25?\n\nSo, solving4c +b=96, c +b ≤25. Let's rearrange:\n\nb=96 -4c.\n\nSubstitute into c +b ≤25: c +96 -4c ≤25 → -3c ≤-71 → 3c ≥71 → c ≥71/3≈23.666. So, c must be24 or25. Since c must be integer. But c=24: then b=96 -4*24=96-96=0. Then c +b=24+0=24 ≤25. So yes, that's c=24,b=0. But also, c=23: b=96 -92=4. But c=23 +b=23+4=27>25, which is invalid. So, k=96 is only achievable by c=24,b=0. Similarly, k=92: For c=23, b=0. Let's check if lower c can achieve k=92.\n\n4c +b=92. Let's solve for c:\n\nIf c=23: b=92 -92=0. Valid.\n\nIf c=22:4*22=88, so b=92-88=4. c +b=22+4=26>25. Not valid.\n\nc=21:4*21=84. b=92-84=8. c +b=29>25. Not valid.\n\nSo, only c=23,b=0 is valid. So, k=92 is unique to c=23. Similarly, other k's in 92,93,94,96,97,100 cannot be achieved by lower c's.\n\nTherefore, total distinct k's are92 +6=98. Therefore, the number of distinct scores S=1.5k is also98, since each k corresponds to a unique S. Since k is unique, multiplying by1.5 doesn't create duplicates because the mapping from k to S is linear (injective). So, if k1 ≠k2, then1.5k1 ≠1.5k2. Therefore, the number of distinct scores is98.\n\nWait, but let me check if that's the case. Since k is an integer, S=1.5k= 3k/2. So, for even k, S is a multiple of3/2. For example, k=2: S=3. But is there a case where two different k's can result in the same S? For instance, if k is even and odd? Wait, no, because S=1.5k. If k is even, say k=2m, then S=3m. If k is odd, say k=2m+1, then S=3m +1.5. Therefore, S can be either an integer or a half-integer. But since we are considering all possible S between0 and150 inclusive, the possible S's are multiples of1.5. Therefore, each k corresponds to a unique S. Hence, the number of S's is equal to the number of k's, which is98.\n\nWait, but let me check with a small example. Suppose we have c=0, b=1: k=1, S=1.5. c=0, b=2: k=2, S=3. c=1, b=0: k=4, S=6. So, these are all distinct. Therefore, each k is unique, so S is unique. Therefore, the count is98.\n\nTherefore, the answer is98. However, let me confirm once more.\n\nFirst, the range for c=0 to22 gives k from0 to91, which is92 numbers. Then, for c=23,24,25, we have additional k's:92,93,94,96,97,100. That's6 numbers. So, total is92 +6=98. So, the number of distinct scores is98.\n\nBut let me check if I considered all possibilities. For example, is there a possible k=95? Let's see. 4c +b=95. So, solving for c and b with c +b <=25.\n\nIf c=23:4*23=92. Then b=3. So, c +b=23 +3=26>25. Not allowed.\n\nc=22:4*22=88. b=7. c +b=29>25. Not allowed.\n\nc=21:4*21=84. b=11. c +b=21 +11=32>25. No. Similarly, decreasing c leads to higher b and higher c +b. So, it's impossible. So, k=95 is not achievable. Therefore, 95 is a gap.\n\nSimilarly, k=98:4c +b=98. For c=24:4*24=96. b=2. Then c +b=24 +2=26>25. Not allowed. c=23:4*23=92. b=6. c +b=29>25. So, not possible. So, k=98 is not achievable. Therefore, 98 is also a gap. Similarly, k=99: same reasoning.\n\nTherefore, the missing k's are95,98,99. So, in the upper ranges, after91, the achievable k's are92,93,94,96,97,100. So, six numbers.\n\nTherefore, total of98 distinct k's. So, the answer should be98.\n\nBut let's check another perspective. Let's use generating functions. The generating function for the number of ways to achieve a score would be the product of generating functions for correct answers and blank answers. Since each correct answer contributes 6, which is 12 half-points, and each blank contributes1.5, which is3 half-points. Alternatively, since we have k=4c +b, where S= (3/2)k, so perhaps we can think of the generating function as (1 + x^4 + x^8 + ... + x^{100}) for correct answers (since c can be 0 to25, so4c can be up to100), multiplied by (1 +x +x^2 + ... +x^{25}) for the blanks. Wait, but actually, since c +b ≤25, the generating function would be a convolution. However, this might be complicated, but perhaps we can model it as:\n\nThe generating function for k=4c +b with c +b ≤25 is the coefficient of x^k in the generating function:\n\nSum_{c=0 to25} x^{4c} * Sum_{b=0 to25 -c} x^b.\n\nBut calculating this generating function's number of terms is equivalent to the number of distinct k's. However, this approach would require expanding the generating function, which might be tedious. Alternatively, maybe using dynamic programming to count the number of distinct k's.\n\nBut given that we already have a logical breakdown resulting in98, and the step-by-step checks seem to confirm that, perhaps98 is the correct answer. However, to be thorough, maybe compute the number of k's more carefully.\n\nAlternatively, note that for c from0 to22, the minimum k for each c is4c, and the maximum is3c +25. But for c from0 to22, the ranges overlap such that each subsequent c fills in the next numbers. Therefore, the total number of k's from0 to91 is91 -0 +1=92.\n\nThen, the additional k's from c=23,24,25 are92,93,94,96,97,100. So, that's6 numbers. Total98.\n\nTherefore, the answer is98. Hence, the number of different possible scores is98, which can be written as \\boxed{98}.\n\n**Final Answer**\n\\boxed{98}\n</think>To determine the number of different possible scores in a contest with 25 questions, where each correct answer earns 6 points, each blank answer earns 1.5 points, and each incorrect answer earns 0 points, we start by expressing the score \\( S \\) in terms of the number of correct answers \\( c \\) and blank answers \\( b \\):\n\n\\[ S = 6c + 1.5b \\]\n\nWe can rewrite this as:\n\n\\[ S = \\frac{3}{2}(4c + b) \\]\n\nLet \\( k = 4c + b \\). The score \\( S \\) is then \\( \\frac{3}{2}k \\). We need to find the number of distinct values of \\( k \\) such that \\( c \\) and \\( b \\) are non-negative integers and \\( c + b \\leq 25 \\).\n\n### Key Steps:\n\n1. **Range of \\( k \\) for each \\( c \\)**:\n   - For each \\( c \\) from 0 to 25, \\( b \\) ranges from 0 to \\( 25 - c \\).\n   - The value of \\( k = 4c + b \\) ranges from \\( 4c \\) (when \\( b = 0 \\)) to \\( 4c + (25 - c) = 3c + 25 \\).\n\n2. **Overlap Analysis**:\n   - For \\( c \\) from 0 to 22, the ranges of \\( k \\) overlap with the next \\( c \\) values, covering all integers from 0 to 91.\n     - For \\( c = 0 \\), \\( k \\) ranges from 0 to 25.\n     - For \\( c = 1 \\), \\( k \\) ranges from 4 to 28.\n     - For \\( c = 2 \\), \\( k \\) ranges from 8 to 31.\n     - ...\n     - For \\( c = 22 \\), \\( k \\) ranges from 88 to 91.\n   - For \\( c = 23, 24, 25 \\), the ranges of \\( k \\) are non-overlapping and contribute additional values: 92, 93, 94, 96, 97, and 100.\n     - For \\( c = 23 \\), \\( k \\) ranges from 92 to 94.\n     - For \\( c = 24 \\), \\( k \\) ranges from 96 to 97.\n     - For \\( c = 25 \\), \\( k \\) is 100.\n\n3. **Counting Distinct Values**:\n   - The range from 0 to 91 includes 92 distinct values.\n   - The additional values from \\( c = 23, 24, 25 \\) are 6 distinct values: 92, 93, 94, 96, 97, and 100.\n\n### Conclusion:\nThe total number of distinct values of \\( k \\) is \\( 92 + 6 = 98 \\). Since each \\( k \\) corresponds to a unique score \\( S = \\frac{3}{2}k \\), the number of different possible scores is:\n\n\\[\n\\boxed{98}\n\\]"}