{"row_number": 6281, "expected_answer": "45", "problem": "Given that \\(x^2 + y^2 + z^2 = 3\\), find the maximal value of \\(7x + 25y + z\\).", "generated_solution": "<think>\nOkay, so I need to find the maximum value of 7x + 25y + z given that x² + y² + z² = 3. Hmm, let me think. This seems like a problem where I can use the <PERSON><PERSON><PERSON>-<PERSON> inequality. From what I remember, the <PERSON><PERSON><PERSON>-<PERSON> inequality states that for any vectors a and b, the absolute value of their dot product is less than or equal to the product of their magnitudes. So, maybe I can apply that here.\n\nLet me write down the Cauchy-<PERSON> inequality formula: |a · b| ≤ ||a|| ||b||. In this case, the expression we want to maximize is the dot product of two vectors. Let me see. If I let vector a be (7, 25, 1) and vector b be (x, y, z), then their dot product is exactly 7x + 25y + z. That makes sense. \n\nSo, according to <PERSON><PERSON><PERSON><PERSON><PERSON>, the maximum value of 7x + 25y + z would be the magnitude of vector a multiplied by the magnitude of vector b. But the magnitude of vector b is given by the constraint x² + y² + z² = 3, which is √3. Wait, but the magnitude of vector a is √(7² + 25² + 1²). Let me calculate that.\n\nCalculating the magnitude of a: 7 squared is 49, 25 squared is 625, 1 squared is 1. So adding those up: 49 + 625 + 1 = 675. So the magnitude is √675. Let me simplify √675. Breaking it down, 675 is 25*27, and 27 is 9*3, so 675 = 25*9*3. Therefore, √(25*9*3) = 5*3*√3 = 15√3. So the magnitude of vector a is 15√3.\n\nSo according to Cauchy-Schwarz, the maximum value of 7x + 25y + z is 15√3 multiplied by √3 (the magnitude of vector b). Wait, √3 * 15√3 is 15*3 = 45. So the maximum value is 45? That seems straightforward, but let me double-check.\n\nWait, actually, the Cauchy-Schwarz inequality says that the maximum is ||a|| * ||b||. Since the constraint is x² + y² + z² = 3, then ||b|| is √3. So multiplying 15√3 (||a||) by √3 (||b||) gives 15*3 = 45. Yeah, that looks right.\n\nBut hold on, is there a possibility that the maximum can't be achieved? For the Cauchy-Schwarz inequality, equality holds when the vectors are proportional. So to check, if there exists a scalar λ such that (x, y, z) = λ*(7, 25, 1). Then substituting into the constraint x² + y² + z² = 3, we get (λ²)(7² +25² +1²) = 3. Which is λ²*675 = 3, so λ² = 3/675 = 1/225, so λ = 1/15. Then x = 7/15, y = 25/15 = 5/3, z = 1/15.\n\nSo plugging these back into 7x +25y + z, we get 7*(7/15) +25*(5/3) +1*(1/15). Let's compute each term:\n\n7*(7/15) = 49/15\n\n25*(5/3) = 125/3 = 625/15\n\n1*(1/15) = 1/15\n\nAdding them up: 49 + 625 + 1 = 675 over 15, which is 675/15 = 45. So yes, the maximum is indeed 45, and it's achievable. So that's the answer.\n\nAlternatively, I could use Lagrange multipliers. Let me try that method to confirm. Let’s set up the function to maximize: f(x, y, z) = 7x +25y +z with the constraint g(x, y, z) = x² + y² + z² -3 =0. The Lagrangian is L = 7x +25y +z - λ(x² + y² + z² -3). Taking partial derivatives:\n\n∂L/∂x =7 - 2λx =0 ⇒ 7 =2λx\n\n∂L/∂y =25 -2λy=0 ⇒25=2λy\n\n∂L/∂z =1 -2λz=0 ⇒1=2λz\n\n∂L/∂λ = -(x² + y² + z² -3)=0 ⇒x² + y² + z²=3.\n\nFrom the first three equations:\n\nx =7/(2λ)\n\ny=25/(2λ)\n\nz=1/(2λ)\n\nPlugging into the constraint:\n\n(7/(2λ))² + (25/(2λ))² + (1/(2λ))² =3\n\n= (49 +625 +1)/(4λ²) =3\n\n=675/(4λ²)=3 ⇒ 4λ²=675/3=225 ⇒ λ²=225/4 ⇒ λ=15/2 or λ=-15/2. Since we’re maximizing, let's take λ positive. So λ=15/2.\n\nThen x=7/(2*(15/2))=7/15\n\ny=25/(15)=5/3\n\nz=1/(15)\n\nSame as before. So when we plug these into 7x +25y +z, it gives 45. So both methods confirm the answer is 45. That seems solid. Therefore, the maximum value is 45.\n</think>To find the maximal value of \\(7x + 25y + z\\) given the constraint \\(x^2 + y^2 + z^2 = 3\\), we can use the Cauchy-Schwarz inequality.\n\nLet \\(\\mathbf{a} = (7, 25, 1)\\) and \\(\\mathbf{b} = (x, y, z)\\). The Cauchy-Schwarz inequality states:\n\n\\[\n| \\mathbf{a} \\cdot \\mathbf{b} | \\leq \\|\\mathbf{a}\\| \\|\\mathbf{b}\\|\n\\]\n\nFirst, we compute the magnitudes of \\(\\mathbf{a}\\) and \\(\\mathbf{b}\\):\n\n\\[\n\\|\\mathbf{a}\\| = \\sqrt{7^2 + 25^2 + 1^2} = \\sqrt{49 + 625 + 1} = \\sqrt{675} = 15\\sqrt{3}\n\\]\n\nGiven the constraint \\(x^2 + y^2 + z^2 = 3\\), we have:\n\n\\[\n\\|\\mathbf{b}\\| = \\sqrt{x^2 + y^2 + z^2} = \\sqrt{3}\n\\]\n\nThus, the inequality becomes:\n\n\\[\n|7x + 25y + z| \\leq 15\\sqrt{3} \\cdot \\sqrt{3} = 15 \\cdot 3 = 45\n\\]\n\nTo achieve equality in the Cauchy-Schwarz inequality, \\(\\mathbf{b}\\) must be a scalar multiple of \\(\\mathbf{a}\\). Let \\(\\mathbf{b} = \\lambda \\mathbf{a}\\). Then:\n\n\\[\n(x, y, z) = \\lambda (7, 25, 1)\n\\]\n\nSubstituting into the constraint \\(x^2 + y^2 + z^2 = 3\\):\n\n\\[\n(7\\lambda)^2 + (25\\lambda)^2 + (1\\lambda)^2 = 3\n\\]\n\n\\[\n49\\lambda^2 + 625\\lambda^2 + \\lambda^2 = 3\n\\]\n\n\\[\n675\\lambda^2 = 3\n\\]\n\n\\[\n\\lambda^2 = \\frac{3}{675} = \\frac{1}{225}\n\\]\n\n\\[\n\\lambda = \\frac{1}{15}\n\\]\n\nThus, the values of \\(x, y, z\\) are:\n\n\\[\nx = 7\\lambda = 7 \\cdot \\frac{1}{15} = \\frac{7}{15}\n\\]\n\n\\[\ny = 25\\lambda = 25 \\cdot \\frac{1}{15} = \\frac{5}{3}\n\\]\n\n\\[\nz = \\lambda = \\frac{1}{15}\n\\]\n\nSubstituting these values into \\(7x + 25y + z\\):\n\n\\[\n7x + 25y + z = 7 \\cdot \\frac{7}{15} + 25 \\cdot \\frac{5}{3} + \\frac{1}{15}\n\\]\n\n\\[\n= \\frac{49}{15} + \\frac{125}{3} + \\frac{1}{15}\n\\]\n\n\\[\n= \\frac{49}{15} + \\frac{625}{15} + \\frac{1}{15}\n\\]\n\n\\[\n= \\frac{49 + 625 + 1}{15} = \\frac{675}{15} = 45\n\\]\n\nTherefore, the maximal value of \\(7x + 25y + z\\) given \\(x^2 + y^2 + z^2 = 3\\) is:\n\n\\[\n\\boxed{45}\n\\]"}