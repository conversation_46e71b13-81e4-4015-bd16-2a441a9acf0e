{"row_number": 6344, "expected_answer": "160", "problem": "Let \\( f(x) \\) denote the unique integer \\( k \\) such that \\( |k - \\sqrt[3]{x}| < \\frac{1}{2} \\). Find the remainder when \\( \\sum_{n=1}^{2021} f(n) \\) is divided by 1000.", "generated_solution": "<think>\nOkay, let me try to figure out this problem. So, we have a function f(x) which gives the unique integer k such that the distance between k and the cube root of x is less than 1/2. In other words, f(x) is the nearest integer to the cube root of x, right? Unless the cube root is exactly halfway between two integers, but since we're dealing with integers x, maybe that case doesn't come up much. Hmm.\n\nThe problem is asking for the sum of f(n) from n=1 to 2021, and then find the remainder when that sum is divided by 1000. So first, I need to compute the sum, then take modulo 1000. But 2021 is a pretty big number, so summing each term individually would take too long. There must be a pattern or a formula to compute this more efficiently.\n\nLet me start by understanding how f(n) behaves. For a given integer k, the values of n where f(n) = k are the numbers n for which k - 1/2 < cube root(n) < k + 1/2. If I cube those inequalities, I can find the range of n that correspond to each k. So, solving for n:\n\n(k - 1/2)^3 < n < (k + 1/2)^3\n\nBut since n must be an integer, the number of integers n in that interval would be floor((k + 1/2)^3) - floor((k - 1/2)^3). However, I need to check if the endpoints are integers. Let's see.\n\nLet me compute some small k values to see.\n\nFor k=1:\nLeft bound: (1 - 0.5)^3 = 0.5^3 = 0.125\nRight bound: (1 + 0.5)^3 = 1.5^3 = 3.375\nSo n must be between 0.125 and 3.375, so integers 1, 2, 3. So 3 numbers. Therefore, f(n)=1 for n=1,2,3.\n\nFor k=2:\nLeft: (2 - 0.5)^3 = 1.5^3 = 3.375\nRight: (2 + 0.5)^3 = 2.5^3 = 15.625\nSo n ranges from 4 to 15.625, so integers 4 through 15. That's 12 numbers. Therefore, f(n)=2 for n=4 to 15 inclusive. Wait, 15 - 4 + 1 = 12. Yes.\n\nSimilarly for k=3:\nLeft: 2.5^3 = 15.625\nRight: 3.5^3 = 42.875\nSo n from 16 to 42. That's 42 - 16 + 1 = 27 numbers. So f(n)=3 for n=16 to 42.\n\nWait, but let me check if (k + 0.5)^3 is an integer. Since k is integer, (k + 0.5) is a half-integer, and its cube is a number with fractions. So the upper bound for n when moving from k to k+1 is (k+0.5)^3, which is not integer, so the next integer is floor((k+0.5)^3) + 1? Wait, no. Let me check with k=1. When k=1, the upper bound was 3.375, so the next integer after 3.375 is 4, so the last n for k=1 is 3, and the first n for k=2 is 4. So yes, each time, the lower bound is the previous upper bound rounded up, and the upper bound is the next upper bound rounded down. Hmm.\n\nSo, in general, for each integer k >= 1, the numbers n where f(n)=k are from ceil((k - 0.5)^3) to floor((k + 0.5)^3 - 1e-9)? Wait, but actually, the interval is ( (k - 0.5)^3, (k + 0.5)^3 ), so the integers n in this interval would be from floor((k - 0.5)^3) + 1 up to floor((k + 0.5)^3). Let me verify with k=1:\n\n(k - 0.5)^3 = 0.5^3 = 0.125, floor(0.125) = 0, so starting at 0 + 1 = 1. (k + 0.5)^3 = 1.5^3 = 3.375, floor(3.375) = 3. So n=1,2,3. That works. For k=2:\n\n(k - 0.5)^3 = 1.5^3 = 3.375, floor(3.375)=3, so start at 4. (k + 0.5)^3 = 2.5^3=15.625, floor(15.625)=15. So n=4 to 15. That's 12 numbers. Correct. So the formula would be the number of n for each k is floor((k + 0.5)^3) - floor((k - 0.5)^3). Therefore, the count is (k + 0.5)^3 - (k - 0.5)^3, but since we're dealing with floor, it's approximately that value rounded down. However, since (k + 0.5)^3 is not an integer, the floor would just be the integer part. So for each k, the number of terms is floor((k + 0.5)^3) - floor((k - 0.5)^3).\n\nBut maybe it's easier to compute (k + 0.5)^3 and (k - 0.5)^3 exactly, then take the difference. Let's compute that.\n\nCompute (k + 0.5)^3 - (k - 0.5)^3.\n\nExpand both:\n\n(k + 0.5)^3 = k^3 + 3k^2*(0.5) + 3k*(0.5)^2 + (0.5)^3 = k^3 + 1.5k^2 + 0.75k + 0.125\n\nSimilarly, (k - 0.5)^3 = k^3 - 1.5k^2 + 0.75k - 0.125\n\nSubtracting the two:\n\n(k + 0.5)^3 - (k - 0.5)^3 = [k^3 + 1.5k^2 + 0.75k + 0.125] - [k^3 - 1.5k^2 + 0.75k - 0.125] = 3k^2 + 0.25\n\nTherefore, the difference is 3k^2 + 0.25. But since (k + 0.5)^3 and (k - 0.5)^3 are not integers, the number of integers between them is floor((k + 0.5)^3) - floor((k - 0.5)^3). But (k + 0.5)^3 = m + 0.875 where m is an integer? Wait, no. Let's take k=1: (1 + 0.5)^3=3.375, which is 3 + 0.375. (1 - 0.5)^3=0.125. So the difference is 3.375 - 0.125 = 3.25. But floor(3.375) - floor(0.125) = 3 - 0 = 3. But 3.25 floored? Wait, that's the actual difference is 3.25, but the floor difference is 3, which is 3.25 - 0.25. Hmm. So the difference between the floors is equal to 3k^2 + 0.25 - fractional part? Not sure.\n\nAlternatively, since (k + 0.5)^3 - (k - 0.5)^3 = 3k^2 + 0.25, and since both (k + 0.5)^3 and (k - 0.5)^3 have fractional parts, their floor difference would be approximately 3k^2 + 0.25 minus the fractional parts. But perhaps the actual number of integers is 3k^2 + 0.25 rounded down? Wait, but with k integer, 3k^2 is integer, so 3k^2 + 0.25 is a number with 0.25. So if we take the floor of that, it would be 3k^2. But for example, when k=1, 3*1 + 0.25=3.25, floor is 3, which matches the earlier example. Similarly, k=2: 3*(4) + 0.25=12.25, floor is 12, which matches. k=3: 3*9 +0.25=27.25, floor is 27. So yes, it seems like the number of terms for each k is floor(3k^2 + 0.25) = 3k^2. Wait, but 3k^2 + 0.25 is not an integer, but when you subtract the two floors, you get 3k^2. So in general, the number of integers n for which f(n)=k is 3k^2. Wait, that's a nice result. So for each k, the number of terms is 3k². Let's verify this.\n\nk=1: 3*1²=3 terms, which matches (n=1,2,3). k=2: 3*4=12 terms (n=4 to 15). Correct. k=3: 3*9=27 terms (n=16 to 42, which is 42-16+1=27). Wait, 42-16=26, so 26+1=27. Yes. So that works. Then k=4: 3*16=48 terms. Let's check:\n\nFor k=4, (4 - 0.5)^3 = 3.5³ = 42.875, (4 + 0.5)^3 = 4.5³ = 91.125. So the integers from 43 to 91. So 91 - 43 +1 = 49. Wait, but 3k²=3*16=48, but actual number of terms is 49. Wait, so that contradicts. So perhaps my earlier conclusion was incorrect.\n\nWait, so maybe the formula isn't exactly 3k². Let's check for k=4.\n\nCompute (4 + 0.5)^3 - (4 - 0.5)^3 = 4.5³ - 3.5³.\n\nCalculate 4.5³: 4.5*4.5=20.25, 20.25*4.5=91.125\n\n3.5³=42.875\n\nDifference: 91.125 - 42.875 = 48.25\n\nSo the number of integers between 42.875 and 91.125 is from 43 to 91 inclusive. 91 - 43 +1 = 49. So 49. But 3k² +0.25 = 3*16 +0.25=48.25. So floor(48.25)=48, but actual count is 49. Hmm. So the formula floor((k + 0.5)^3) - floor((k - 0.5)^3) equals 49 for k=4. But 3k² +0.25 is 48.25, which is 48.25, but the actual difference is 49. So my previous reasoning is flawed. Therefore, perhaps the number of terms is actually floor((k + 0.5)^3) - floor((k - 0.5)^3). Which for k=4 is floor(91.125) - floor(42.875) = 91 - 42 = 49. Which is indeed 49. So that's 49. So the formula isn't exactly 3k², but sometimes 3k² + 1. Wait, 49 - 48 = 1. So maybe for some k, the difference is 3k² +1? Hmm.\n\nAlternatively, perhaps the formula is floor(3k² + 0.25 + ...). Let's see. For k=1: 3*1² +0.25=3.25, floor is 3. For k=2: 3*4 +0.25=12.25, floor is 12. For k=3: 3*9 +0.25=27.25, floor is 27. For k=4: 3*16 +0.25=48.25, floor is 48. But the actual count is 49. So floor(3k² +0.25) +1? But 48.25 floored is 48, plus 1 is 49. Hmm. But for k=1, 3.25 floored is 3, plus 1 would be 4, which is not correct. Hmm. So that's inconsistent.\n\nAlternatively, maybe the formula is (3k² + 0.25) rounded to the nearest integer. For k=1, 3.25 rounded is 3. For k=2, 12.25 rounded is 12. For k=3, 27.25 rounded is 27. For k=4, 48.25 rounded is 48. But in reality, for k=4, it's 49. So that's still off.\n\nSo perhaps the initial approach of calculating the difference (k + 0.5)^3 - (k - 0.5)^3 = 3k² +0.25, and then the number of integers in that interval is either 3k² or 3k² +1. So sometimes, when the fractional part of (k - 0.5)^3 is small, and the fractional part of (k + 0.5)^3 is large, you get an extra integer in the interval.\n\nAlternatively, maybe we can use the formula that the number of integers between a and b is floor(b) - ceil(a) + 1. So for each k:\n\nNumber of n where f(n) =k is floor((k + 0.5)^3) - ceil((k - 0.5)^3) +1.\n\nLet's compute this for k=4:\n\nceil(3.5³) = ceil(42.875) = 43\n\nfloor(4.5³) = floor(91.125) =91\n\nSo 91 -43 +1=49. Which is correct. For k=3:\n\nceil(2.5³)=ceil(15.625)=16\n\nfloor(3.5³)=floor(42.875)=42\n\n42 -16 +1=27. Correct. For k=2:\n\nceil(1.5³)=ceil(3.375)=4\n\nfloor(2.5³)=floor(15.625)=15\n\n15 -4 +1=12. Correct. For k=1:\n\nceil(0.5³)=ceil(0.125)=1\n\nfloor(1.5³)=floor(3.375)=3\n\n3 -1 +1=3. Correct. So yes, the formula is floor((k + 0.5)^3) - ceil((k - 0.5)^3) +1. So let's express this in terms of k:\n\nWe can also note that ceil((k - 0.5)^3) = floor((k - 0.5)^3) +1 if (k -0.5)^3 is not an integer, which it never is, since k is integer and 0.5 is a fraction. So ceil(a) = floor(a) +1 for non-integer a. Thus, ceil((k -0.5)^3) = floor((k -0.5)^3) +1. Then:\n\nNumber of n = floor((k + 0.5)^3) - (floor((k -0.5)^3) +1) +1 = floor((k +0.5)^3) - floor((k -0.5)^3). So that reduces back to the original formula. So the number of terms is floor((k +0.5)^3) - floor((k -0.5)^3). But when k increases, how does this behave?\n\nAlternatively, maybe we can find a general formula for floor((k +0.5)^3) - floor((k -0.5)^3). Let's compute (k +0.5)^3 - (k -0.5)^3 = 3k² +0.25, as earlier. So the exact difference is 3k² +0.25. However, the floor difference is floor((k +0.5)^3) - floor((k -0.5)^3) = (k +0.5)^3 - fractional_part((k +0.5)^3) - [ (k -0.5)^3 - fractional_part((k -0.5)^3) ] = (k +0.5)^3 - (k -0.5)^3 - [ fractional_part((k +0.5)^3) - fractional_part((k -0.5)^3) ] = 3k² +0.25 - [ fractional_part((k +0.5)^3) - fractional_part((k -0.5)^3) ]\n\nBut fractional parts are between 0 and 1. So the term [ fractional_part((k +0.5)^3) - fractional_part((k -0.5)^3) ] can be positive or negative?\n\nWait, (k +0.5)^3 is something like m + c where m is integer and 0 <= c <1. Similarly, (k -0.5)^3 is n + d where n is integer and 0 <= d <1. Then the floor difference is m - n. So (m + c) - (n + d) = m -n + c - d. But (k +0.5)^3 - (k -0.5)^3 = 3k² +0.25. So m -n + c -d =3k² +0.25. But m -n is the floor difference, which we're trying to compute.\n\nBut m -n = 3k² +0.25 - (c - d). Since c and d are between 0 and1, c - d is between -1 and1. So 3k² +0.25 - (c - d) = m -n. So m -n must be an integer. So 3k² +0.25 - (c - d) must be integer. Therefore, 0.25 - (c -d) must be the fractional part that when subtracted from 3k² +0.25 gives an integer.\n\nBut since m -n is integer, and 3k² is integer, then 0.25 - (c -d) must be integer. But 0.25 - (c -d) is at most 0.25 - (-1) =1.25 and at least 0.25 -1= -0.75. So the only integer in that range is 0 or 1. Hmm. Therefore, 0.25 - (c -d) =0 or 1. Therefore, c -d=0.25 or -0.75. Since c and d are in [0,1), c -d can be in (-1,1). So possible.\n\nTherefore, m -n =3k² +0.25 - (0.25 -d +c). Wait, maybe this is getting too convoluted. Let's try to compute for specific k:\n\nTake k=4:\n\n(k +0.5)^3 =4.5³=91.125, fractional part is 0.125\n\n(k -0.5)^3=3.5³=42.875, fractional part is 0.875\n\nSo c=0.125, d=0.875. So c -d= -0.75. Then:\n\nm -n= 3k² +0.25 - (c -d)= 3*16 +0.25 - (-0.75)=48 +0.25 +0.75=49. Which is correct, as m -n=91 -42=49.\n\nSimilarly, for k=1:\n\n(k +0.5)^3=1.5³=3.375, fractional part 0.375\n\n(k -0.5)^3=0.5³=0.125, fractional part 0.125\n\nc=0.375, d=0.125. c -d=0.25.\n\nThen m -n=3*1 +0.25 -0.25=3+0.25 -0.25=3. Which is correct, 3 -0=3.\n\nFor k=2:\n\nc= (2.5³)=15.625 fractional part 0.625\n\nd= (1.5³)=3.375 fractional part 0.375\n\nc -d=0.625 -0.375=0.25\n\nm -n= 3*4 +0.25 -0.25=12 +0.25 -0.25=12. Correct.\n\nk=3:\n\n(k +0.5)^3=3.5³=42.875, fractional 0.875\n\n(k -0.5)^3=2.5³=15.625, fractional 0.625\n\nc -d=0.875 -0.625=0.25\n\nm -n=3*9 +0.25 -0.25=27. Correct.\n\nSo in these cases, when k >=1 and k=1,2,3,4, except for k=4, c -d=0.25. Wait, for k=4, c -d= -0.75. So that seems different.\n\nWait, but the formula for m -n is 3k² +0.25 - (c -d). For k=4:\n\n3k²=48, 0.25 - (c -d)=0.25 - (-0.75)=1. So 48 +1=49. For k=1,2,3: 3k² +0.25 -0.25=3k². Hence, when c -d=0.25, m -n=3k². When c -d=-0.75, m -n=3k² +1.\n\nSo in general, is c -d=0.25 or -0.75? For k=1,2,3: (k +0.5)^3's fractional part is 0.375,0.625,0.875; (k -0.5)^3's fractional part is 0.125,0.375,0.625. Therefore, c -d=0.25 each time. For k=4: c=0.125, d=0.875, so c -d= -0.75. For k=5: Let's compute.\n\n(k +0.5)=5.5, (5.5)^3=166.375, fractional part 0.375\n\n(k -0.5)=4.5, 4.5³=91.125, fractional 0.125\n\nc=0.375, d=0.125. c -d=0.25.\n\nSo m -n=3*25 +0.25 -0.25=75. So the number of terms for k=5 is 75? Let's check:\n\n(k +0.5)^3=5.5³=166.375, floor=166\n\n(k -0.5)^3=4.5³=91.125, floor=91\n\n166 -91=75. So the numbers n from 92 to 166. 166 -92 +1=75. Correct. Then for k=5, the number of terms is 75=3*25.\n\nFor k=6:\n\n(k +0.5)=6.5³=274.625, fractional part 0.625\n\n(k -0.5)=5.5³=166.375, fractional part 0.375\n\nc -d=0.625 -0.375=0.25\n\nSo m -n=3*36 +0.25 -0.25=108. So floor(274.625)=274, floor(166.375)=166. 274 -166=108. So n from 167 to 274. 274-167 +1=108. Correct.\n\nFor k=7:\n\n7.5³=421.875, fractional part 0.875\n\n6.5³=274.625, fractional part 0.625\n\nc -d=0.875 -0.625=0.25\n\nm -n=3*49 +0.25 -0.25=147. So 421 -274=147. So n from 275 to 421. 421-275 +1=147. Correct.\n\nk=8:\n\n8.5³=614.125, fractional 0.125\n\n7.5³=421.875, fractional 0.875\n\nc -d=0.125 -0.875= -0.75\n\nSo m -n=3*64 +0.25 -(-0.75)=192 +0.25 +0.75=193. So floor(614.125)=614, floor(421.875)=421. 614 -421=193. Let's check the number of terms: n from 422 to 614. 614 -422 +1=193. Correct.\n\nSo it seems that for k ≡1 mod 4 (k=1,5,9,...), maybe c -d=0.25, but for k=4,8,... c -d=-0.75. Wait, but 1,5,9,...? Let's check k=9:\n\nk=9:\n\n9.5³=857.375, fractional 0.375\n\n8.5³=614.125, fractional 0.125\n\nc -d=0.375 -0.125=0.25\n\nm -n=3*81 +0.25 -0.25=243. 857 -614=243. Correct. So for k=9, the number of terms is 243.\n\nSimilarly, for k=10:\n\n10.5³=1157.625, fractional 0.625\n\n9.5³=857.375, fractional 0.375\n\nc -d=0.625 -0.375=0.25\n\nm -n=3*100 +0.25 -0.25=300. 1157 -857=300. Correct.\n\nk=11:\n\n11.5³=1520.875, fractional 0.875\n\n10.5³=1157.625, fractional 0.625\n\nc -d=0.875 -0.625=0.25\n\nm -n=3*121 +0.25 -0.25=363. 1520 -1157=363. Correct.\n\nk=12:\n\n12.5³=1953.125, fractional 0.125\n\n11.5³=1520.875, fractional 0.875\n\nc -d=0.125 -0.875= -0.75\n\nm -n=3*144 +0.25 -(-0.75)=432 +0.25 +0.75=433. So floor(1953.125)=1953, floor(1520.875)=1520. 1953 -1520=433. Correct.\n\nSo it seems that for k being a multiple of 4 (k=4,8,12,...), the difference c -d= -0.75, leading to m -n=3k² +1. For other k's, c -d=0.25, leading to m -n=3k². Therefore, the number of terms for each k is:\n\nIf k ≡0 mod 4, then 3k² +1, else 3k².\n\nWait, let's check with k=4: 3*(16)+1=49. Correct. k=8: 3*64 +1=193. Correct. k=12:3*144 +1=433. Correct.\n\nTherefore, the number of terms for k is 3k² if k is not divisible by 4, and 3k² +1 if k is divisible by 4. So this is the pattern. Now, our task is to compute the sum f(n) from n=1 to 2021. Which would be the sum over k of k multiplied by the number of terms where f(n)=k. So we need to find all k such that the intervals (k -0.5)^3 to (k +0.5)^3 include some n <=2021. Then for each such k, multiply k by the number of terms in that interval (either 3k² or 3k² +1) and sum them up, then subtract any excess if the last interval exceeds n=2021.\n\nFirst, we need to find the maximum k such that (k -0.5)^3 <=2021 +0.5^3. Wait, maybe we can find k_max such that (k_max +0.5)^3 <=2021. Wait, but we need the maximum k where there exists some n<=2021 with f(n)=k. So the maximum k is the smallest integer where (k +0.5)^3 >=2021. Wait, no. If n=2021 is in the interval for k, then (k -0.5)^3 <2021 <(k +0.5)^3. Therefore, we need to find k such that (k -0.5)^3 <2021 <(k +0.5)^3.\n\nSo solve for k:\n\n(k -0.5)^3 <2021 <(k +0.5)^3\n\nLet's compute k such that this holds. Let's approximate cube roots.\n\nCube root of 2021 is approximately 12.64, because 12^3=1728, 13^3=2197. So 12.5^3=1953.125, 12.6^3=12^3 + 3*12²*0.6 + 3*12*(0.6)^2 + (0.6)^3=1728 + 3*144*0.6 + 3*12*0.36 +0.216=1728 + 259.2 + 12.96 +0.216=1728+259.2=1987.2+12.96=2000.16+0.216=2000.376. So 12.6³≈2000.376. Then 12.7³=?\n\n12.7³=12^3 +3*12²*0.7 +3*12*(0.7)^2 +0.7³=1728 +3*144*0.7 +3*12*0.49 +0.343=1728 + 302.4 +17.64 +0.343=1728+302.4=2030.4+17.64=2048.04+0.343≈2048.383. So 12.7³≈2048.383.\n\nSince 2021 is between 12.6³ and12.7³. Let's check (k +0.5)=12.7, so k=12.7 -0.5=12.2. No, that's not helpful. Wait, original inequality:\n\n(k -0.5)^3 <2021 <(k +0.5)^3\n\nWe can try k=12. Let's compute (12 +0.5)^3=12.5³=1953.125. But 1953.125 is less than 2021, so (k +0.5)^3 needs to be greater than 2021. So next k=13:\n\n(k +0.5)=13.5. 13.5³=2460.375. (k -0.5)=12.5. 12.5³=1953.125. So 1953.125 <2021 <2460.375. Therefore, k=13 is the integer such that (13 -0.5)^3 <2021 < (13 +0.5)^3. So n=2021 is in the interval for k=13. Therefore, f(2021)=13. Thus, the maximum k needed is13.\n\nTherefore, we need to compute the sum for k from1 to13. However, for k=13, the number of terms might be less than the full 3k² +1 or 3k² because n=2021 is less than (13 +0.5)^3=2460.375, so the number of terms for k=13 is from ceil((13 -0.5)^3) to 2021.\n\nFirst, let's compute the number of terms for each k from1 to12, using the formula 3k² if k not divisible by4, and 3k² +1 if k divisible by4. Then for k=13, compute how many terms are there up to n=2021.\n\nFor k=1 to12:\n\nk=1: 3*1²=3 terms (n=1-3)\nk=2:3*4=12 terms (n=4-15)\nk=3:3*9=27 terms (n=16-42)\nk=4:3*16 +1=49 terms (n=43-91)\nk=5:3*25=75 terms (n=92-166)\nk=6:3*36=108 terms (n=167-274)\nk=7:3*49=147 terms (n=275-421)\nk=8:3*64 +1=193 terms (n=422-614)\nk=9:3*81=243 terms (n=615-857)\nk=10:3*100=300 terms (n=858-1157)\nk=11:3*121=363 terms (n=1158-1520)\nk=12:3*144 +1=433 terms (n=1521-1953)\nWait, let's check these ranges.\n\nWait for k=4: we have n=43-91. The previous k=3: n=16-42. So next is k=4, starting at 43. The number of terms is 49. 43 +49 -1=91. Correct.\n\nSimilarly, k=8: starting from 422 (previous k=7 ended at 421). Number of terms=193. 422 +193 -1=614. Correct.\n\nk=12: starts at 1521 (since k=11 ends at1520). 3*144 +1=433. 1521 +433 -1=1953. Correct.\n\nNow, k=13. The interval is from (13 -0.5)^3=12.5³=1953.125 to (13 +0.5)^3=13.5³=2460.375. Therefore, the integers n start at 1954 (ceil(1953.125)=1954) to floor(2460.375)=2460. But we need only up to n=2021. So the number of terms for k=13 is 2021 -1954 +1=68 terms. So for k=13, contribution to the sum is13*68.\n\nNow, let's compute the total sum:\n\nSum = sum_{k=1 to12} k*(3k² + delta_k) +13*68, where delta_k is1 if k divisible by4, else0.\n\nCompute each term:\n\nk=1: 1*(3*1 +0)=3\nk=2:2*(3*4)=2*12=24\nk=3:3*(3*9)=3*27=81\nk=4:4*(3*16 +1)=4*(48 +1)=4*49=196\nk=5:5*(3*25)=5*75=375\nk=6:6*(3*36)=6*108=648\nk=7:7*(3*49)=7*147=1029\nk=8:8*(3*64 +1)=8*(192 +1)=8*193=1544\nk=9:9*(3*81)=9*243=2187\nk=10:10*(3*100)=10*300=3000\nk=11:11*(3*121)=11*363=3993\nk=12:12*(3*144 +1)=12*(432 +1)=12*433=5196\nk=13:13*68=884\n\nNow sum these up step by step:\n\nLet me list all the terms:\n\n1: 3\n\nSum after k=1: 3\n\nk=2: 24 → sum: 3+24=27\n\nk=3:81 → sum:27+81=108\n\nk=4:196 →108+196=304\n\nk=5:375 →304+375=679\n\nk=6:648 →679+648=1327\n\nk=7:1029 →1327+1029=2356\n\nk=8:1544 →2356+1544=3900\n\nk=9:2187 →3900+2187=6087\n\nk=10:3000 →6087+3000=9087\n\nk=11:3993 →9087+3993=13080\n\nk=12:5196 →13080+5196=18276\n\nk=13:884 →18276+884=19160\n\nSo total sum is19160.\n\nBut wait, let's verify these calculations step by step because it's easy to make arithmetic errors.\n\nLet me recheck:\n\nk=1:3\n\nk=2:24 → 3+24=27\n\nk=3:81 →27+81=108\n\nk=4:196 →108+196=304\n\nk=5:375 →304+375=679 (correct)\n\nk=6:648 →679+648=1327 (648+600=1248, +79=1327)\n\nk=7:1029 →1327+1029. 1327+1000=2327, +29=2356. Correct.\n\nk=8:1544 →2356+1544. 2000+1500=3500. 356+44=400. So total 3500+400=3900. Correct.\n\nk=9:2187 →3900+2187=6087. Yes.\n\nk=10:3000 →6087+3000=9087. Correct.\n\nk=11:3993 →9087+3993. Let's compute 9000+3000=12000, 87+993=1080. Total 13080. Correct.\n\nk=12:5196 →13080+5196. 13000+5000=18000, 80+196=276. 18000+276=18276. Correct.\n\nk=13:884 →18276+884=19160. 18276+800=19076, +84=19160. Correct.\n\nSo total sum is19,160. Now, we need to compute 19160 mod 1000. Which is the last three digits of 19160. 19160 divided by1000 is19 with remainder160. So the remainder is160.\n\nBut let me check if the counts for each k are correct.\n\nFor k=4: 49 terms. 4*49=196. Correct.\n\nk=8:193 terms.8*193=1544. 193*8=1504 + 7*8=56=1504+56=1560. Wait, no. Wait 193*8: 200*8=1600, minus7*8=56, so1600-56=1544. Correct.\n\nk=12:433 terms.12*433= (10*433)+(2*433)=4330 +866=5196. Correct.\n\nk=13:68 terms.13*68=884. 10*68=680, 3*68=204; 680+204=884. Correct.\n\nSo the total sum is19160. Mod1000:19160-19*1000=19160-19000=160. So the remainder is160.\n\nBut wait, I need to confirm the number of terms for k=13. Because the upper bound for k=13 is (13 +0.5)^3=2460.375. But we are summing only up to n=2021. So the number of terms is2021 -1953=68, but wait:\n\nWait, the starting point for k=13 is ceil((13 -0.5)^3)=ceil(12.5³)=ceil(1953.125)=1954. So the terms are from1954 to2021. So the number of terms is2021 -1954 +1=68. Correct. So 68 terms. Hence, 13*68=884. Correct.\n\nTherefore, the total sum is19160, which mod1000 is160.\n\nSo the answer is160. Wait, but let me verify with another approach.\n\nAlternatively, maybe there's a miscalculation in the counts for some k. Let's check the count for k=12.\n\nk=12:3k² +1=3*144 +1=433. The interval is from1521 to1953. 1953-1521 +1=433. Correct. So sum for k=12 is12*433=5196. Correct.\n\nAnd for k=13,1954 to2021:68 terms. So, correct.\n\nThus, the final answer should be160. Therefore, the remainder is160.\n\n**Final Answer**\n\\boxed{160}\n</think>To solve the problem, we need to determine the sum of \\( f(n) \\) from \\( n = 1 \\) to \\( 2021 \\), where \\( f(n) \\) is the nearest integer to the cube root of \\( n \\). We then find the remainder when this sum is divided by 1000.\n\n### Step-by-Step Solution:\n\n1. **Understanding \\( f(n) \\)**:\n   - \\( f(n) = k \\) if \\( |k - \\sqrt[3]{n}| < \\frac{1}{2} \\).\n   - This means \\( k \\) is the integer closest to \\( \\sqrt[3]{n} \\).\n\n2. **Range of \\( n \\) for each \\( k \\)**:\n   - For each integer \\( k \\), the values of \\( n \\) such that \\( f(n) = k \\) lie in the interval \\( ( (k - 0.5)^3, (k + 0.5)^3 ) \\).\n   - The number of integers \\( n \\) in this interval is \\( \\lfloor (k + 0.5)^3 \\rfloor - \\lceil (k - 0.5)^3 \\rceil + 1 \\).\n\n3. **Number of terms for each \\( k \\)**:\n   - For \\( k \\) not divisible by 4, the number of terms is \\( 3k^2 \\).\n   - For \\( k \\) divisible by 4, the number of terms is \\( 3k^2 + 1 \\).\n\n4. **Maximum \\( k \\) for \\( n \\leq 2021 \\)**:\n   - The maximum \\( k \\) is found by solving \\( (k - 0.5)^3 < 2021 < (k + 0.5)^3 \\). This gives \\( k = 13 \\).\n\n5. **Calculating contributions for each \\( k \\)**:\n   - For \\( k = 1 \\) to \\( 12 \\), calculate the sum using the number of terms formula.\n   - For \\( k = 13 \\), calculate the number of terms from 1954 to 2021, which is 68 terms.\n\n6. **Summing contributions**:\n   - Contributions for \\( k = 1 \\) to \\( 12 \\):\n     \\[\n     \\begin{aligned}\n     1 \\times 3 &= 3 \\\\\n     2 \\times 12 &= 24 \\\\\n     3 \\times 27 &= 81 \\\\\n     4 \\times 49 &= 196 \\\\\n     5 \\times 75 &= 375 \\\\\n     6 \\times 108 &= 648 \\\\\n     7 \\times 147 &= 1029 \\\\\n     8 \\times 193 &= 1544 \\\\\n     9 \\times 243 &= 2187 \\\\\n     10 \\times 300 &= 3000 \\\\\n     11 \\times 363 &= 3993 \\\\\n     12 \\times 433 &= 5196 \\\\\n     \\end{aligned}\n     \\]\n   - Contribution for \\( k = 13 \\):\n     \\[\n     13 \\times 68 = 884\n     \\]\n\n7. **Total sum and remainder**:\n   - Total sum:\n     \\[\n     3 + 24 + 81 + 196 + 375 + 648 + 1029 + 1544 + 2187 + 3000 + 3993 + 5196 + 884 = 19160\n     \\]\n   - Remainder when divided by 1000:\n     \\[\n     19160 \\mod 1000 = 160\n     \\]\n\nThe final answer is \\(\\boxed{160}\\)."}