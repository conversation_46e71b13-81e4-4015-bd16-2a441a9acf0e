{"row_number": 6487, "expected_answer": "\\( (0,0,0,0) \\)", "problem": "Find the integral solutions of the equation \\(x^2 + y^2 + z^2 + u^2 = 2xyzu\\).", "generated_solution": "<think>\nOkay, so I need to find all integral solutions to the equation \\(x^2 + y^2 + z^2 + u^2 = 2xyzu\\). Hmm, let's start by understanding what this equation is saying. It's a Diophantine equation because we're looking for integer solutions. The left side is the sum of four squares, and the right side is twice the product of all four variables.\n\nFirst, I know that squares are always non-negative, so the left side is at least 0. The right side is 2 times the product of x, y, z, u. Since the product can be positive or negative depending on the number of negative integers, but when multiplied by 2, it's still positive if there are even negative numbers and negative otherwise. However, the left side is the sum of squares which is non-negative. Therefore, the right side must also be non-negative. So, the product xyzu must be non-negative. That means either all variables are non-negative, or exactly two or four of them are negative. But since squares are the same for a number and its negative, maybe we can assume without loss of generality that all variables are non-negative. Let me check that.\n\nIf (x, y, z, u) is a solution, then so is (-x, -y, -z, -u), but since we can just take absolute values, maybe we can restrict to non-negative integers first and then include the sign combinations that make the product non-negative. So perhaps first consider x, y, z, u ≥ 0, find those solutions, and then consider possible sign changes. But let's see.\n\nSo let's assume x, y, z, u are non-negative integers. Then the equation becomes \\(x^2 + y^2 + z^2 + u^2 = 2xyzu\\). Let's try small integers and see if any solutions pop up. Let's start with zeros.\n\nIf x = 0, then the left side becomes 0 + y² + z² + u², and the right side becomes 0. So 0 + y² + z² + u² = 0. The only solution here is y = z = u = 0. So (0,0,0,0) is a solution. Let's note that.\n\nSo (0,0,0,0) is a solution. Are there others?\n\nNow, suppose one of the variables is zero. Let's say x=0. Then we have y² + z² + u² = 0, which implies y=z=u=0. So the only solution with a zero is all zeros. So if any variable is zero, all must be zero. So non-trivial solutions (if any) must have all variables non-zero.\n\nSo now, assume x, y, z, u ≥ 1. Let's see. Let me try small numbers. Let's try x=y=z=u=1. Then left side: 1+1+1+1=4. Right side: 2*1*1*1*1=2. So 4 ≠ 2, so not a solution.\n\nWhat if x=y=z=1 and u= something. Let's suppose three variables are 1, and solve for the fourth. Let's set x=y=z=1. Then the equation becomes 1+1+1+u² = 2*1*1*1*u => 3 + u² = 2u. Rearranged: u² - 2u +3 =0. The discriminant is 4 - 12 = -8 <0, so no real solutions, so no integer solutions.\n\nWhat if two variables are 1? Let's say x=y=1. Then equation becomes 1 +1 + z² + u² = 2*1*1*z*u => 2 + z² + u² = 2zu. Rearranged: z² - 2zu + u² + 2 = 0. Wait, z² - 2zu + u² is (z - u)^2, so we have (z - u)^2 + 2 = 0. But squares are non-negative, so (z - u)^2 ≥0, so left side is at least 2, which can't equal zero. So no solutions here.\n\nWhat if one variable is 1? Let's set x=1, and others ≥1. Then the equation is 1 + y² + z² + u² = 2*1*y*z*u. Let's see. Rearranged: y² + z² + u² - 2yzu = -1. Hmm, since y,z,u ≥1, let's see. For y,z,u ≥1, the left side y² + z² + u² is at least 3, and 2yzu is at least 2. So 3 - 2*1*1*1=1, so the left side minus 2yzu would be 1 in the minimal case, which is greater than -1. So if we need y² + z² + u² - 2yzu = -1, but y,z,u ≥1. Let's check if y=1. Let's set y=1. Then equation becomes 1 + z² + u² - 2*1*z*u = -1 => z² + u² - 2zu +1 +1 = -1 => (z - u)^2 +2 = -1, which is impossible. Similarly, even if two variables are 1, it doesn't work.\n\nTherefore, perhaps if we have x, y, z, u all ≥2. Let's see. Let's try with x=y=z=2. Then left side: 4 +4 +4 +u²=12 + u². Right side: 2*2*2*2*u=16u. So equation: 12 + u² =16u => u² -16u +12=0. Discriminant: 256 - 48=208. sqrt(208)=~14.4. So u=(16 ±14.4)/2. So u≈15.2 or 0.8. Not integers, so no solution here.\n\nAlternatively, try x=1, y=2. Then equation becomes 1 +4 + z² + u²=2*1*2*z*u =>5 + z² + u²=4zu. Rearranged: z² -4zu + u² +5=0. Again, z² -4zu +u² = (z - 2u)^2 -3u². Hmm, not sure. Let's compute discriminant for z if we fix u. Let me treat this as quadratic in z: z² -4u z + (u² +5)=0. Discriminant: 16u² -4*1*(u² +5)=16u² -4u² -20=12u² -20. To have integer solutions, discriminant must be a perfect square. So 12u² -20 =k². Let's see for u=2: 12*4 -20=48-20=28. 28 is not a square. u=3: 12*9 -20=108-20=88, not square. u=1: 12 -20= -8, not square. So no solution here.\n\nAlternatively, maybe set two variables to 2 and others different. Let's see. Suppose x=2, y=2, z=2. Then equation becomes 4+4+4 + u²=2*2*2*2*u =>12 + u²=16u. Same as before, leading to u² -16u +12=0, which as we saw has no integer solutions.\n\nWhat if x=2, y=2, z=3. Then left side: 4+4+9 +u²=17 + u². Right side:2*2*2*3*u=24u. So equation: 17 +u²=24u. Then u² -24u +17=0. Discriminant: 576 -68=508. sqrt(508)≈22.5, not integer. No solution.\n\nThis approach might not be efficient. Let's think of another way. Maybe parity. Let's check if all variables must be even or something.\n\nSuppose all variables are even. Let x=2a, y=2b, z=2c, u=2d. Then equation becomes 4a² +4b² +4c² +4d²=2*(2a)(2b)(2c)(2d)=2*16abcd=32abcd. Dividing both sides by 4: a² + b² + c² + d²=8abcd. So similar equation but with variables a,b,c,d. Not sure if helpful.\n\nAlternatively, maybe some variables are even, some are odd. Let's check parity. If all variables are even, as above. If some variables are odd:\n\nSuppose x is odd. Then x² ≡1 mod 4. Similarly for y, z, u. The left side is the sum of four squares. If all variables are odd, then the sum is 1+1+1+1=4 ≡0 mod4. The right side is 2xyzu. If all variables are odd, then xyzu is odd, so 2xyzu ≡2 mod4. But 4 ≡0 mod4 ≠ 2 mod4. So that's a contradiction. Therefore, if all variables are odd, the left side is 0 mod4, the right side is 2 mod4, which can't be. So at least one variable must be even.\n\nSimilarly, if three variables are odd, sum of squares would be 1+1+1 + (even)^2. The even square is 0 mod4, so sum is 3 +0=3 mod4. The right side: product of three odds and one even is even, so 2*(even)=0 mod4. 3≡3≠0 mod4. Contradiction.\n\nIf two variables are odd, then sum of squares is 1+1 +0+0=2 mod4. Right side: product of two odds and two evens: even, so 2*even=0 mod4. 2≠0 mod4. Again, contradiction.\n\nIf one variable is odd, sum of squares is 1 +0+0+0=1 mod4. Right side: 2*(even*even*even*odd)=2*(even)=0 mod4. 1≠0 mod4. Contradiction.\n\nOnly case where left side ≡ right side mod4 is when all variables are even, which gives left side 0 mod4 and right side 0 mod4. So all variables must be even. Therefore, the only possible solutions are when x,y,z,u are all even. Therefore, the solution (0,0,0,0) is trivial. But are there others?\n\nSuppose all variables are even. Let’s set x=2a, y=2b, z=2c, u=2d. Then the equation becomes:\n\n(2a)^2 + (2b)^2 + (2c)^2 + (2d)^2 = 2*(2a)(2b)(2c)(2d)\n\nWhich simplifies to:\n\n4a² +4b² +4c² +4d² = 32abcd\n\nDivide both sides by 4:\n\na² + b² + c² + d² = 8abcd\n\nSo now we have the same equation but with a,b,c,d. So if the original equation has a non-trivial solution (all variables even but not all zero), then this new equation must have a solution in positive integers. However, if we can apply the same reasoning again. Suppose a,b,c,d are even. Then set a=2a', etc. Then:\n\n(2a')² + ... = 8*(2a')(2b')(2c')(2d')\n\nWhich would lead to:\n\n4a'² + ... = 8*(16a'b'c'd') => a'² + b'² + c'² + d'² = 32a'b'c'd'\n\nContinuing this, each time you factor out 2s, the right side gets multiplied by 8 each time (since 8* previous coefficient). However, unless all variables are zero, this process would have to terminate at some point. The only way this can happen is if all variables are zero. So by infinite descent, the only solution is the trivial solution (0,0,0,0). Therefore, the only integral solution is all zeros.\n\nWait, but let me check this argument again. Suppose there exists a non-trivial solution with all variables even. Then, by dividing each by 2, we get another solution. But unless that solution is also all even, we can't continue. Wait, but in our case, we derived that all variables must be even, so this process can be repeated indefinitely unless all variables are zero. Therefore, by infinite descent, there are no non-trivial solutions.\n\nThus, the only integral solution is (0,0,0,0).\n\nBut let me verify this with an example. Let's suppose that there's a solution with x,y,z,u all even. Let's say x=2a, etc. Then the equation reduces to a² + b² + c² + d²=8abcd. If we suppose a,b,c,d are all even, then we can keep dividing by 2 indefinitely, which would imply that each variable is divisible by 2 infinitely many times, hence must be zero. Therefore, the only solution possible is all zeros.\n\nTherefore, the only integral solution is (0,0,0,0).\n\nBut wait, hold on. What if we consider that variables can be zero. But earlier, we saw that if any variable is zero, all must be zero. So combining both arguments, the only solution is (0,0,0,0). So that's the answer.\n\nBut let me check with another approach. Maybe suppose that variables are positive integers. Let's assume x,y,z,u are positive integers. Then, using AM ≥ GM. The sum of squares x² + y² + z² + u² ≥ 4*(xyzu)^{1/2} by AM-GM. So, 4*(xyzu)^{1/2} ≤ 2xyzu. Divide both sides by 2: 2*(xyzu)^{1/2} ≤ xyzu. Let t = (xyzu)^{1/2}. Then 2t ≤ t² => t² - 2t ≥0 => t(t - 2) ≥0. Since t is positive (because variables are positive), this implies t ≥2. So (xyzu)^{1/2} ≥2 => xyzu ≥4.\n\nBut then, equality holds in AM-GM only when all terms are equal. So if x² = y² = z² = u², so x=y=z=u. Let’s suppose x=y=z=u=k. Then the equation becomes 4k² = 2k^4 => 2k² =k^4 =>k^4 -2k²=0 =>k²(k² -2)=0. Since k is a positive integer, k² -2=0 implies k=√2, which is not integer. Therefore, equality cannot hold in AM-GM here, so the inequality is strict. So 4*(xyzu)^{1/2} < x² + y² + z² + u² =2xyzu. Therefore, 4*(xyzu)^{1/2} <2xyzu => divide both sides by 2: 2*(xyzu)^{1/2} < xyzu. Let t=(xyzu)^{1/2}, then 2t < t² => t² -2t >0 => t(t -2)>0 => t>2. So xyzu >4. Therefore, in any case, if x,y,z,u are positive integers, their product is at least 4, but even more, since t>2.\n\nBut let's see if possible. Suppose variables are positive integers and product is ≥4. Let me take x ≤ y ≤ z ≤ u to make it easier.\n\nSuppose x=1. Then, as before, equation becomes 1 + y² + z² + u² =2yzu. Let's see. The right side is even. The left side is 1 + ... which is odd + even. Let's check if left side is even. y² + z² + u² must be odd. So among y, z, u, there are an odd number of odd numbers. So either one or three of y,z,u are odd. Let's try small y.\n\nIf x=1, y=1. Then equation becomes 1 +1 + z² + u²=2*1*1*z*u => 2 + z² + u²=2zu. Rearranged: z² -2zu +u² +2=0. As before, which is (z -u)^2 +2=0, impossible.\n\nIf x=1, y=2. Then equation is 1 +4 + z² +u²=2*1*2*z*u =>5 + z² +u²=4zu. Then z² -4zu +u² +5=0. Let's see for small z. Let z=2: 4 -8u +u² +5= u² -8u +9=0. Discriminant 64 -36=28. Not square. z=3: 9 -12u +u² +5= u² -12u +14=0. Discriminant 144 -56=88. Not square. z=4: 16 -16u +u² +5= u² -16u +21=0. Discriminant 256 -84=172. Not square. z=1: 1 -4u +u² +5= u² -4u +6=0. Discriminant 16 -24= -8. No solution.\n\nx=1, y=3. Equation: 1 +9 + z² +u²=2*1*3*z*u =>10 + z² +u²=6zu. Rearranged: z² -6zu +u² +10=0. Again, treating as quadratic in z: z² -6u z + (u² +10)=0. Discriminant: 36u² -4(u² +10)=32u² -40. This must be a perfect square. Let’s set 32u² -40 =k². So k²=32u² -40. Let’s see if u=2: 32*4 -40=128-40=88, not square. u=3:32*9 -40=288-40=248, not square. u=4:32*16 -40=512-40=472, not square. Doesn't seem to work.\n\nAlternatively, maybe x=1, y=4. Then equation:1+16 + z² +u²=2*1*4*z*u =>17 + z² +u²=8zu. So z² -8zu +u² +17=0. Again, discriminant as quadratic in z:64u² -4(u² +17)=60u² -68. Set equal to k². 60u² -68=k². For u=2: 240 -68=172, not square. u=3:540 -68=472, not square. Not helpful.\n\nMaybe x=2, y=2. Then equation becomes 4+4 +z² +u²=2*2*2*z*u =>8 + z² +u²=8zu. Rearranged:z² -8zu +u² +8=0. Let's set this as quadratic in z: z² -8u z + (u² +8)=0. Discriminant:64u² -4(u² +8)=60u² -32. Set to k²:60u² -32=k². Trying u=1:60 -32=28, not square. u=2:240 -32=208, not square. u=3:540 -32=508, not square.\n\nAlternatively, trying x=2, y=3. Equation:4 +9 +z² +u²=2*2*3*z*u =>13 + z² +u²=12zu. Rearranged:z² -12zu +u² +13=0. Discriminant:144u² -4(u² +13)=140u² -52. Set to k². For u=1:140 -52=88. u=2:560 -52=508. Not squares.\n\nThis seems going nowhere. Maybe there's another approach. Let's think modulo something else. Let's see.\n\nSuppose variables are positive integers. Then 2xyzu = x² + y² + z² + u². Since the right side is positive, each variable must be positive. Now, consider the equation modulo 3.\n\nSquares modulo 3 are 0 or 1. Let’s consider possible values. The left side is sum of four squares, so modulo 3, possible values are 0,1,2,3,4 ≡0,1,2,0,1 mod3. The right side is 2xyzu. The product xyzu can be 0 or 1 or 2 mod3. So 2xyzu mod3 can be 0,2,1. So let's see possible combinations.\n\nIf left side is 0 mod3: Then right side must be 0 mod3. So xyzu ≡0 mod3. Therefore, at least one variable is divisible by3.\n\nIf left side is 1 mod3: Right side must be 1 mod3, so xyzu ≡2 mod3. But xyzu ≡2 mod3 implies that the product is 2 mod3. Since in modulo3, 2 is equivalent to -1. So product of variables ≡-1 mod3. The product of four numbers. Let’s see. If all variables ≡1 mod3: product is 1. If three variables ≡1 and one ≡-1: product ≡-1. If two variables ≡1 and two≡-1: product≡1. If one variable ≡1 and three≡-1: product≡-1. If variables ≡0: product≡0. So to get product≡-1 mod3, there must be an odd number of variables≡-1 mod3 and the rest≡1, and none≡0. So in such a case, all variables must be ≡1 or -1 mod3, with an odd number of ≡-1.\n\nSimilarly, if left side is 2 mod3: then right side must be 2 mod3. So xyzu ≡1 mod3. Then product of four variables ≡1 mod3. This requires even number of variables≡-1 mod3, rest≡1.\n\nBut maybe this is getting too complicated. Let's instead consider that if there is a solution with x,y,z,u ≥1, then since we have 2xyzu = sum of squares. Each variable divides the sum of squares. For example, x divides y² + z² + u². Since x divides the left side minus x². Wait, actually, from the equation: x² = 2xyzu - (y² + z² + u²). So x divides (y² + z² + u²). Similarly for y, z, u. So each variable divides the sum of the squares of the other three.\n\nSo let's suppose x ≤ y ≤ z ≤ u. Then x ≥1, and x divides y² + z² + u². Since y,z,u ≥x, then y² + z² + u² ≥3x². So 3x² ≤ y² + z² + u². But also, from the equation: x² + y² + z² + u² =2xyzu. Therefore, 2xyzu =x² + y² + z² + u² ≥x² +3x²=4x². Thus, 2xyzu ≥4x² => yzu ≥2x. Since y ≥x, z ≥x, u ≥x, then yzu ≥x*x*x=x³. Thus, x³ ≤ yzu ≤ yzu. But we have yzu ≥2x. Combining, x³ ≤ yzu ≥2x =>x³ ≤ yzu. But also, since y,z,u ≥x, yzu ≥x^3. So yzu ≥x^3. But from 2xyzu = x² + y² + z² + u² ≥ x² + 3x² =4x², so 2xyzu ≥4x² => yzu ≥2x. So combining with yzu ≥x³, we have x³ ≤ yzu ≥2x. Thus, x³ ≤2x =>x² ≤2. Since x is a positive integer, x=1.\n\nTherefore, x must be 1. So x=1. Then we can substitute into the equation:1 + y² + z² + u²=2yzu. So 2yzu =1 + y² + z² + u².\n\nNow, repeating the same reasoning for y. Assuming y ≤ z ≤ u.\n\nSimilarly, y divides 1 + z² + u². Let's see. Since y divides 1 + z² + u². Let’s try small y.\n\ny=1: Then equation becomes 1 +1 + z² + u²=2*1*z*u =>2 + z² + u²=2zu. Rearranged: z² -2zu +u² +2=0. As before, which is (z -u)^2 +2=0, impossible.\n\ny=2: Then equation is 1 +4 + z² +u²=2*2*z*u =>5 + z² +u²=4zu. Which we've tried before. Let's check possible z and u.\n\nAgain, z ≥2. Let’s try z=2. Then equation:5 +4 +u²=8u =>9 +u²=8u =>u² -8u +9=0. Discriminant 64 -36=28. Not a square.\n\nz=3:5 +9 +u²=12u =>14 +u²=12u =>u² -12u +14=0. Discriminant 144 -56=88. Not square.\n\nz=4:5 +16 +u²=16u =>21 +u²=16u =>u² -16u +21=0. Discriminant 256 -84=172. Not square.\n\nz=5:5 +25 +u²=20u =>30 +u²=20u =>u² -20u +30=0. Discriminant 400 -120=280. Not square.\n\nSimilarly, no solutions here.\n\ny=3: Equation becomes1 +9 +z² +u²=2*3*z*u =>10 + z² +u²=6zu. Rearranged:z² -6zu +u² +10=0. Again, discriminant for z:36u² -4(u² +10)=32u² -40. Set to k². Trying u=2: 128 -40=88, not square. u=3:288 -40=248, not square.\n\ny=4:1 +16 +z² +u²=2*4*z*u =>17 +z² +u²=8zu. Which is similar to earlier attempts. Doesn't yield solutions.\n\nSo maybe there are no solutions with x=1. Which would suggest that the only solution is x=y=z=u=0.\n\nWait, but how does this fit with our previous parity argument? If all variables are even, leading to infinite descent, but here with x=1, which is odd, but we couldn't find solutions. So this further supports the conclusion that the only solution is (0,0,0,0).\n\nThus, after analyzing via multiple methods: parity mod4, infinite descent, trying small variables, and modular arithmetic, it seems the only integral solution is all zeros. Therefore, the answer is (0,0,0,0). However, since the question allows integral solutions, we should also consider negative integers. But since squares are the same for negatives, and the product xyzu must be non-negative, so the number of negative variables must be even. But since (0,0,0,0) is the same regardless of signs. Therefore, the only integral solution is (0,0,0,0).\n\n**Final Answer**\nThe only integral solution is \\boxed{(0, 0, 0, 0)}.\n</think>To find the integral solutions of the equation \\(x^2 + y^2 + z^2 + u^2 = 2xyzu\\), we will proceed as follows:\n\n1. **Trivial Solution**: First, consider the case where any of the variables is zero. If any variable is zero, say \\(x = 0\\), then the equation simplifies to \\(y^2 + z^2 + u^2 = 0\\). Since squares are non-negative, this implies \\(y = z = u = 0\\). Therefore, \\((0, 0, 0, 0)\\) is a solution.\n\n2. **Non-Trivial Solutions**: Assume \\(x, y, z, u \\geq 1\\). We will use the Arithmetic Mean-Geometric Mean (AM-GM) inequality to derive a contradiction:\n   \\[\n   x^2 + y^2 + z^2 + u^2 \\geq 4\\sqrt[4]{x^2 y^2 z^2 u^2} = 4\\sqrt{xyzu}.\n   \\]\n   The given equation is \\(x^2 + y^2 + z^2 + u^2 = 2xyzu\\). Thus,\n   \\[\n   4\\sqrt{xyzu} \\leq 2xyzu.\n   \\]\n   Dividing both sides by 2, we get:\n   \\[\n   2\\sqrt{xyzu} \\leq xyzu.\n   \\]\n   Let \\(t = \\sqrt{xyzu}\\). Then the inequality becomes:\n   \\[\n   2t \\leq t^2 \\implies t^2 - 2t \\geq 0 \\implies t(t - 2) \\geq 0.\n   \\]\n   Since \\(t \\geq 1\\) (as \\(x, y, z, u \\geq 1\\)), the inequality \\(t(t - 2) \\geq 0\\) holds only if \\(t \\geq 2\\). However, equality in the AM-GM inequality holds if and only if \\(x = y = z = u\\). Setting \\(x = y = z = u = k\\), the equation becomes:\n   \\[\n   4k^2 = 2k^4 \\implies 2k^2 = k^4 \\implies k^4 - 2k^2 = 0 \\implies k^2(k^2 - 2) = 0.\n   \\]\n   This implies \\(k = 0\\) or \\(k^2 = 2\\). Since \\(k\\) must be an integer, \\(k = 0\\) is the only solution, which contradicts our assumption that \\(x, y, z, u \\geq 1\\).\n\n3. **Parity Analysis**: Next, consider the equation modulo 4. The left side \\(x^2 + y^2 + z^2 + u^2\\) can be 0, 1, 2, or 3 modulo 4, while the right side \\(2xyzu\\) can be 0 or 2 modulo 4. If all variables are even, we can repeatedly divide by 2, leading to an infinite descent unless all variables are zero. If any variable is odd, the left side modulo 4 does not match the right side, leading to contradictions.\n\n4. **Divisibility and Bounds**: Each variable must divide the sum of the squares of the other three. If \\(x \\leq y \\leq z \\leq u\\), then \\(x\\) divides \\(y^2 + z^2 + u^2\\). Given \\(y, z, u \\geq x\\), we have \\(y^2 + z^2 + u^2 \\geq 3x^2\\). Thus,\n   \\[\n   2xyzu = x^2 + y^2 + z^2 + u^2 \\geq x^2 + 3x^2 = 4x^2 \\implies 2yzu \\geq 4x \\implies yzu \\geq 2x.\n   \\]\n   Since \\(y, z, u \\geq x\\), we get \\(yzu \\geq x^3\\). Combining, we have \\(x^3 \\leq 2x \\implies x^2 \\leq 2 \\implies x = 1\\). Substituting \\(x = 1\\) into the equation, we get:\n   \\[\n   1 + y^2 + z^2 + u^2 = 2yzu.\n   \\]\n   Repeating the same reasoning for \\(y, z, u\\), we find no solutions for \\(y, z, u \\geq 1\\).\n\nThus, the only integral solution is \\((0, 0, 0, 0)\\).\n\n\\[\n\\boxed{(0, 0, 0, 0)}\n\\]"}