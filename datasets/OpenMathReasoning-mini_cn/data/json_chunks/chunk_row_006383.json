{"row_number": 6383, "expected_answer": "175", "problem": "By dividing 100000 by an integer with 3 different digits, <PERSON> obtains a quotient and an integer residue. The quotient is written with the same digits as the divisor, but in reverse order. What is the divisor?", "generated_solution": "<think>\nOkay, so I need to find a three-digit number with all different digits that, when you divide 100,000 by it, the quotient is the reverse of the divisor, and there's some remainder. Hmm, let's try to parse this step by step.\n\nFirst, let's define the divisor as a three-digit number. Let's call it 'abc', where a, b, c are digits and a ≠ 0 (since it's a three-digit number). Also, all three digits must be different. The problem states that when you divide 100,000 by this number, the quotient is the reverse of the divisor, which would be 'cba', and there's a remainder. So mathematically, this can be written as:\n\n100,000 = (abc) * (cba) + remainder\n\nBut the remainder has to be less than the divisor (abc). So, if we rearrange this, the remainder is 100,000 - (abc)*(cba). Since the remainder is an integer and less than abc, that means that (abc)*(cba) must be just less than 100,000. So, (abc)*(cba) < 100,000. Also, since the quotient is cba, which is a three-digit number, the divisor must be such that when you divide 100,000 by it, the quotient is also a three-digit number. Wait, but 100,000 divided by a three-digit number could be a three or four-digit quotient. For example, 100,000 ÷ 100 = 1000, which is a four-digit number, but 100,000 ÷ 999 ≈ 100.1, which would have a three-digit quotient (100) if we take the integer part. But the problem says the quotient is the reverse of the divisor, so maybe the quotient is the exact reverse, not just the integer part? Wait, but division with a remainder: the quotient is the integer part, right?\n\nWait, maybe let's think about division with remainder. When we say 100,000 divided by abc gives quotient cba and remainder r, that means:\n\nabc * cba + r = 100,000, where 0 ≤ r < abc\n\nSo, we can write 100,000 - abc * cba = r, which is less than abc. Therefore, abc * cba must be just less than 100,000, such that the difference is less than abc. So, the product of the divisor and its reverse is just under 100,000.\n\nSo our main equation is:\n\nabc * cba ≈ 100,000\n\nBut with the difference being less than abc.\n\nSo, abc is a three-digit number, cba is also a three-digit number. Let's denote the divisor as N = 100a + 10b + c, and the quotient as M = 100c + 10b + a. Then N * M < 100,000, and 100,000 - N*M < N.\n\nTherefore, N*M is between 100,000 - N and 100,000.\n\nHmm, this seems a bit complex. Maybe we can approximate N*M ≈ 100,000. So N and M are three-digit numbers, reverses of each other. Let's think about possible three-digit numbers where their product is around 100,000.\n\nLet me think about the square root of 100,000. The square root of 100,000 is 316.227..., so numbers around 316 would have squares near 100,000. But here, it's not a square; it's a product of a number and its reverse. So perhaps N is around 317 or so? But N and M are reverses, so if N is, say, 317, then M would be 713. Then 317*713. Let me calculate that. 317*700 = 221,900, and 317*13=4,121, so total is 226,021. That's way more than 100,000. Hmm, that's too big.\n\nWait, maybe I need smaller numbers. If N is 144, reverse is 441. 144*441=63,504. That's way too low. Hmm. Let's try 219 and 912. 219*912. Let's compute that: 200*900=180,000, 200*12=2,400, 19*900=17,100, 19*12=228. So total is 180,000 + 2,400 + 17,100 + 228=199,728. Still under 200,000. Wait, but 100,000 is the target. Hmm, maybe 250 and 052? Wait, 052 is not a three-digit number if leading zeros are not allowed. So the reverse must also be a three-digit number, so the original number cannot end with 0, otherwise the reverse would start with 0, which is invalid. So the divisor cannot have a zero in the units place. So the divisor's last digit (c) can't be 0. Similarly, the first digit (a) can't be 0, and all digits must be different.\n\nSo possible divisors are three-digit numbers with distinct digits, no leading zero, and no trailing zero.\n\nSo, let's think of numbers where N * reverse(N) is just under 100,000, but how?\n\nAlternatively, let's consider that N * M = 100,000 - r, where r is less than N. So N*M is between 100,000 - N and 100,000. Therefore, approximately, N*M ≈ 100,000.\n\nIf we approximate N ≈ M, then each would be around sqrt(100,000) ≈ 316. So maybe N is around 300s. Let's try numbers around 300.\n\nTake 320 and reverse is 023, which is invalid. So divisor can't end with 0. So numbers like 321, reverse is 123. 321*123. Let's compute that. 300*100=30,000, 300*23=6,900, 21*100=2,100, 21*23=483. So total is 30,000 + 6,900 + 2,100 + 483=39,483. That's way too low. Hmm.\n\nWait, maybe numbers in the 400s? Let's try 400. Reverse is 004, invalid. 401: reverse is 104. 401*104=41,000 + 401*4=1,604. Total 41,000+1,604=42,604. Still low.\n\nWait, maybe 500s? 500 is invalid. Let's try 501: reverse is 105. 501*105=52,605. Still low. 600s: 601*106=63,706. Still under 100,000.\n\nWait, maybe numbers where the product is closer to 100,000. Let's try 317*713. Wait, earlier that was 226,021. That's way over. Hmm. Maybe N is smaller. Wait, but if N is 250, reverse is 052 which is invalid. 251: reverse is 152. 251*152. Let's compute: 200*150=30,000, 200*2=400, 51*150=7,650, 51*2=102. Total is 30,000 + 400 + 7,650 + 102=38,152. Still low.\n\nWait, maybe 3-digit numbers where the product is in the 99,000s. Let's think: 99,000 divided by a three-digit number. Let's see, if N*M = 99,000, then if N is 300, M is 330. But M would have to be reverse of N. Wait, 300 reversed is 003, which is invalid. Hmm. Alternatively, let's see if there's a pair of three-digit reverses that multiply to near 100,000.\n\nAlternatively, maybe set up the equation: N = 100a + 10b + c, M = 100c + 10b + a. Then N*M = (100a + 10b + c)(100c + 10b + a). This product needs to be approximately 100,000.\n\nExpanding the product:\n\n= 100a*100c + 100a*10b + 100a*a + 10b*100c + 10b*10b + 10b*a + c*100c + c*10b + c*a\n\nBut that's a bit messy. Let's compute more systematically:\n\n(100a + 10b + c)(100c + 10b + a) =\n100a*100c + 100a*10b + 100a*a + 10b*100c + 10b*10b + 10b*a + c*100c + c*10b + c*a\n\n= 10,000ac + 1,000ab + 100a² + 1,000bc + 100b² + 10ab + 100c² + 10bc + ac\n\nCombine like terms:\n\n= 100a² + (10,000ac + ac) + (1,000ab + 10ab) + (1,000bc + 10bc) + 100b² + 100c²\n\n= 100a² + 10,001ac + 1,010ab + 1,010bc + 100b² + 100c²\n\nHmm, that's quite a complex expression. Maybe instead of expanding, we can look for possible N and M where N*M is just less than 100,000. Let's try to estimate.\n\nSuppose N is a three-digit number, M is its reverse, N*M ≈ 100,000. Let's consider that N and M are both in the 300-400 range. For example, 317 and 713, but their product is too big as before. Wait, 317*713=226,021. That's way over. Hmm. So if the product is around 100,000, perhaps N and M are around 300 and 333? Wait, 300*333=99,900, which is close. But 300 reversed is 003, invalid.\n\nAlternatively, maybe 303 and 303, but digits must be different. So, since the digits a, b, c have to be different, let's think of numbers with distinct digits. Let's try 312, reverse is 213. 312*213. Let's compute:\n\n312*200=62,400\n\n312*13=4,056\n\nTotal: 62,400 + 4,056=66,456. Still too low.\n\nHow about 324*423?\n\n324*400=129,600\n\n324*23=7,452\n\nTotal: 129,600 + 7,452=137,052. Too high.\n\nHmm, maybe 230s? Wait, but divisor can't end with 0. Let's try 231, reverse is 132. 231*132. 200*100=20,000, 200*32=6,400, 31*100=3,100, 31*32=992. So total: 20,000 +6,400=26,400 +3,100=29,500 +992=30,492. Too low.\n\nWait, maybe 340s? 340 is invalid. 341, reverse is 143. 341*143. Let's compute:\n\n300*100=30,000\n\n300*40=12,000\n\n300*3=900\n\n40*100=4,000\n\n40*40=1,600\n\n40*3=120\n\n1*100=100\n\n1*40=40\n\n1*3=3\n\nWait, that's a bad way. Alternatively, 341*143. Let's compute 341*100=34,100; 341*40=13,640; 341*3=1,023. Adding up: 34,100 +13,640=47,740 +1,023=48,763. Still too low.\n\nHmm. Maybe the numbers need to be in the 400s. Let's try 401*104=41,000 + 401*4=1,604, total=42,604. Still low. 420*024 invalid. 412*214. Let's compute 400*200=80,000, 400*14=5,600, 12*200=2,400, 12*14=168. Total: 80,000+5,600=85,600+2,400=88,000+168=88,168. Closer, but still under 100,000. But 88,168 is 11,832 less than 100,000. The remainder would be 100,000 - 88,168=11,832, which is much larger than the divisor 412. Since the remainder must be less than the divisor, this doesn't work. So even though 412*214=88,168, the remainder would be 11,832 which is greater than 412, so that's invalid.\n\nSo maybe the product needs to be closer to 100,000. Let's try 463*364. Compute 463*300=138,900; 463*60=27,780; 463*4=1,852. Total: 138,900+27,780=166,680+1,852=168,532. Way over.\n\nHmm. Maybe numbers where the product is near 100,000. Let's try 250*40=100,000, but 250 reversed is 052 invalid. 256*652. Compute 200*600=120,000; 200*52=10,400; 56*600=33,600; 56*52=2,912. Total: 120,000+10,400=130,400+33,600=164,000+2,912=166,912. Over.\n\nWait, perhaps 319*913. Let's see: 300*900=270,000; 300*13=3,900; 19*900=17,100; 19*13=247. Total: 270k + 3.9k = 273,900 +17,100=291,000 +247=291,247. Way too high.\n\nThis approach is too time-consuming. Maybe we need a better strategy.\n\nLet’s denote the divisor as N = 100a + 10b + c, quotient M = 100c + 10b + a. We have:\n\nN * M = 100,000 - r, where 0 < r < N.\n\nSo, N*M is slightly less than 100,000.\n\nSo, 100,000 - N*M < N → N*M > 100,000 - N\n\nBut since N is a 3-digit number, 100 ≤ N ≤ 999. So, 100,000 - N ≥ 99,001. Therefore, N*M must be between 99,001 and 100,000.\n\nSo we need two 3-digit numbers N and M (where M is reverse of N) such that their product is between 99,001 and 100,000.\n\nSo, the product N*M is in [99,001, 100,000]. Let's try to find such pairs.\n\nGiven that N and M are reverses, let's assume that N is between 300 and 316, since 316^2 ≈ 100,000. Wait, 316*316=99,856. That's close to 100,000. So if N is 316 and M is 613, then 316*613. Let's compute this.\n\n316*600=189,600\n\n316*13=4,108\n\nTotal: 189,600 + 4,108=193,708. That's way over 100,000.\n\nHmm, no. Maybe that approach is wrong. Wait, maybe N is less than 317, but M is larger.\n\nWait, if N is 250 and M is 052 (invalid). Hmm.\n\nWait, 215*512. Let's compute:\n\n200*500=100,000\n\n200*12=2,400\n\n15*500=7,500\n\n15*12=180\n\nTotal: 100,000 +2,400=102,400 +7,500=109,900 +180=110,080. Over.\n\nWait, 199*991= 199*1000 -199*9=199,000 -1,791=197,209. Too high.\n\nWait, perhaps smaller numbers. Let's see, 143*341=143*300 +143*40 +143*1=42,900 +5,720 +143=48,763. Still low.\n\nWait, maybe there's a better approach. Let's consider that N * reverse(N) ≈ 100,000. So N is a three-digit number, reverse(N) is also a three-digit number. Let's denote N as 100a +10b +c, reverse(N) is 100c +10b +a. Their product is (100a +10b +c)(100c +10b +a) ≈ 100,000.\n\nLet me think about the possible leading digits. The product of N and M (reverse) is around 100,000. So, for the leading digits: a and c are the first digits of N and M. The product of a three-digit number and another three-digit number is a six-digit number. Wait, but 100,000 is a six-digit number (but in this case, 100,000 is written as 100000). Wait, 100,000 is 6 digits. But the product N*M is roughly 100,000, so 6 digits. So, when we multiply a three-digit by another three-digit, we get a 5 or 6-digit number. 100,000 is a six-digit number.\n\nSo, N*M= around 100,000, which is 100,000. So the product is a six-digit number starting with 1.\n\nWait, maybe leading digits of N and M multiply to something that contributes to the first digit of the product being 1. So, if N is a three-digit number starting with a, and M starts with c, then a*c must be such that when multiplied, the leading digit is 1. Since a and c are digits from 1 to 9, their product must be 10 or something, but since a and c are integers, the only way the product of a and c contributes to the first digit of the total product being 1 is if a*c is around 10, but since a and c are single digits, the possible pairs where a*c is 10 or 11 or 12? Wait, 9*1=9, 9*2=18, which would lead to different leading digits. Let's see.\n\nWait, actually, when multiplying two three-digit numbers, the leading digit of the product is influenced by the product of the first digits of the two numbers. So, if N starts with a and M starts with c, then the product a*c will contribute to the higher digits of the product. For example, if a=3 and c=3, then 3*3=9, and the product would start with 9 (if there's a carryover from the next digits). But we need the product to be around 100,000, which starts with 1. So, the product a*c should be such that when multiplied (with carries considered), the leading digit is 1. Therefore, a*c must be a number that when combined with the carries from the subsequent digits, results in the first digit of the product being 1. Since 100,000 is 1 followed by five zeros, the product must be between 100,000 and 999,999. Wait, but in our case, the product is around 100,000. So maybe the leading digit of the product is 1, so a*c must be around 10.\n\nBut since a and c are digits from 1 to 9, the possible pairs where a*c is close to 10 would be (2,5)=10, (1,10) invalid, (5,2)=10, etc. So possible pairs where a*c=10 are (2,5) and (5,2). So perhaps the first digit of N is 2 and the first digit of M is 5 (so N ends with 5?), or vice versa.\n\nLet's test this hypothesis. Suppose a=2 and c=5. Then N is 2b5, and M is 5b2. Then N*M ≈ 100,000. Let's compute N*M.\n\nFor example, take N=2b5 and M=5b2. Then the product is (200 + 10b +5)*(500 + 10b +2). Let's expand this:\n\n= (205 +10b)*(502 +10b)\n\n= 205*502 + 205*10b + 10b*502 + 10b*10b\n\n= 102,910 + 2,050b + 5,020b + 100b²\n\n= 102,910 + 7,070b + 100b²\n\nWe need this to be approximately 100,000. So:\n\n100b² + 7,070b + 102,910 ≈ 100,000\n\nSo:\n\n100b² +7,070b +102,910 -100,000 =0\n\n→100b² +7,070b +2,910=0\n\nDivide by 10:\n\n10b² +707b +291=0\n\nBut discriminant D=707² -4*10*291\n\n= 499,849 -11,640=488,209\n\nsqrt(488,209)=698.7 approx. But since b must be a digit (0-9), even if possible, the equation is unlikely to have integer solutions. So maybe this approach is not working.\n\nAlternatively, maybe a=3 and c=3, but 3*3=9, so leading digit would be 9, which is too high.\n\nAlternatively, a=4 and c=2, since 4*2=8. Hmm. Let's try a=3 and c=4. 3*4=12. So product starts with 1 due to carryover. For example, 300*400=120,000, which starts with 1. So if a=3 and c=4, N=3b4, M=4b3. Then product is (300 +10b +4)*(400 +10b +3). Let's compute:\n\n(304 +10b)*(403 +10b)\n\n=304*403 +304*10b +10b*403 +10b*10b\n\n=122,512 +3,040b +4,030b +100b²\n\n=122,512 +7,070b +100b²\n\nSet this ≈100,000:\n\n100b² +7,070b +122,512 =100,000\n\n→100b² +7,070b +22,512=0\n\nDivide by 2:\n\n50b² +3,535b +11,256=0\n\nDiscriminant D= (3,535)^2 -4*50*11,256\n\nCalculate 3,535^2: 3,535*3,535. Let's compute 3,500^2=12,250,000, then 3,535^2 = (3,500 +35)^2 =3,500² +2*3,500*35 +35²=12,250,000 +245,000 +1,225=12,496,225\n\n4*50*11,256=200*11,256=2,251,200\n\nSo D=12,496,225 -2,251,200=10,245,025\n\nsqrt(10,245,025)=3,200.78 (since 3,200^2=10,240,000, and 3,201^2=10,246,401). Not a perfect square. So again, no integer solution. So maybe this is not the case.\n\nAlternatively, maybe a=1 and c=9. 1*9=9, leading digit 9, which would give a product starting with 9, but 9xxxxx which is over 100,000. Wait, 9xxxxx is 900,000 to 999,999, which is way over. So that's not it.\n\nAlternatively, a=1 and c=1. But digits must be different, so no.\n\nAlternatively, a=1 and c=2. 1*2=2. The product would start with 2, which would be 2xxxxx, which is over 200,000. Not possible.\n\nHmm, maybe the leading digits are causing a carryover that makes the product start with 1. For example, if a=9 and c=1, then 9*1=9, but the next digits could carryover and make it 10. Wait, but multiplying 9xx * 1xx would be 9xx*1xx=9xx,xx, which is 9 followed by some digits, so the product would start with 9. But we need a product starting with 1. So maybe there's a case where the multiplication of the first digits plus some carryover equals 10, which would make the first digit 1.\n\nFor example, if a=2 and c=5, the product of the first digits is 10, so 2*5=10. So 2xx * 5xx. The 10 would result in a leading 0 with a carryover of 1, making the first digit 1. But how?\n\nWait, when multiplying two three-digit numbers, the highest term in the product is a*c *10,000. So for example, 200*500=100,000. So if a*c=10, then that term is 10*10,000=100,000. Then, the rest of the product would add to it, so if a*c=10, the product will be 100,000 plus something. But if we can have a*c=10 and the rest of the terms subtract enough to get back to around 100,000.\n\nWait, but a and c are single digits. So a*c=10. The only way is 2*5=10. So the first digit of N is 2 and the last digit is 5, so N is 2b5, reverse is 5b2. So let's take N=2b5, M=5b2. Then N*M= (200 +10b +5)*(500 +10b +2) = (205 +10b)*(502 +10b). As we calculated before, this equals 100b² +7,070b +102,910. We need this product to be approximately 100,000, so:\n\n100b² +7,070b +102,910 ≈100,000 → 100b² +7,070b ≈ -2,910. That can't be. Wait, since left side is positive and right side is negative. So something's wrong here.\n\nWait, maybe I made a miscalculation. Let me check again. If N=2b5 and M=5b2, then N*M = (200 +10b +5)*(500 +10b +2)=205*502 +205*10b +10b*502 +10b*10b. Wait, 205*502= (200*500) + (200*2) + (5*500) + (5*2)=100,000 +400 +2,500 +10=102,910. Then 205*10b=2,050b, 10b*502=5,020b, and 10b*10b=100b². So total=102,910 +7,070b +100b². So, the equation is 100b² +7,070b +102,910 =100,000 +r, but r must be less than N=2b5. So, 100b² +7,070b +102,910 must be between 100,000 - N and 100,000. So:\n\n100,000 - (200 +10b +5) ≤100b² +7,070b +102,910 ≤100,000\n\nSo:\n\n100b² +7,070b +102,910 ≤100,000\n\nBut substituting, 100b² +7,070b +102,910 ≤100,000 → 100b² +7,070b ≤ -2,910. Which is impossible because left side is positive and right side is negative. So this approach can't work.\n\nTherefore, there must be an error in my assumptions. Maybe a*c is not exactly 10, but it's a higher number, and with carryover, the total product starts with 10. Let's think differently. For example, if a=3 and c=4, then a*c=12, but if there is a carryover from the next digit multiplication, the total could be 13, which would start with 1. But this is getting too vague.\n\nAlternatively, perhaps the product is exactly 100,000 - r, where r is the remainder. Since 100,000 divided by N is M with remainder r, then N*M + r =100,000. Since r <N, then N*M >100,000 -N. So N*M is just under 100,000.\n\nAlternatively, maybe N*M is around 99,900 to 100,000. Let's try to find N and M where N is a three-digit number, M is its reverse, and their product is in that range.\n\nLet me try N=  320, reverse=023 invalid. 315 reverse=513. Compute 315*513. Let's compute 300*500=150,000, 300*13=3,900, 15*500=7,500, 15*13=195. Total=150,000+3,900=153,900+7,500=161,400+195=161,595. Over 100k.\n\nHow about N=250, reverse=052 invalid. N=215, reverse=512. Compute 215*512. Let's do 200*500=100,000; 200*12=2,400; 15*500=7,500; 15*12=180. Total=100,000+2,400=102,400+7,500=109,900+180=110,080. Over.\n\nWait, maybe smaller N. Let's try N=198, reverse=891. 198*891. Let's compute:\n\n200*900=180,000\n\nSubtract 2*900=1,800 → 178,200\n\nSubtract 200*9=1,800 → 176,400\n\nAdd (2*9)=18 → 176,418. Still over 100k.\n\nN=175, reverse=571. 175*571. Let's compute 175*500=87,500; 175*70=12,250; 175*1=175. Total=87,500+12,250=99,750+175=99,925. Wow, that's 99,925. Which is 75 less than 100,000. So if N=175, then 100,000 divided by 175 would be quotient=571 with remainder=75. But wait, 175*571=99,925. Then 100,000 -99,925=75. Which is less than N=175. So that fits. And 175 has digits 1,7,5 which are all different. And the quotient is 571, which is the reverse of 175. Because 175 reversed is 571. Yes! So divisor is 175.\n\nWait, let's verify. 175 * 571 = ?\n\nFirst, compute 175*500=87,500\n\n175*70=12,250\n\n175*1=175\n\nAdding them up: 87,500 +12,250=99,750 +175=99,925.\n\nSo 100,000 -99,925=75. 75 <175. So yes, the division 100,000 divided by 175 gives quotient 571 and remainder 75. 571 is the reverse of 175. And 175 has three distinct digits.\n\nTherefore, the divisor is 175.\n</think>To solve the problem, we need to find a three-digit number \\( N \\) with distinct digits such that when 100,000 is divided by \\( N \\), the quotient is the reverse of \\( N \\) and there is an integer remainder \\( r \\) where \\( 0 \\leq r < N \\).\n\nLet's denote the divisor as \\( N = 100a + 10b + c \\) and its reverse as \\( M = 100c + 10b + a \\). The relationship given in the problem is:\n\\[ 100,000 = N \\times M + r \\]\nwhere \\( 0 \\leq r < N \\).\n\nThis implies that \\( N \\times M \\) must be slightly less than 100,000. We need to find \\( N \\) and \\( M \\) such that \\( N \\times M \\approx 100,000 \\).\n\nWe test possible values for \\( N \\) and compute the product \\( N \\times M \\):\n\n1. Let \\( N = 175 \\). Then \\( M = 571 \\).\n2. Compute \\( 175 \\times 571 \\):\n   \\[\n   175 \\times 571 = 175 \\times (500 + 70 + 1) = 175 \\times 500 + 175 \\times 70 + 175 \\times 1\n   \\]\n   \\[\n   175 \\times 500 = 87,500\n   \\]\n   \\[\n   175 \\times 70 = 12,250\n   \\]\n   \\[\n   175 \\times 1 = 175\n   \\]\n   Adding these together:\n   \\[\n   87,500 + 12,250 + 175 = 99,925\n   \\]\n3. The product \\( 175 \\times 571 = 99,925 \\) is very close to 100,000.\n4. The remainder \\( r \\) is:\n   \\[\n   r = 100,000 - 99,925 = 75\n   \\]\n5. Since \\( 75 < 175 \\), the condition \\( 0 \\leq r < N \\) is satisfied.\n\nThus, the divisor \\( N \\) is \\( 175 \\) and the quotient is \\( 571 \\), which is the reverse of \\( 175 \\).\n\nTherefore, the divisor is:\n\\[\n\\boxed{175}\n\\]"}