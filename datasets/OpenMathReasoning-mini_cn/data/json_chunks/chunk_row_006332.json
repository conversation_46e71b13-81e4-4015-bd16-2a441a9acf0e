{"row_number": 6332, "expected_answer": "6", "problem": "What is the smallest positive integer $n$ such that the complex number $(\\sqrt{3}-i)^{n}$ is real and negative?", "generated_solution": "<think>\nOkay, let's try to solve this problem: Find the smallest positive integer n such that the complex number (√3 - i)^n is real and negative. Hmm, okay. So, first, I need to recall how to work with complex numbers and their powers. \n\nI remember that when dealing with complex numbers, it's often useful to represent them in polar form. Because when you raise a complex number to a power, using <PERSON>'s theorem makes things easier. So, maybe I should convert √3 - i into polar form first. Let me think. \n\nPolar form requires the modulus (or magnitude) and the argument (angle). The modulus of a complex number a + bi is √(a² + b²). So, for √3 - i, that would be √[(√3)² + (-1)²] = √(3 + 1) = √4 = 2. Okay, the modulus is 2. \n\nNow, the argument. The argument θ is the angle made with the positive real axis. Since the complex number is √3 - i, which is in the fourth quadrant (real part positive, imaginary part negative), the argument will be negative or, equivalently, measured clockwise from the positive real axis. Alternatively, I can represent it as a positive angle by subtracting from 2π. \n\nThe reference angle can be found by taking arctangent of |b/a|. Here, a = √3 and b = -1 (but since we're taking absolute value, it'll be 1/√3). So, arctan(1/√3) is π/6. Therefore, since it's in the fourth quadrant, the argument θ is -π/6 or 11π/6. Depending on how we want to represent it. \n\nSo, in polar form, √3 - i is 2[cos(-π/6) + i sin(-π/6)] or 2[cos(11π/6) + i sin(11π/6)]. Both are equivalent. Maybe using 11π/6 is better to keep the argument positive between 0 and 2π. \n\nNow, using De Moivre's theorem, (√3 - i)^n would become 2^n [cos(n * θ) + i sin(n * θ)]. So, if we raise it to the nth power, the modulus becomes 2^n, and the argument becomes n * θ. \n\nWe want this result to be a real and negative number. A real and negative number has an argument of π (180 degrees) on the complex plane. So, for the complex number to be real and negative, the angle nθ must be equal to π (mod 2π). Because angles are periodic with period 2π. Also, the imaginary part must be zero, which is achieved when sin(nθ) = 0, so nθ must be an integer multiple of π. But since we want specifically to be negative real, the angle should be π plus any multiple of 2π. \n\nSo, putting this together: nθ ≡ π mod 2π. \n\nSince θ here is 11π/6, substituting that in, we have:\n\nn * (11π/6) ≡ π mod 2π\n\nLet me write that equation:\n\nn * (11π/6) = π + 2πk, where k is an integer.\n\nSimplify both sides by dividing by π:\n\n11n/6 = 1 + 2k\n\nMultiply both sides by 6 to eliminate denominators:\n\n11n = 6 + 12k\n\nSo,\n\n11n = 12k + 6\n\nWe need to solve for the smallest positive integer n such that this equation holds, where k is also an integer. \n\nSo, rearrange for n:\n\nn = (12k + 6)/11\n\nWe need n to be a positive integer, so (12k + 6) must be divisible by 11. Let's write:\n\n12k + 6 ≡ 0 mod 11\n\nWhich means:\n\n12k ≡ -6 mod 11\n\nBut 12 mod 11 is 1, so:\n\n1 * k ≡ -6 mod 11\n\nWhich simplifies to:\n\nk ≡ -6 mod 11\n\nBut -6 mod 11 is the same as 5 mod 11 (since 11 - 6 = 5). So,\n\nk ≡ 5 mod 11\n\nTherefore, the smallest positive integer k satisfying this is k = 5. \n\nPlugging back into the equation for n:\n\nn = (12*5 + 6)/11 = (60 + 6)/11 = 66/11 = 6\n\nWait, so n=6? Let me check that. \n\nWait, if n=6, then let's compute the angle:\n\nnθ = 6*(11π/6) = 11π. But 11π is equivalent to π modulo 2π, since 11π = 5*2π + π. So, 11π ≡ π mod 2π. Therefore, the angle is π, so the complex number would be 2^6 [cos(π) + i sin(π)] = 64*(-1 + 0i) = -64, which is real and negative. So, that works. \n\nBut let me check if there's a smaller n. Since we started with k=5, but maybe I made a mistake there. Let's see. \n\nThe equation was n = (12k +6)/11. We need n to be integer, so 12k +6 must be divisible by 11. Let's test k=0: 12*0 +6 =6; 6/11 is not integer. k=1: 12+6=18; 18/11 isn't integer. k=2: 24+6=30; 30/11≈2.72, not integer. k=3: 36+6=42; 42/11≈3.81, no. k=4: 48+6=54; 54/11≈4.90, nope. k=5: 60+6=66; 66/11=6, which is integer. So yes, n=6 is the first solution. \n\nBut let's check n=6 as we did earlier. \n\nAlternatively, maybe there's a different approach to verify. Let's compute (√3 - i)^n step by step for n=1,2,... and see when it becomes real and negative.\n\nn=1: √3 -i, not real.\n\nn=2: (√3 -i)^2. Let's compute this. \n\n(√3)^2 + (-i)^2 + 2*(√3)*(-i) = 3 + (-1)(i^2) + (-2√3 i). Wait, wait, better to multiply it out:\n\n(√3 -i)(√3 -i) = (√3)(√3) + (√3)(-i) + (-i)(√3) + (-i)(-i) = 3 -√3 i -√3 i + i²\n\ni squared is -1, so 3 - 2√3 i -1 = 2 - 2√3 i. Still not real.\n\nn=2: 2 - 2√3 i, which is complex, not real.\n\nn=3: Multiply previous result (2 - 2√3 i) by (√3 - i):\n\n(2)(√3) + (2)(-i) + (-2√3 i)(√3) + (-2√3 i)(-i)\n\nCalculate term by term:\n\n2√3 - 2i - 2√3*√3 i + 2√3 i²\n\nSimplify each term:\n\n2√3 - 2i - 2*(3)i + 2√3*(-1)\n\nSo:\n\n2√3 -2i -6i -2√3 = (2√3 -2√3) + (-2i -6i) = 0 -8i = -8i. \n\nSo n=3: -8i, which is purely imaginary (negative imaginary), but not real. So not what we need.\n\nn=4: Multiply -8i by (√3 -i):\n\n-8i*(√3 -i) = -8√3 i + 8i² = -8√3 i -8 (since i² = -1). So, that's -8 -8√3 i. Still complex.\n\nn=4: -8 -8√3 i.\n\nn=5: Multiply (-8 -8√3 i) by (√3 -i):\n\nUse distributive property:\n\n-8*(√3) + (-8)*(-i) + (-8√3 i)*(√3) + (-8√3 i)*(-i)\n\n= -8√3 +8i -8*3 i +8√3 i²\n\nSimplify:\n\n-8√3 +8i -24i +8√3*(-1)\n\n= (-8√3 -8√3) + (8i -24i)\n\n= -16√3 -16i\n\nn=5: -16√3 -16i, still complex.\n\nn=6: Multiply (-16√3 -16i) by (√3 -i):\n\nFirst term: (-16√3)(√3) = -16*3 = -48\n\nSecond term: (-16√3)(-i) = 16√3 i\n\nThird term: (-16i)(√3) = -16√3 i\n\nFourth term: (-16i)(-i) = 16i² = -16\n\nCombine all terms:\n\n-48 +16√3 i -16√3 i -16 = (-48 -16) + (16√3 i -16√3 i) = -64 + 0i = -64.\n\nAh! So n=6 gives -64, which is real and negative. So that works. Therefore, n=6 is indeed the smallest positive integer. \n\nWait, so the calculation checks out. So the answer should be 6. \n\nBut just to ensure that there isn't a smaller n that we might have missed with the previous approach. Let's check n=6. Since n=1 to n=5 don't work, and n=6 works, then 6 is indeed the smallest. \n\nAlternatively, using the polar form approach. Since the angle of (√3 -i) is 11π/6, so multiplying n times the angle should be congruent to π modulo 2π. So:\n\nn*(11π/6) ≡ π mod 2π\n\nWhich can be written as:\n\n(11n/6)π ≡ π mod 2π\n\nDivide both sides by π:\n\n11n/6 ≡ 1 mod 2\n\nMultiply both sides by 6:\n\n11n ≡ 6 mod 12\n\nSo, 11n ≡ 6 mod 12\n\nWe need to solve for n. 11 and 12 are coprime (gcd(11,12)=1), so the inverse of 11 mod 12 exists. The inverse of 11 mod 12 is a number x such that 11x ≡1 mod12. Let's find x:\n\n11*11 = 121 ≡ 121 -10*12=121-120=1 mod12. So, x=11. So, inverse is 11.\n\nMultiply both sides by 11:\n\nn ≡ 6*11 mod12\n\n6*11=66\n\n66 mod12: 12*5=60, 66-60=6. So 66≡6 mod12. So, n≡6 mod12. So the smallest positive n is 6. That matches the previous result. \n\nTherefore, the answer is 6. \n\nI think this confirms that n=6 is the smallest positive integer that satisfies the condition. So, the answer should be \\boxed{6}.\n\n**Final Answer**\nThe smallest positive integer \\( n \\) is \\boxed{6}.\n</think>To find the smallest positive integer \\( n \\) such that the complex number \\( (\\sqrt{3} - i)^n \\) is real and negative, we start by converting the complex number to polar form.\n\n1. **Calculate the modulus:**\n   \\[\n   \\text{Modulus} = \\sqrt{(\\sqrt{3})^2 + (-1)^2} = \\sqrt{3 + 1} = \\sqrt{4} = 2\n   \\]\n\n2. **Calculate the argument:**\n   \\[\n   \\text{Argument} = \\arctan\\left(\\frac{-1}{\\sqrt{3}}\\right) = -\\frac{\\pi}{6} \\quad \\text{(or equivalently } \\frac{11\\pi}{6}\\text{)}\n   \\]\n\n3. **Express in polar form:**\n   \\[\n   \\sqrt{3} - i = 2 \\left( \\cos\\left( \\frac{11\\pi}{6} \\right) + i \\sin\\left( \\frac{11\\pi}{6} \\right) \\right)\n   \\]\n\n4. **Apply De Moivre's Theorem:**\n   \\[\n   (\\sqrt{3} - i)^n = 2^n \\left( \\cos\\left( n \\cdot \\frac{11\\pi}{6} \\right) + i \\sin\\left( n \\cdot \\frac{11\\pi}{6} \\right) \\right)\n   \\]\n\n5. **Condition for real and negative:**\n   The result must be real and negative, so the argument must be \\( \\pi \\) (mod \\( 2\\pi \\)):\n   \\[\n   n \\cdot \\frac{11\\pi}{6} \\equiv \\pi \\pmod{2\\pi}\n   \\]\n\n6. **Simplify the congruence:**\n   \\[\n   \\frac{11n}{6} \\equiv 1 \\pmod{2} \\quad \\Rightarrow \\quad 11n \\equiv 6 \\pmod{12}\n   \\]\n\n7. **Solve the congruence:**\n   The modular inverse of 11 modulo 12 is 11. Multiplying both sides by 11:\n   \\[\n   n \\equiv 6 \\cdot 11 \\pmod{12} \\quad \\Rightarrow \\quad n \\equiv 66 \\pmod{12} \\quad \\Rightarrow \\quad n \\equiv 6 \\pmod{12}\n   \\]\n\nThus, the smallest positive integer \\( n \\) that satisfies the condition is \\( \\boxed{6} \\)."}