{"row_number": 6132, "expected_answer": "\\(90^\\circ\\)", "problem": "In triangle \\(ABC\\) with \\(\\angle B = 120^\\circ\\), points \\(E\\) and \\(F\\) are taken on sides \\(AB\\) and \\(BC\\) respectively such that \\(AE = EF = FC\\). Let \\(M\\) be the midpoint of \\(AC\\). Find \\(\\angle EMF\\).", "generated_solution": "<think>\nOkay, so I have this geometry problem here. Let me read it again to make sure I understand.\n\nIn triangle ABC, angle B is 120 degrees. Points E and F are on sides AB and BC respectively such that AE = EF = FC. M is the midpoint of AC. I need to find the measure of angle EMF. Hmm, okay.\n\nFirst, I need to visualize the triangle. Since angle B is 120 degrees, that's an obtuse triangle. Let me sketch this mentally. Point B is the vertex with the big angle. Points E on AB and F on BC are such that AE, EF, and FC are all equal. So AE = EF = FC. Wait, AE is on AB, EF is a segment connecting E to F, and FC is on BC. So they form a chain from A to E to F to C, each segment equal in length. Interesting.\n\nSince M is the midpoint of AC, that's straightforward. The problem is to find angle EMF, which is the angle at point M between points E and F.\n\nHmm. Let me think about possible approaches. Coordinate geometry might be a good idea here. Assign coordinates to the points and calculate vectors or use coordinate geometry formulas to find the angle. Alternatively, maybe use vectors or trigonometry. Let's try coordinates.\n\nLet's place point B at the origin to simplify calculations. Since angle B is 120 degrees, perhaps we can place points A and C in such a way that calculations are easier. Let me assign coordinates.\n\nLet me set point B at (0, 0). Let me take side BC along the positive x-axis. Let's suppose BC has length 3 units for simplicity, since FC is one-third of BC? Wait, because FC = EF = AE. Wait, but AE is on AB, EF is between E and F, and FC is on BC. Hmm. Let me clarify the lengths.\n\nIf AE = EF = FC, then starting from A, moving along AB to E, then from E to F (which is on BC), then from F to C. But AB and BC are sides of the triangle. So maybe the lengths AE, EF, FC are all equal, but not necessarily each being a third of AB or BC. Hmm. That might complicate things. Let me think.\n\nWait, the problem states that E is on AB and F is on BC such that AE = EF = FC. So the segments AE, EF, FC are all equal. So starting at A, moving along AB to E, then from E to F (which is not along AB or BC, but a different direction), then from F to C along BC. Wait, EF is a segment connecting E and F, which are on different sides. So EF is a transversal. So AE, EF, FC are all equal in length, but they're not along the same line. Therefore, this creates a sort of stepped path from A to C via E and F, with each step equal in length.\n\nSo perhaps we can model this using coordinates. Let me proceed step by step.\n\nFirst, let's assign coordinates to points. Let me place point B at the origin (0, 0). Let me let BC be along the x-axis. Let me assume BC has length 3k, so that FC = k. Then since FC = k, and F is on BC, point F will be at (2k, 0), since from B(0,0) to C(3k,0), FC is from F(2k,0) to C(3k,0), which is length k. Similarly, EF = k. Then E is a point on AB such that EF = k and AE = k. Hmm.\n\nBut we need to define AB such that angle at B is 120 degrees. Let me figure out coordinates for points A and C.\n\nLet me assume point B is at (0,0), point C is at (3,0) (taking k=1 for simplicity). Then BC is from (0,0) to (3,0). Now, angle at B is 120 degrees. So triangle ABC has angle B = 120°, with sides BA, BC, and AC.\n\nTo find coordinates of A, since angle at B is 120°, we can use the Law of Cosines. But maybe it's easier to set up coordinate system. Let me set BA along some direction. Let me suppose BA makes an angle of 120° with BC. Since BC is along the x-axis, BA would be making 120° with the positive x-axis. So the coordinates of A can be determined if we know the length of BA.\n\nBut maybe we can let BA be of length such that calculations are easier. Wait, perhaps it's better to assign coordinates such that BA is of length 'a' and BC is of length 'c', then using Law of Cosines. But maybe coordinate system approach is better.\n\nAlternatively, to set up coordinates with B at (0,0), C at (3,0), and angle at B is 120°, so we can find coordinates of A.\n\nThe Law of Cosines for triangle ABC: AC² = AB² + BC² - 2*AB*BC*cos(120°). But we don't know the lengths of AB or BC. Hmm. Since we set BC as length 3 (with C at (3,0)), then BC = 3. Let me let AB be some length, say, let's choose AB = 3 as well. Then AC² = 3² + 3² - 2*3*3*cos(120°). Let's compute that.\n\ncos(120°) is -0.5. So AC² = 9 + 9 - 2*3*3*(-0.5) = 18 + 9 = 27. So AC = 3√3. Then coordinates of A can be determined.\n\nSince angle at B is 120°, and BA is of length 3, pointing 120° from the x-axis. So coordinates of A would be (3*cos(120°), 3*sin(120°)). Cos(120°) is -0.5, sin(120°) is √3/2. So A is at (3*(-0.5), 3*(√3/2)) = (-1.5, (3√3)/2). Let's note that.\n\nSo point A is at (-1.5, (3√3)/2), B is at (0,0), C is at (3,0).\n\nNow, points E on AB and F on BC such that AE = EF = FC = k. But since FC is part of BC, which is from (3,0) to (0,0). Wait, but we set BC as length 3. So FC = k, so F is 3 - k away from C? Wait, hold on. If FC = k, then since BC is length 3, BF = BC - FC = 3 - k. But earlier, I thought if BC is 3, then FC is 1 if k=1. But according to the problem, AE = EF = FC. So all three segments are equal. So we need to find E on AB and F on BC such that AE = EF = FC. Hmm. So, AE is along AB, EF is from E to F (which is on BC), and FC is along BC. So each of those three segments has the same length. Let me see.\n\nGiven that, maybe AE = EF = FC = 1, so FC = 1, so F is 1 unit away from C on BC. Since BC is from (3,0) to (0,0), then F would be at (3 - 1, 0) = (2,0). Then FC = 1, which is from (2,0) to (3,0). Then EF = 1. So E is a point such that the distance from E to F(2,0) is 1. Also, E is on AB. And AE = 1. So AE is from A(-1.5, (3√3)/2) to E, and the length of AE is 1.\n\nSo we need to find E on AB such that AE = 1 and EF = 1, with F at (2,0). Let me parameterize AB. The coordinates of AB can be parametrized from A(-1.5, (3√3)/2) to B(0,0). Let me find the parametric equations.\n\nLet parameter t go from 0 to 1, where t=0 is at A and t=1 is at B. Then coordinates of E are:\n\nx = -1.5 + t*(0 - (-1.5)) = -1.5 + 1.5t\n\ny = (3√3)/2 + t*(0 - (3√3)/2) = (3√3)/2 - (3√3)/2 * t\n\nSo E is at (-1.5 + 1.5t, (3√3)/2 - (3√3)/2 t )\n\nThe distance AE is given as 1. The distance from A to E is t times the length of AB. Since AB is length 3 (as we set earlier), then distance AE = 3t. But the problem says AE = 1, so 3t = 1, so t = 1/3. So E is at:\n\nx = -1.5 + 1.5*(1/3) = -1.5 + 0.5 = -1.0\n\ny = (3√3)/2 - (3√3)/2*(1/3) = (3√3)/2 - (√3)/2 = (2√3)/2 = √3\n\nTherefore, E is at (-1, √3). Wait, let me check that.\n\nWait, if t = 1/3, then the coordinates would be:\n\nx = -1.5 + 1.5*(1/3) = -1.5 + 0.5 = -1.0\n\ny = (3√3/2) - (3√3/2)*(1/3) = (3√3/2) - (√3/2) = (2√3/2) = √3. Correct.\n\nSo E is at (-1, √3). Now, F is at (2,0). Then EF is the distance between E(-1, √3) and F(2,0). Let's compute that.\n\nDistance EF = sqrt[(2 - (-1))² + (0 - √3)²] = sqrt[(3)^2 + (-√3)^2] = sqrt[9 + 3] = sqrt[12] = 2*sqrt(3). But the problem states EF should be equal to FC, which we set as 1. But here, EF is 2*sqrt(3), which is not 1. Wait, that contradicts the problem statement. So something's wrong here.\n\nHmm. So my assumption that AB is length 3 might be incorrect. Because in this setup, if AB is 3, then AE would be 1 when t = 1/3, but then EF is not 1. Therefore, my initial assumption of AB being length 3 must be wrong. So maybe I need to adjust the lengths.\n\nAlternatively, maybe I need to choose BC of a different length so that AE = EF = FC. Let me think.\n\nLet me denote FC = x, so EF = x, and AE = x. Then, since FC = x, F is located x units away from C along BC. So if BC is length L, then BF = L - x. Also, E is located x units away from A along AB. So AB must be at least x in length.\n\nBut angle at B is 120°, so perhaps we can use Law of Cosines to relate the sides.\n\nLet me denote AB = c, BC = a, and AC = b. But maybe let's use variables for coordinates.\n\nAlternatively, let me try to set up the problem more abstractly. Let me consider BC as a line segment of length 3x, so that FC = x, hence F divides BC into BF = 2x and FC = x. Then EF = x, so the distance from E to F must be x. Also, AE = x, so E divides AB such that AE = x.\n\nAssuming that, perhaps I can assign coordinates accordingly.\n\nLet me set point B at (0,0), point C at (3x,0). Then F is at (2x,0). Then point E is on AB, at a distance x from A, and point E must be such that the distance from E to F is x. Let me find coordinates of A such that angle at B is 120°.\n\nLet me denote AB = m. Then coordinates of A can be determined based on angle B = 120°.\n\nIn triangle ABC, angle at B is 120°, BC = 3x, AB = m. Then coordinates of A can be given in terms of m and angle 120°.\n\nCoordinates of A: since from point B(0,0), moving at an angle of 120° for length m. So coordinates of A are (m*cos(120°), m*sin(120°)) = ( -m/2, (m√3)/2 )\n\nTherefore, point A is at (-m/2, (m√3)/2 )\n\nPoint E is x units away from A along AB. Since AB is from A(-m/2, (m√3)/2 ) to B(0,0), the direction from A to B is (m/2, - (m√3)/2 ). The unit vector in the direction AB is (m/2 / m, - (m√3)/2 / m ) = (1/2, -√3/2 )\n\nTherefore, moving x units from A along AB gives point E:\n\nE = A + x*(unit vector AB)\n\n= (-m/2, (m√3)/2 ) + x*(1/2, -√3/2 )\n\n= (-m/2 + x/2, (m√3)/2 - (x√3)/2 )\n\n= ( (-m + x)/2 , ( (m - x)√3 ) / 2 )\n\nNow, F is at (2x,0). The distance between E and F should be x. So let's compute the coordinates of E and F and set their distance to x.\n\nCoordinates of E: ( (-m + x)/2 , ( (m - x)√3 ) / 2 )\n\nCoordinates of F: (2x,0 )\n\nDistance EF:\n\nsqrt[ (2x - (-m + x)/2 )² + (0 - ( (m - x)√3 ) / 2 )² ] = x\n\nSimplify the terms inside:\n\nFirst coordinate difference:\n\n2x - [ (-m + x)/2 ] = (4x - (-m + x))/2 = (4x + m - x)/2 = (3x + m)/2\n\nSecond coordinate difference:\n\n0 - [ ( (m - x)√3 ) / 2 ] = - ( (m - x)√3 ) / 2\n\nTherefore, the distance squared is:\n\n[ (3x + m)/2 ]² + [ - ( (m - x)√3 ) / 2 ]²\n\n= ( (3x + m)² + 3(m - x)² ) / 4\n\nSet equal to x² (since distance EF is x):\n\n( (3x + m)² + 3(m - x)² ) / 4 = x²\n\nMultiply both sides by 4:\n\n(3x + m)² + 3(m - x)² = 4x²\n\nExpand each term:\n\n(9x² + 6mx + m²) + 3(m² - 2mx + x²) = 4x²\n\n= 9x² + 6mx + m² + 3m² - 6mx + 3x² = 4x²\n\nCombine like terms:\n\n9x² + 3x² = 12x²\n\n6mx -6mx = 0\n\nm² + 3m² = 4m²\n\nTherefore, total left side is 12x² + 4m²\n\nSet equal to 4x²:\n\n12x² + 4m² = 4x²\n\nSubtract 4x²:\n\n8x² + 4m² = 0\n\nDivide both sides by 4:\n\n2x² + m² = 0\n\nBut x and m are lengths, so they can't be negative. The only solution is x = 0 and m = 0, which is impossible for a triangle. Hmm, that's a problem. So this implies that there is no solution unless x and m are imaginary numbers, which doesn't make sense here. So there must be a mistake in my approach.\n\nWait, maybe my coordinate system setup is flawed. Let me check again.\n\nWait, perhaps the problem is that FC is along BC, which in my setup is from (0,0) to (3x,0), so FC = x would place F at (3x - x, 0) = (2x, 0). So that part is correct.\n\nThen, point E is on AB such that AE = x. So AB has length m, and E is x units from A towards B. So the coordinates of E were calculated correctly.\n\nThen, the distance between E and F is calculated as sqrt[...] and set to x. The resulting equation leads to 2x² + m² = 0, which is impossible. Therefore, this suggests that under my initial assumptions (assigning BC as 3x, FC = x, etc.), there's a contradiction. So my approach must be wrong.\n\nAlternatively, perhaps the problem requires BC to be divided in a different ratio. Wait, the problem says AE = EF = FC. So starting from A, moving along AB to E, then from E to F, then from F to C along BC. So AE, EF, FC are consecutive segments, each of equal length. Therefore, the total length from A to C via E and F is 3 times the length of AE. But in reality, the straight-line distance AC is shorter. However, maybe the triangle is structured such that this path is possible.\n\nAlternatively, perhaps the coordinates approach is too convoluted here. Maybe using vectors or another geometric approach would be better.\n\nLet me think differently. Let's consider triangle ABC with angle B = 120°, and points E on AB, F on BC such that AE = EF = FC. Let me try to find the ratio of AE to AB and FC to BC.\n\nSince AE = EF = FC, let me denote their common length as k. Then AE = k, EF = k, FC = k.\n\nTherefore, along BC, FC = k, so BC = BF + FC = BF + k. Therefore, BF = BC - k.\n\nSimilarly, along AB, AE = k, so AB = AE + EB = k + EB.\n\nNow, point E is on AB, and point F is on BC. The segment EF connects them, and EF = k.\n\nWe can use the Law of Cosines in triangle EBF or other triangles. Wait, but perhaps we can use coordinate geometry but not fix BC to a specific length. Let me set coordinates again, but this time with variable lengths.\n\nLet me place point B at the origin (0,0). Let me let BC be along the x-axis, so point C is at (c, 0). Then, since angle at B is 120°, point A will be somewhere in the plane. Let me find coordinates of A.\n\nUsing the Law of Cosines in triangle ABC:\n\nAC² = AB² + BC² - 2 AB BC cos(120°)\n\nBut without knowing AB or BC, this is still too abstract. Let me instead assign coordinates based on angle.\n\nFrom point B(0,0), angle of 120° between BA and BC. If BC is along the x-axis, then BA makes an angle of 120° with the positive x-axis. Therefore, coordinates of A can be expressed as (AB cos 120°, AB sin 120° ) = ( -AB/2, (AB √3)/2 )\n\nSo point A is at (-AB/2, (AB √3)/2 )\n\nNow, point E is on AB such that AE = k. So starting from A, moving towards B by a distance k. Since AB has length AB, the coordinates of E can be parametrized as A + (B - A)*(k/AB). Let me compute that.\n\nVector AB is (0 - (-AB/2), 0 - (AB √3)/2 ) = (AB/2, - (AB √3)/2 )\n\nTherefore, moving from A towards B by a distance k (which is a fraction of AB), the coordinates of E are:\n\nE = A + (AB/2, - (AB √3)/2 ) * (k / AB )\n\n= (-AB/2, (AB √3)/2 ) + ( (AB/2)*(k/AB), - (AB √3)/2*(k/AB ) )\n\n= (-AB/2 + k/2, (AB √3)/2 - (k √3)/2 )\n\n= ( (-AB + k ) / 2, ( (AB - k ) √3 ) / 2 )\n\nSimilarly, point F is on BC such that FC = k. Since BC is from (0,0) to (c, 0), then point F is at (c - k, 0 )\n\nNow, the distance between E and F should be equal to k. So compute the distance EF and set it equal to k.\n\nCoordinates of E: ( (-AB + k ) / 2, ( (AB - k ) √3 ) / 2 )\n\nCoordinates of F: (c - k, 0 )\n\nDistance EF squared:\n\n[ c - k - ( (-AB + k ) / 2 ]² + [ 0 - ( (AB - k ) √3 ) / 2 ]² = k²\n\nSimplify the terms:\n\nFirst coordinate difference:\n\nc - k - (-AB + k ) / 2 = (2(c - k) - (-AB + k )) / 2\n\n= (2c - 2k + AB - k ) / 2\n\n= (2c + AB - 3k ) / 2\n\nSecond coordinate difference:\n\n- ( (AB - k ) √3 ) / 2\n\nSquare both terms:\n\nFirst term squared: (2c + AB - 3k )² / 4\n\nSecond term squared: 3(AB - k )² / 4\n\nSum equals k²:\n\n[ (2c + AB - 3k )² + 3(AB - k )² ] / 4 = k²\n\nMultiply both sides by 4:\n\n(2c + AB - 3k )² + 3(AB - k )² = 4k²\n\nNow, expand the left-hand side:\n\nFirst term: (2c + AB - 3k )² = (2c + AB - 3k )²\n\nSecond term: 3(AB - k )² = 3(AB² - 2ABk + k² )\n\nLet me expand (2c + AB - 3k )²:\n\n= (2c)^2 + (AB - 3k )² + 2*(2c)*(AB - 3k )\n\n= 4c² + AB² - 6ABk + 9k² + 4cAB - 12ck\n\nSo total left-hand side:\n\n4c² + AB² - 6ABk + 9k² + 4cAB - 12ck + 3AB² - 6ABk + 3k²\n\nCombine like terms:\n\n4c² + (AB² + 3AB²) + (-6ABk -6ABk ) + (9k² + 3k²) +4cAB -12ck\n\n= 4c² + 4AB² -12ABk +12k² +4cAB -12ck\n\nSet equal to 4k²:\n\n4c² + 4AB² -12ABk +12k² +4cAB -12ck =4k²\n\nSubtract 4k²:\n\n4c² + 4AB² -12ABk +8k² +4cAB -12ck =0\n\nDivide both sides by 4:\n\nc² + AB² -3ABk +2k² +cAB -3ck =0\n\nHmm, this is getting complicated. Maybe there's a relationship between c (BC) and AB from the original triangle ABC.\n\nIn triangle ABC, angle at B is 120°, so by the Law of Cosines:\n\nAC² = AB² + BC² - 2 AB BC cos(120° )\n\nAC² = AB² + c² - 2 AB c (-1/2 )\n\nAC² = AB² + c² + AB c\n\nSo that's the relation between AB and BC (c). Let me note that.\n\nBut we need another equation to relate c and AB. Perhaps from the previous equation we derived:\n\nc² + AB² -3ABk +2k² +cAB -3ck =0\n\nThis seems too involved. Maybe we need to find ratios or express variables in terms of each other.\n\nAlternatively, perhaps take k=1 for simplicity, then solve for AB and c. Let's try that.\n\nLet k=1. Then the equation becomes:\n\nc² + AB² -3AB*1 +2*1² +cAB -3c*1 =0\n\nSimplify:\n\nc² + AB² -3AB +2 +cAB -3c =0\n\nWhich is:\n\nc² + AB² +cAB -3AB -3c +2=0\n\nHmm. Also, from the Law of Cosines:\n\nAC² = AB² + c² + AB c\n\nBut maybe we can find another equation.\n\nAlternatively, perhaps choose AB = c, but that's arbitrary. Let me suppose AB = c. Let's see.\n\nIf AB = c, then substituting into the equation:\n\nc² + c² +c*c -3c -3c +2=0\n\n= 2c² + c² -6c +2=0\n\n=3c² -6c +2=0\n\nSolving quadratic:\n\nc = [6 ± sqrt(36 -24)] /6 = [6 ± sqrt(12)] /6 = [6 ± 2*sqrt(3)] /6 = [3 ± sqrt(3)] /3\n\nBut AB = c, so this gives possible values for c. But maybe this isn't helpful. Alternatively, perhaps assume AB = 2 units, then compute c.\n\nAlternatively, maybe this approach is too algebraic and messy. Let me think if there's a better way.\n\nAlternatively, use complex numbers. Let me place point B at the origin in the complex plane. Let point C be at a real number c, so complex coordinate c. Point A is at some complex number a. Given angle at B is 120°, which translates to the argument between vectors BA and BC being 120°. Hmm, but this might not simplify things.\n\nAlternatively, maybe use vectors. Let me denote vectors BA and BC.\n\nGiven angle at B is 120°, the dot product between vectors BA and BC is |BA||BC|cos(120°)\n\nBut this might not directly help.\n\nWait, perhaps going back to coordinate geometry. Let me choose specific values for AB and BC that satisfy the Law of Cosines, then see if AE = EF = FC is possible.\n\nSuppose we let AB = 1, then BC can be calculated using the Law of Cosines if we fix AC. Wait, no, without more info, it's hard.\n\nAlternatively, let me assume AB = BC. If AB = BC, then triangle ABC is isoceles with AB = BC. Then angle at B is 120°, so the other angles are 30° each. Then AC² = AB² + AB² - 2 AB² cos(120°) = 2AB² - 2AB²*(-0.5) = 2AB² + AB² = 3AB². So AC = AB√3.\n\nIn this case, coordinates of A would be ( -AB/2, (AB√3)/2 ), as before. Let me set AB = BC = 2 for simplicity, so coordinates:\n\nB(0,0), C(2,0). Then AB = 2, so A is at (-1, √3). Then AC should be 2√3, which is distance from (-1, √3) to (2,0):\n\nsqrt( (3)^2 + (√3)^2 ) = sqrt(9 + 3) = sqrt(12) = 2√3. Correct.\n\nNow, AE = EF = FC. Let's see.\n\nFC is along BC from F to C. Since BC is from (0,0) to (2,0), FC = k implies F is at (2 - k, 0). Similarly, AE = k implies E is k units from A along AB.\n\nAB is from A(-1, √3) to B(0,0). The vector AB is (1, -√3). The unit vector in direction AB is (1/2, -√3/2) since |AB| = 2. So moving k units from A along AB:\n\nE = A + k*(1/2, -√3/2 )\n\n= (-1, √3) + (k/2, - (k√3)/2 )\n\n= (-1 + k/2, √3 - (k√3)/2 )\n\nF is at (2 - k, 0)\n\nNow, distance EF must be k.\n\nCompute distance between E(-1 + k/2, √3 - (k√3)/2 ) and F(2 - k, 0):\n\nDifference in x-coordinates: (2 - k) - (-1 + k/2 ) = 2 - k +1 - k/2 = 3 - (3k)/2\n\nDifference in y-coordinates: 0 - (√3 - (k√3)/2 ) = -√3 + (k√3)/2\n\nDistance squared:\n\n[3 - (3k)/2]^2 + [ -√3 + (k√3)/2 ]^2 = k²\n\nCompute each term:\n\nFirst term: (3 - (3k)/2 )² = 9 - 9k + (9k²)/4\n\nSecond term: [ -√3 + (k√3)/2 ]² = ( √3 (-1 + k/2 ) )² = 3*(1 - k + (k²)/4 )\n\nSum:\n\n9 -9k + (9k²)/4 + 3 - 3k + (3k²)/4 = k²\n\nSimplify:\n\n(9 + 3) + (-9k -3k) + (9k²/4 + 3k²/4) = k²\n\n12 -12k + 12k²/4 = k²\n\n12 -12k +3k² =k²\n\nBring all terms to left:\n\n3k² -k² -12k +12 =0\n\n2k² -12k +12 =0\n\nDivide by 2:\n\nk² -6k +6 =0\n\nSolutions:\n\nk = [6 ± sqrt(36 -24)] /2 = [6 ± sqrt(12)] /2 = [6 ± 2*sqrt(3)] /2 = 3 ± sqrt(3)\n\nSince k must be positive and less than AB = 2, let's check the values:\n\n3 + sqrt(3) ≈ 3 + 1.732 ≈ 4.732 > 2, invalid\n\n3 - sqrt(3) ≈ 3 - 1.732 ≈ 1.268 < 2, valid.\n\nTherefore, k = 3 - sqrt(3) ≈1.268. So in this case, with AB=BC=2, FC=3 - sqrt(3). Then AE=EF=FC=3 - sqrt(3).\n\nNow, we need to find points E and F.\n\nCoordinates of E:\n\nE = (-1 + k/2, √3 - (k√3)/2 )\n\nk=3 - sqrt(3), so:\n\nx-coordinate: -1 + (3 - sqrt(3))/2 = (-2 + 3 - sqrt(3))/2 = (1 - sqrt(3))/2\n\ny-coordinate: √3 - ( (3 - sqrt(3))√3 )/2 = √3 - [ 3√3 - 3 ] / 2 = √3 - (3√3)/2 + 3/2 = (-√3)/2 + 3/2\n\nSo E is at ( (1 - sqrt(3))/2 , (3 - √3)/2 )\n\nCoordinates of F:\n\nF = (2 - k, 0 ) = (2 - (3 - sqrt(3)), 0 ) = ( -1 + sqrt(3), 0 )\n\nWait, 2 - 3 is -1, so (-1 + sqrt(3), 0 ). But sqrt(3) ≈1.732, so -1 +1.732 ≈0.732, which is between B(0,0) and C(2,0). So F is at (sqrt(3)-1, 0 )\n\nNow, M is the midpoint of AC. Coordinates of A(-1, √3), C(2,0). Midpoint M:\n\nx = (-1 +2)/2 = 0.5\n\ny = (√3 +0)/2 = √3/2\n\nSo M is at (0.5, √3/2 )\n\nNow, need to find angle EMF, which is the angle at point M between points E and F.\n\nTo find angle EMF, we can compute the vectors ME and MF, then find the angle between them.\n\nFirst, find coordinates of vectors ME and MF.\n\nCoordinates of E: ( (1 - sqrt(3))/2 , (3 - √3)/2 )\n\nCoordinates of M: (0.5, √3/2 )\n\nCoordinates of F: (sqrt(3) -1, 0 )\n\nVector ME = E - M:\n\nx: (1 - sqrt(3))/2 - 0.5 = (1 - sqrt(3))/2 - 1/2 = (-sqrt(3))/2\n\ny: (3 - √3)/2 - √3/2 = (3 - √3 - √3)/2 = (3 - 2√3)/2\n\nSo ME vector is ( -sqrt(3)/2 , (3 - 2√3)/2 )\n\nVector MF = F - M:\n\nx: sqrt(3) -1 -0.5 = sqrt(3) -1.5\n\ny: 0 - √3/2 = -√3/2\n\nNow, to find angle between ME and MF, use the dot product formula:\n\ncos(theta) = ( ME · MF ) / ( |ME| |MF| )\n\nFirst compute the dot product:\n\nME · MF = [ -sqrt(3)/2 ] * [ sqrt(3) -1.5 ] + [ (3 - 2√3)/2 ] * [ -√3/2 ]\n\nLet me compute each term:\n\nFirst term:\n\n- sqrt(3)/2 * sqrt(3) = - (3)/2\n\n- sqrt(3)/2 * (-1.5) = (1.5 sqrt(3))/2 = (3 sqrt(3))/4\n\nSecond term:\n\n(3 - 2√3)/2 * (-√3)/2 = [ -3√3 + 2*3 ] / 4 = [ -3√3 +6 ] /4\n\nSo total dot product:\n\n-3/2 + (3√3)/4 + ( -3√3 +6 ) /4\n\nCombine terms:\n\n-3/2 + [3√3 -3√3 +6 ] /4 = -3/2 + 6/4 = -3/2 + 3/2 = 0\n\nWait, the dot product is zero? That would mean the vectors ME and MF are perpendicular, so angle EMF is 90 degrees. But let me check the calculations again to make sure.\n\nFirst term of ME · MF:\n\n[ -sqrt(3)/2 ] * [ sqrt(3) -1.5 ]\n\n= -sqrt(3)/2 * sqrt(3) + sqrt(3)/2 * 1.5\n\n= - (3)/2 + (1.5 sqrt(3))/2\n\n= -1.5 + (0.75 sqrt(3))\n\nSecond term:\n\n[ (3 - 2√3)/2 ] * [ -√3/2 ]\n\n= (3*(-√3) + (-2√3)(-√3) ) / 4\n\n= ( -3√3 + 6 ) /4\n\nSo combining both terms:\n\n(-1.5 + 0.75√3) + ( -3√3/4 + 6/4 )\n\n= -1.5 + 0.75√3 -0.75√3 + 1.5\n\n= (-1.5 +1.5) + (0.75√3 -0.75√3 )\n\n= 0 + 0 = 0\n\nWow, the dot product is indeed zero. Therefore, vectors ME and MF are perpendicular, so angle EMF is 90 degrees. Therefore, the answer is 90°, which is π/2 radians.\n\nBut the problem states to put the answer inside \\boxed{}, so probably \\boxed{90^\\circ} or \\boxed{\\dfrac{\\pi}{2}} if in radians, but since the problem uses degrees, 90 degrees.\n\nBut wait, let me confirm with another approach to make sure.\n\nAlternative approach: Since the dot product is zero, angle is 90°, so answer is 90°.\n\nTherefore, the answer should be \\boxed{90^\\circ}\n</think>To solve the problem, we will use a coordinate geometry approach and vector analysis to find the angle \\(\\angle EMF\\).\n\n1. **Coordinate Setup**:\n   - Place point \\(B\\) at the origin \\((0, 0)\\).\n   - Place point \\(C\\) at \\((2, 0)\\) since \\(BC = 2\\).\n   - Since \\(\\angle B = 120^\\circ\\), point \\(A\\) can be determined using the coordinates \\((-1, \\sqrt{3})\\).\n\n2. **Finding Points \\(E\\) and \\(F\\)**:\n   - Let \\(AE = EF = FC = k\\).\n   - Since \\(FC = k\\) and \\(BC = 2\\), point \\(F\\) is at \\((2 - k, 0)\\).\n   - Point \\(E\\) is on \\(AB\\) such that \\(AE = k\\). The coordinates of \\(E\\) can be found by moving \\(k\\) units from \\(A\\) towards \\(B\\).\n\n3. **Calculating \\(k\\)**:\n   - Using the distance formula for \\(EF = k\\):\n     \\[\n     \\sqrt{(2 - k - x_E)^2 + (0 - y_E)^2} = k\n     \\]\n   - Solving the above equation, we find \\(k = 3 - \\sqrt{3}\\).\n\n4. **Coordinates of \\(E\\) and \\(F\\)**:\n   - Coordinates of \\(E\\):\n     \\[\n     E = \\left(\\frac{1 - \\sqrt{3}}{2}, \\frac{3 - \\sqrt{3}}{2}\\right)\n     \\]\n   - Coordinates of \\(F\\):\n     \\[\n     F = (\\sqrt{3} - 1, 0)\n     \\]\n\n5. **Midpoint \\(M\\)**:\n   - Midpoint \\(M\\) of \\(AC\\):\n     \\[\n     M = \\left(\\frac{-1 + 2}{2}, \\frac{\\sqrt{3} + 0}{2}\\right) = \\left(\\frac{1}{2}, \\frac{\\sqrt{3}}{2}\\right)\n     \\]\n\n6. **Vectors \\(\\overrightarrow{ME}\\) and \\(\\overrightarrow{MF}\\)**:\n   - Vector \\(\\overrightarrow{ME}\\):\n     \\[\n     \\overrightarrow{ME} = \\left(\\frac{1 - \\sqrt{3}}{2} - \\frac{1}{2}, \\frac{3 - \\sqrt{3}}{2} - \\frac{\\sqrt{3}}{2}\\right) = \\left(\\frac{-\\sqrt{3}}{2}, \\frac{3 - 2\\sqrt{3}}{2}\\right)\n     \\]\n   - Vector \\(\\overrightarrow{MF}\\):\n     \\[\n     \\overrightarrow{MF} = \\left(\\sqrt{3} - 1 - \\frac{1}{2}, 0 - \\frac{\\sqrt{3}}{2}\\right) = \\left(\\sqrt{3} - \\frac{3}{2}, -\\frac{\\sqrt{3}}{2}\\right)\n     \\]\n\n7. **Dot Product Calculation**:\n   - The dot product of \\(\\overrightarrow{ME}\\) and \\(\\overrightarrow{MF}\\):\n     \\[\n     \\overrightarrow{ME} \\cdot \\overrightarrow{MF} = \\left(\\frac{-\\sqrt{3}}{2}\\right) \\left(\\sqrt{3} - \\frac{3}{2}\\right) + \\left(\\frac{3 - 2\\sqrt{3}}{2}\\right) \\left(-\\frac{\\sqrt{3}}{2}\\right)\n     \\]\n   - Simplifying the dot product:\n     \\[\n     \\left(\\frac{-\\sqrt{3}}{2}\\right) \\left(\\sqrt{3} - \\frac{3}{2}\\right) + \\left(\\frac{3 - 2\\sqrt{3}}{2}\\right) \\left(-\\frac{\\sqrt{3}}{2}\\right) = 0\n     \\]\n   - Since the dot product is zero, the vectors \\(\\overrightarrow{ME}\\) and \\(\\overrightarrow{MF}\\) are perpendicular.\n\n8. **Conclusion**:\n   - Since \\(\\overrightarrow{ME}\\) and \\(\\overrightarrow{MF}\\) are perpendicular, \\(\\angle EMF = 90^\\circ\\).\n\nThus, the final answer is:\n\\[\n\\boxed{90^\\circ}\n\\]"}