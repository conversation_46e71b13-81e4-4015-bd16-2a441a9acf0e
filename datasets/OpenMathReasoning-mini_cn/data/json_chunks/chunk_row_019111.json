{"row_number": 19111, "expected_answer": "\\(\\frac{2\\pi i}{n!}\\)", "problem": "Calculate the integral\n\\[\n\\int_{C_r} \\dfrac{e^z \\cos z}{z(z+1)\\dots (z+n)}\\, dz\n\\]\nwhere \\(0 < r < 1\\) and \\(C_r = \\{re^{i\\theta} \\mid 0 \\le \\theta \\le 2\\pi\\}\\) is a circle with radius \\(r\\) traversed counterclockwise.", "generated_solution": "<think>\nOkay, so I have this integral to solve: the integral of e^z times cos(z) divided by z(z+1)...(z+n) around a circle C_r with radius r between 0 and 1. The circle is traversed counterclockwise. Hmm. Let me try to recall complex analysis and contour integrals.\n\nFirst, since the integral is over a closed contour, maybe I can use the residue theorem. The residue theorem says that the integral of a function around a closed contour is 2πi times the sum of the residues inside the contour. But here, the contour is C_r with radius r < 1. So I need to check which singularities of the integrand lie inside this contour.\n\nThe integrand is e^z cos(z) divided by z(z+1)...(z+n). The denominator is z multiplied by (z+1) up to (z+n). So the singularities are at z = 0, z = -1, z = -2, ..., z = -n. But since the radius r is between 0 and 1, all the poles except possibly z = 0 are at z = -k where k is 1 to n. But each of these -k is at a distance k from the origin. Since k ≥ 1 and r < 1, then all these points -1, -2, ..., -n lie outside the circle of radius r. Therefore, the only singularity inside C_r is z = 0.\n\nTherefore, by the residue theorem, the integral should be 2πi times the residue of the integrand at z = 0. So the problem reduces to computing the residue at z = 0.\n\nSo let me write the integrand as f(z) = [e^z cos(z)] / [z(z+1)(z+2)...(z+n)]. Then, since z = 0 is a simple pole (because the denominator has a single z term and the rest of the terms at z=0 are (0+1)(0+2)...(0+n) = n! which is non-zero). Therefore, the residue at z=0 is the limit as z approaches 0 of z * f(z).\n\nSo Res(f, 0) = lim_{z→0} z * [e^z cos(z)] / [z(z+1)...(z+n)] = lim_{z→0} [e^z cos(z)] / [(z+1)(z+2)...(z+n)].\n\nNow, substituting z = 0 into this expression gives [e^0 cos(0)] / [1*2*...*n] = [1 * 1] / n! = 1/n!.\n\nTherefore, the residue is 1/n!.\n\nHence, the integral is 2πi times this residue, so 2πi * (1/n!) = 2πi / n!.\n\nWait, but let me check if I missed anything here. The integrand has e^z * cos(z). When I compute the residue, I just took the limit as z approaches 0 of z times the integrand. Since the denominator factors are (z)(z+1)...(z+n), then when I multiply by z, it cancels the z in the denominator, leaving e^z cos(z) over (z+1)...(z+n). Then at z=0, the denominator becomes 1*2*...*n = n!. The numerator at z=0 is e^0 cos(0) = 1*1 = 1. So yes, that seems correct.\n\nBut maybe I should check if there's another way to compute this. For example, maybe using series expansion or something else, but since it's a simple pole, the residue calculation should be straightforward.\n\nAlternatively, perhaps there's an alternative approach using the Cauchy integral formula. Let's recall that the Cauchy integral formula states that for a function g(z) analytic inside and on the contour, then the integral of g(z)/(z - a) around the contour is 2πi g(a). But in this case, the denominator is z(z+1)...(z+n). Hmm. So maybe if I can rewrite the denominator as a product, then maybe express the integrand as e^z cos(z) divided by z times (z+1)...(z+n). Then the only pole inside is at z=0. So maybe using higher-order poles or partial fractions? Wait, but z=0 is a simple pole here because the denominator has z to the first power.\n\nAlternatively, perhaps I can consider expanding the denominator as a Laurent series around z=0, but that seems more complicated than necessary. Since the residue at z=0 is straightforward to compute as we did earlier, I think that's sufficient.\n\nTherefore, my conclusion is that the integral equals 2πi / n!.\n\nWait, let me just verify once again. The function is e^z cos(z) over z(z+1)...(z+n). The only singularity inside |z| = r <1 is at z=0. The residue at z=0 is the limit as z approaches 0 of z times the integrand. That's correct. So z cancels with the denominator's z, leaving e^z cos(z) divided by (z+1)...(z+n). At z=0, that's 1/(1*2*...*n) = 1/n!. Therefore, the residue is 1/n!.\n\nSo the integral is 2πi times that. So the final answer is 2πi/n!.\n\nI don't see any mistakes here. So the answer should be 2πi divided by n factorial.\n\n**Final Answer**\n\\boxed{\\dfrac{2\\pi i}{n!}}\n</think>To calculate the integral\n\\[\n\\int_{C_r} \\frac{e^z \\cos z}{z(z+1)\\dots (z+n)}\\, dz\n\\]\nwhere \\(0 < r < 1\\) and \\(C_r\\) is a circle of radius \\(r\\) traversed counterclockwise, we use the residue theorem.\n\nFirst, we identify the singularities of the integrand. The integrand has singularities at \\(z = 0, -1, -2, \\ldots, -n\\). Since \\(0 < r < 1\\), the only singularity inside the contour \\(C_r\\) is at \\(z = 0\\).\n\nNext, we compute the residue of the integrand at \\(z = 0\\). The integrand can be written as\n\\[\nf(z) = \\frac{e^z \\cos z}{z(z+1)(z+2) \\cdots (z+n)}.\n\\]\nSince \\(z = 0\\) is a simple pole, the residue is given by\n\\[\n\\operatorname{Res}(f, 0) = \\lim_{z \\to 0} z \\cdot \\frac{e^z \\cos z}{z(z+1)(z+2) \\cdots (z+n)}.\n\\]\nSimplifying the expression inside the limit, we get\n\\[\n\\operatorname{Res}(f, 0) = \\lim_{z \\to 0} \\frac{e^z \\cos z}{(z+1)(z+2) \\cdots (z+n)}.\n\\]\nEvaluating this limit at \\(z = 0\\), we obtain\n\\[\n\\operatorname{Res}(f, 0) = \\frac{e^0 \\cos 0}{1 \\cdot 2 \\cdots n} = \\frac{1}{n!}.\n\\]\n\nBy the residue theorem, the integral is \\(2\\pi i\\) times the residue at \\(z = 0\\):\n\\[\n\\int_{C_r} \\frac{e^z \\cos z}{z(z+1)\\dots (z+n)}\\, dz = 2\\pi i \\cdot \\frac{1}{n!} = \\frac{2\\pi i}{n!}.\n\\]\n\nThus, the final answer is\n\\[\n\\boxed{\\frac{2\\pi i}{n!}}.\n\\]"}