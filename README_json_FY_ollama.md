# JSON文件翻译脚本使用说明 - Ollama版

## 脚本概述

`json_FY_ollama.py` 是一个专门用于翻译 `json_chunks` 目录下JSON文件的脚本，使用Ollama API进行高效并发翻译。

## 功能特点

### 🎯 **核心功能**
- 翻译JSON文件中的 `expected_answer`、`problem`、`generated_solution` 字段
- 保持数学公式、阿拉伯数字、符号完全不变
- 保留 `generated_solution` 字段中的 `<think></think>` 标签
- 覆盖写入原字段内容（不添加后缀）

### 🤖 **Ollama集成**
- 自动获取可用模型列表
- 交互式模型选择界面
- 支持多种模型（qwen、llama、mistral等）
- 智能推荐适合中文翻译的模型

### 🚀 **性能优化**
- 支持并发翻译（默认3个文件同时处理）
- Ollama内部并发限制（默认4个请求）
- 自动跳过已翻译的文件（断点续存）
- 进度条显示和实时统计

### 🛡️ **安全保障**
- 自动备份原始文件到 `json_chunks_backup` 目录
- 完整的错误处理和重试机制
- 翻译结果验证（检查中文字符和字段完整性）

### 📝 **长文本处理**
- 智能分块翻译（超过6000字符自动分块）
- 适配Ollama较小的context限制
- 保持数学公式和结构完整性
- 自动合并分块结果

## 使用方法

### 1. 环境准备

确保Ollama服务正在运行：
```bash
# 启动Ollama服务
ollama serve

# 拉取推荐的中文模型（可选）
ollama pull qwen3:8b
ollama pull qwen:14b
```

### 2. 运行翻译脚本

```bash
cd /home/<USER>/WorkSpace/notebooks
python3 json_FY_ollama.py
```

### 3. 脚本执行流程

1. **连接检查**：验证Ollama服务是否可用
2. **模型选择**：显示可用模型列表，支持交互式选择
3. **文件扫描**：扫描 `json_chunks` 目录下的所有JSON文件
4. **备份创建**：自动备份原始文件
5. **翻译确认**：显示任务概览，等待用户确认
6. **并发翻译**：使用多线程并发翻译文件
7. **结果验证**：验证翻译质量和完整性

## 配置参数

### 默认配置
```python
OLLAMA_CONFIG = {
    "base_url": "http://localhost:11434",
    "default_model": "qwen3:8b",
    "max_concurrent": 4,         # Ollama并发数（较低）
    "max_workers": 3,            # 文件处理并发数
    "temperature": 0.1,
    "top_p": 0.9,
    "num_predict": 4096,         # 最大生成token数
    "chunk_threshold": 6000,     # 分块阈值（Ollama context较小）
    "chunk_size": 4000,          # 每块大小
}
```

### 性能调优建议

**对于不同规模的任务：**

- **小规模（< 1000文件）**：
  ```python
  max_concurrent = 4
  max_workers = 3
  ```

- **中等规模（1000-5000文件）**：
  ```python
  max_concurrent = 6
  max_workers = 4
  ```

- **大规模（> 5000文件）**：
  ```python
  max_concurrent = 8
  max_workers = 5
  ```

**注意**：Ollama的并发能力通常比VLLM低，建议保守设置。

## 模型选择指南

### 推荐模型

1. **qwen3:8b** - 默认推荐，最新版本，中文翻译效果好
2. **qwen:14b** - 更好的翻译质量，需要更多资源
3. **llama2:7b** - 通用模型，英文能力强
4. **mistral:7b** - 平衡性能和质量

### 模型选择界面示例
```
可用的Ollama模型:
============================================================
1. qwen3:8b (4.1GB) - 修改时间: 2023-12-08
2. llama2:7b (3.8GB) - 修改时间: 2023-12-07
3. mistral:7b (4.2GB) - 修改时间: 2023-12-09

默认推荐: qwen3:8b (适合中文翻译)

请选择要使用的模型 (输入数字 1-3，或按Enter使用默认模型):
```

## 翻译质量控制

### 完整性检查
- 自动检查 `<think></think>` 标签配对
- 验证翻译结果的完整性
- 检测内容截断问题

### 重试机制
- 网络错误自动重试（最多3次）
- 翻译结果为空时重试
- 指数退避策略避免服务过载

### 断点续存
- 自动检测已翻译文件（中文字符数量）
- 支持中断后继续翻译
- 保持翻译进度状态

## 故障排除

### 常见问题

1. **连接失败**
   ```
   ❌ 无法连接到Ollama服务
   ```
   - 检查Ollama服务是否运行：`ollama serve`
   - 确认端口11434未被占用
   - 检查防火墙设置

2. **模型加载失败**
   ```
   ❌ 未选择有效模型
   ```
   - 确认已安装模型：`ollama list`
   - 拉取推荐模型：`ollama pull qwen3:8b`

3. **翻译质量问题**
   - 尝试使用更大的模型（如qwen:14b）
   - 调整temperature参数（降低随机性）
   - 检查原文是否包含特殊字符

4. **性能问题**
   - 降低并发数（max_concurrent, max_workers）
   - 增加分块大小阈值
   - 确保系统资源充足

### 日志分析

脚本提供详细的日志信息：
- `INFO`: 正常进度信息
- `WARNING`: 潜在问题（如标签不完整）
- `ERROR`: 错误信息（需要处理）
- `DEBUG`: 详细调试信息

## 与VLLM版本的区别

| 特性 | Ollama版 | VLLM版 |
|------|----------|---------|
| 模型选择 | 交互式选择 | 固定模型 |
| 并发能力 | 较低（4-8） | 较高（8-16） |
| Context长度 | 较小（需要分块） | 较大 |
| 部署复杂度 | 简单 | 复杂 |
| 模型支持 | 多样化 | 特定模型 |
| 资源需求 | 较低 | 较高 |

## 最佳实践

1. **首次使用**：先用小批量文件测试
2. **模型选择**：优先选择qwen系列模型
3. **并发设置**：根据系统性能调整
4. **监控进度**：关注日志输出和进度条
5. **备份管理**：定期清理备份目录

## 技术支持

如遇到问题，请检查：
1. Ollama服务状态
2. 模型可用性
3. 系统资源使用情况
4. 网络连接状态
5. 日志错误信息

---

**注意**：本脚本专为数学推理数据集翻译优化，保持数学公式和特殊标签的完整性是核心设计目标。
