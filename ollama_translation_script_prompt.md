# Ollama JSON翻译脚本设计Prompt

## 任务描述

请创建一个基于Ollama API的JSON文件翻译脚本 `json_FY_ollama.py`，参考现有的 `json_FY_vllm.py` 的架构和功能，但适配Ollama的API接口。

## 核心要求

### 1. Ollama API集成
- 使用Ollama API端点：`http://localhost:11434`
- 实现模型列表获取：`GET /api/tags`
- 实现文本生成：`POST /api/generate`
- 支持流式和非流式响应

### 2. 模型选择功能
- 启动时自动获取可用模型列表
- 提供交互式模型选择界面
- 显示模型信息（名称、大小、修改时间等）
- 支持默认模型配置

### 3. 翻译功能保持
- 翻译字段：`expected_answer`, `problem`, `generated_solution`
- 保持数学公式和LaTeX符号不变
- 保持`<think>`和`</think>`标签结构
- 支持长文本分块翻译（超过模型context限制时）

### 4. 继承优化功能
- 智能长文本分块翻译
- `<think>`标签完整性检查
- 并发翻译支持
- 自动备份和断点续存
- 实时进度显示
- 错误处理和重试机制

## 技术规格

### API接口规范

#### 获取模型列表
```http
GET http://localhost:11434/api/tags
Response: {
  "models": [
    {
      "name": "llama2:7b",
      "modified_at": "2023-12-07T09:32:18.757212583Z",
      "size": 3825819519,
      "digest": "sha256:...",
      "details": {
        "format": "gguf",
        "family": "llama",
        "families": ["llama"],
        "parameter_size": "7B",
        "quantization_level": "Q4_0"
      }
    }
  ]
}
```

#### 文本生成
```http
POST http://localhost:11434/api/generate
Content-Type: application/json

{
  "model": "llama2:7b",
  "prompt": "翻译提示词和原文",
  "stream": false,
  "options": {
    "temperature": 0.1,
    "top_p": 0.9,
    "num_predict": 4096
  }
}

Response: {
  "model": "llama2:7b",
  "created_at": "2023-12-07T09:32:18.757212583Z",
  "response": "翻译结果",
  "done": true,
  "context": [...],
  "total_duration": 5589157167,
  "load_duration": 3013701500,
  "prompt_eval_count": 26,
  "prompt_eval_duration": 325953000,
  "eval_count": 290,
  "eval_duration": 2248305000
}
```

### 类结构设计

#### OllamaTranslator类
```python
class OllamaTranslator:
    def __init__(self, base_url="http://localhost:11434", max_concurrent=4):
        self.base_url = base_url
        self.api_url = f"{base_url}/api/generate"
        self.tags_url = f"{base_url}/api/tags"
        self.model_name = None
        self.max_concurrent = max_concurrent
        self.session = requests.Session()
        
    def get_available_models(self) -> List[Dict]:
        """获取可用模型列表"""
        
    def select_model(self) -> str:
        """交互式模型选择"""
        
    def check_ollama_connection(self) -> bool:
        """检查Ollama服务状态"""
        
    def translate_text(self, text: str, field_name: str) -> str:
        """翻译单个文本"""
        
    def translate_json_file(self, json_data: Dict) -> Dict:
        """翻译JSON文件"""
```

### 模型选择界面设计

```
可用的Ollama模型:
========================================
1. llama2:7b (3.8GB) - 修改时间: 2023-12-07
2. qwen:7b (4.1GB) - 修改时间: 2023-12-08  
3. mistral:7b (4.2GB) - 修改时间: 2023-12-09
4. codellama:7b (3.9GB) - 修改时间: 2023-12-10

请选择要使用的模型 (输入数字 1-4，或按Enter使用默认模型): 
```

### 翻译提示词模板

#### 针对不同字段的提示词
```python
PROMPTS = {
    "expected_answer": """请将以下内容翻译成中文，保持数字和符号不变：
{content}

只输出翻译结果，不要添加任何解释：""",

    "problem": """请将以下数学问题翻译成中文，保持所有数学公式、符号、数字不变：
{content}

只输出翻译结果，不要添加任何解释：""",

    "generated_solution": """你是一个专业的数学翻译专家。请将以下英文数学解答完整翻译成中文。

重要说明：
这个内容有特殊结构，包含两个必须都翻译的部分：
第一部分：<think>标签内的思考过程（英文）→ 必须翻译成中文
第二部分：</think>标签后的正式解答（英文）→ 必须翻译成中文

翻译规则：
1. 数学公式、符号、数字保持原样不变
2. <think></think>标签结构必须保持
3. <think>内的英文思考过程 → 翻译成中文
4. </think>后的英文正式解答 → 翻译成中文
5. 保持所有格式、换行、缩进
6. 确保输出完整，包含完整的</think>结束标签

现在翻译以下内容：

{content}

要求：输出完整翻译结果，两个部分都必须是中文，保持原结构，确保</think>标签完整。"""
}
```

### 配置参数

```python
# Ollama配置
OLLAMA_CONFIG = {
    "base_url": "http://localhost:11434",
    "default_model": "qwen:7b",  # 默认模型
    "max_concurrent": 4,         # 并发数（Ollama通常较低）
    "max_workers": 3,            # 文件处理并发数
    "temperature": 0.1,
    "top_p": 0.9,
    "num_predict": 4096,         # 最大生成token数
    "chunk_threshold": 8000,     # 分块阈值（Ollama context较小）
    "chunk_size": 6000,          # 每块大小
}
```

### 错误处理和重试

```python
def translate_with_retry(self, text: str, field_name: str, max_retries: int = 3) -> str:
    """带重试的翻译方法"""
    for attempt in range(max_retries):
        try:
            # 翻译逻辑
            pass
        except requests.exceptions.ConnectionError:
            # Ollama服务连接错误
            pass
        except requests.exceptions.Timeout:
            # 超时错误
            pass
        except Exception as e:
            # 其他错误
            pass
```

### 长文本处理策略

由于Ollama模型通常context长度较小，需要更积极的分块策略：

```python
def split_long_text_ollama(self, text: str, max_chunk_size: int = 6000) -> List[str]:
    """针对Ollama优化的文本分块"""
    # 更小的分块大小
    # 更保守的分割策略
    # 考虑模型的context限制
```

### 性能优化建议

1. **并发控制**：Ollama并发能力有限，建议max_concurrent=4
2. **分块策略**：更小的分块大小，适应较小的context
3. **模型选择**：优先推荐支持中文的模型（如qwen系列）
4. **缓存机制**：可考虑添加翻译结果缓存

### 用户体验优化

1. **模型信息显示**：显示模型大小、类型、适用场景
2. **性能预估**：根据模型类型预估翻译速度
3. **智能推荐**：根据任务类型推荐合适的模型
4. **配置保存**：保存用户的模型选择偏好

### 兼容性考虑

1. **API版本**：支持不同版本的Ollama API
2. **模型格式**：支持不同格式的模型名称
3. **错误码**：处理Ollama特有的错误响应
4. **流式响应**：可选支持流式翻译（实时显示）

## 实现优先级

### 第一阶段（核心功能）
1. Ollama API连接和模型列表获取
2. 基本翻译功能实现
3. 模型选择界面

### 第二阶段（优化功能）
1. 长文本分块翻译
2. 并发处理
3. 错误处理和重试

### 第三阶段（高级功能）
1. `<think>`标签完整性检查
2. 自动备份和断点续存
3. 性能监控和统计

## 测试要求

1. **连接测试**：验证Ollama服务连接
2. **模型测试**：测试不同模型的翻译效果
3. **长文本测试**：验证分块翻译功能
4. **并发测试**：验证并发处理稳定性
5. **完整性测试**：验证`<think>`标签保持完整

请根据以上设计要求实现完整的 `json_FY_ollama.py` 脚本。
