#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
JSON文件翻译脚本 - 使用Ollama API
支持智能长文本分块翻译、并发翻译、断点续存、自动备份、完整性检查等功能

主要特性：
- Ollama API集成，支持模型选择
- 智能长文本分块翻译，适配Ollama context限制
- 自动检测和修复不完整的<think>标签
- 并发翻译提高效率
- 断点续存支持
- 自动备份原始文件
- 完整的错误处理和重试机制
- 翻译完整性验证
"""

import json
import os
import glob
import time
import logging
import requests
import shutil
import re
from typing import Dict, Any, List, Optional
from concurrent.futures import ThreadPoolExecutor, as_completed
from threading import Semaphore
from tqdm import tqdm
from datetime import datetime

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Ollama配置
OLLAMA_CONFIG = {
    "base_url": "http://localhost:11434",
    "default_model": "qwen3:8b",
    "max_concurrent": 4,         # Ollama并发数（较低）
    "max_workers": 3,            # 文件处理并发数
    "temperature": 0.1,
    "top_p": 0.9,
    "num_predict": 4096,         # 最大生成token数
    "chunk_threshold": 6000,     # 分块阈值（Ollama context较小）
    "chunk_size": 4000,          # 每块大小
}

# 配置tqdm进度条样式
def setup_progress_bars():
    """配置进度条的全局样式"""
    tqdm.pandas()
    import warnings
    warnings.filterwarnings("ignore", category=UserWarning, module="tqdm")

class OllamaTranslator:
    """使用Ollama API进行翻译的类，支持并发翻译和长文本分块处理"""

    def __init__(self, base_url: str = None, max_concurrent: int = 4):
        self.base_url = base_url or OLLAMA_CONFIG["base_url"]
        self.api_url = f"{self.base_url}/api/generate"
        self.tags_url = f"{self.base_url}/api/tags"
        self.model_name = None
        self.max_concurrent = max_concurrent
        self.session = requests.Session()
        self.rate_limiter = Semaphore(max_concurrent)

        # 超时设置（Ollama通常响应较慢）
        self.connect_timeout = 30
        self.read_timeout = 300

        # 翻译提示词模板
        self.prompts = {
            "expected_answer": """请将以下内容翻译成中文，保持数字和符号不变：
{content}

只输出翻译结果，不要添加任何解释：""",

            "problem": """请将以下数学问题翻译成中文，保持所有数学公式、符号、数字不变：
{content}

只输出翻译结果，不要添加任何解释：""",

            "generated_solution": """你是一个专业的数学翻译专家。请将以下英文数学解答完整翻译成中文。

重要说明：
这个内容有特殊结构，包含两个必须都翻译的部分：
第一部分：<think>标签内的思考过程（英文）→ 必须翻译成中文
第二部分：</think>标签后的正式解答（英文）→ 必须翻译成中文

翻译规则：
1. 数学公式、符号、数字保持原样不变
2. <think></think>标签结构必须保持
3. <think>内的英文思考过程 → 翻译成中文
4. </think>后的英文正式解答 → 翻译成中文
5. 保持所有格式、换行、缩进
6. 确保输出完整，包含完整的</think>结束标签

现在翻译以下内容：

{content}

要求：输出完整翻译结果，两个部分都必须是中文，保持原结构，确保</think>标签完整。"""
        }

    def get_available_models(self) -> List[Dict]:
        """获取可用模型列表"""
        try:
            response = self.session.get(self.tags_url, timeout=(self.connect_timeout, self.read_timeout))
            response.raise_for_status()

            result = response.json()
            models = result.get('models', [])

            logger.info(f"发现 {len(models)} 个可用模型")
            return models

        except Exception as e:
            logger.error(f"获取模型列表失败: {e}")
            return []

    def select_model(self) -> str:
        """交互式模型选择"""
        models = self.get_available_models()

        if not models:
            logger.warning("未找到可用模型，使用默认模型")
            return OLLAMA_CONFIG["default_model"]

        print("\n可用的Ollama模型:")
        print("=" * 60)

        for i, model in enumerate(models, 1):
            name = model.get('name', 'Unknown')
            size_bytes = model.get('size', 0)
            size_gb = size_bytes / (1024**3) if size_bytes > 0 else 0
            modified_at = model.get('modified_at', '')

            # 格式化修改时间
            if modified_at:
                try:
                    dt = datetime.fromisoformat(modified_at.replace('Z', '+00:00'))
                    modified_str = dt.strftime('%Y-%m-%d')
                except:
                    modified_str = modified_at[:10]
            else:
                modified_str = "Unknown"

            print(f"{i}. {name} ({size_gb:.1f}GB) - 修改时间: {modified_str}")

        print(f"\n默认推荐: {OLLAMA_CONFIG['default_model']} (适合中文翻译)")

        while True:
            try:
                choice = input(f"\n请选择要使用的模型 (输入数字 1-{len(models)}，或按Enter使用默认模型): ").strip()

                if not choice:
                    selected_model = OLLAMA_CONFIG["default_model"]
                    break

                choice_num = int(choice)
                if 1 <= choice_num <= len(models):
                    selected_model = models[choice_num - 1]['name']
                    break
                else:
                    print(f"请输入 1-{len(models)} 之间的数字")

            except ValueError:
                print("请输入有效的数字")
            except KeyboardInterrupt:
                print("\n用户取消选择，使用默认模型")
                selected_model = OLLAMA_CONFIG["default_model"]
                break

        self.model_name = selected_model
        logger.info(f"选择的模型: {selected_model}")
        return selected_model

    def check_ollama_connection(self) -> bool:
        """检查Ollama服务状态"""
        try:
            response = self.session.get(self.tags_url, timeout=(5, 10))
            response.raise_for_status()
            return True
        except Exception as e:
            logger.error(f"Ollama连接检查失败: {e}")
            return False

    def check_think_tags_completeness(self, text: str) -> bool:
        """检查<think>标签的完整性"""
        if not text:
            return True

        # 计算<think>和</think>标签的数量
        think_open_count = text.count('<think>')
        think_close_count = text.count('</think>')

        # 检查标签是否配对
        if think_open_count != think_close_count:
            logger.warning(f"<think>标签不配对: 开始标签{think_open_count}个, 结束标签{think_close_count}个")
            return False

        # 如果有<think>标签，检查是否有内容被截断
        if think_open_count > 0:
            # 检查最后一个</think>标签后是否还有实质内容
            last_close_pos = text.rfind('</think>')
            if last_close_pos != -1:
                remaining_text = text[last_close_pos + 8:].strip()
                # 如果</think>后面还有很多内容，可能被截断了
                if len(remaining_text) > 100 and not remaining_text.endswith(('。', '.', '\\]')):
                    logger.warning("检测到可能的内容截断")
                    return False

        return True

    def split_long_text(self, text: str, max_chunk_size: int = None) -> List[str]:
        """针对Ollama优化的文本分块"""
        if max_chunk_size is None:
            max_chunk_size = OLLAMA_CONFIG["chunk_size"]

        if len(text) <= max_chunk_size:
            return [text]

        chunks = []
        current_chunk = ""

        # 按段落分割
        paragraphs = text.split('\n\n')

        for paragraph in paragraphs:
            # 如果单个段落就超过限制，需要进一步分割
            if len(paragraph) > max_chunk_size:
                # 按句子分割
                sentences = re.split(r'(?<=[.!?。！？])\s+', paragraph)

                for sentence in sentences:
                    if len(current_chunk) + len(sentence) > max_chunk_size:
                        if current_chunk:
                            chunks.append(current_chunk.strip())
                            current_chunk = sentence
                        else:
                            # 单个句子太长，强制分割
                            while len(sentence) > max_chunk_size:
                                chunks.append(sentence[:max_chunk_size])
                                sentence = sentence[max_chunk_size:]
                            current_chunk = sentence
                    else:
                        current_chunk += " " + sentence if current_chunk else sentence
            else:
                if len(current_chunk) + len(paragraph) > max_chunk_size:
                    if current_chunk:
                        chunks.append(current_chunk.strip())
                        current_chunk = paragraph
                    else:
                        current_chunk = paragraph
                else:
                    current_chunk += "\n\n" + paragraph if current_chunk else paragraph

        if current_chunk:
            chunks.append(current_chunk.strip())

        return chunks

    def translate_chunk(self, chunk: str, chunk_index: int, total_chunks: int, field_name: str) -> Optional[str]:
        """翻译单个文本块"""
        prompt = f"""请将以下英文数学内容翻译成中文，保持数学公式、符号和格式不变。这是第{chunk_index+1}部分，共{total_chunks}部分。

要求：
1. 保持所有数学公式和LaTeX符号不变
2. 保持<think>和</think>标签不变
3. 翻译数学推理过程和解释文字
4. 保持原文的逻辑结构和段落格式
5. 只返回翻译后的内容，不要添加任何说明

原文：
{chunk}"""

        payload = {
            "model": self.model_name,
            "prompt": prompt,
            "stream": False,
            "options": {
                "temperature": OLLAMA_CONFIG["temperature"],
                "top_p": OLLAMA_CONFIG["top_p"],
                "num_predict": OLLAMA_CONFIG["num_predict"]
            }
        }

        max_retries = 3
        for attempt in range(max_retries):
            try:
                with self.rate_limiter:
                    response = self.session.post(
                        self.api_url,
                        json=payload,
                        timeout=(self.connect_timeout, self.read_timeout)
                    )
                    response.raise_for_status()

                    result = response.json()
                    translated_text = result.get('response', '').strip()

                    if translated_text:
                        logger.debug(f"✅ 成功翻译块 {chunk_index+1}/{total_chunks}")
                        return translated_text
                    else:
                        logger.warning(f"块 {chunk_index+1} 翻译结果为空 (尝试 {attempt + 1})")

            except Exception as e:
                logger.error(f"翻译块 {chunk_index+1} 错误 (尝试 {attempt + 1}): {e}")
                if attempt < max_retries - 1:
                    time.sleep(2 ** attempt)

        logger.error(f"❌ 块 {chunk_index+1} 翻译失败")
        return None

    def translate_long_text(self, text: str, field_name: str) -> Optional[str]:
        """翻译超长文本，使用分块策略"""
        logger.info(f"字段 {field_name} 使用分块翻译，原文长度: {len(text)} 字符")

        # 分割文本
        chunks = self.split_long_text(text)
        logger.info(f"文本分割为 {len(chunks)} 块")

        # 翻译每个块
        translated_chunks = []
        for i, chunk in enumerate(chunks):
            logger.debug(f"翻译块 {i+1}/{len(chunks)}，长度: {len(chunk)} 字符")

            translated_chunk = self.translate_chunk(chunk, i, len(chunks), field_name)
            if translated_chunk is None:
                logger.error(f"块 {i+1} 翻译失败，停止处理")
                return None

            translated_chunks.append(translated_chunk)

            # 短暂休息避免过载
            time.sleep(1)  # Ollama需要更长的休息时间

        # 合并翻译结果
        result = " ".join(translated_chunks)
        logger.info(f"分块翻译完成，结果长度: {len(result)} 字符")

        return result

    def _translate_field(self, field_name: str, content: str, max_retries: int = 3) -> str:
        """翻译单个字段，支持长文本分块处理"""
        if not content:
            return content

        # 检查是否需要分块翻译（超过阈值的文本）
        if len(content) > OLLAMA_CONFIG["chunk_threshold"]:
            return self.translate_long_text(content, field_name)

        # 使用对应的提示词模板
        prompt = self.prompts.get(field_name, self.prompts["problem"]).format(content=content)

        payload = {
            "model": self.model_name,
            "prompt": prompt,
            "stream": False,
            "options": {
                "temperature": OLLAMA_CONFIG["temperature"],
                "top_p": OLLAMA_CONFIG["top_p"],
                "num_predict": OLLAMA_CONFIG["num_predict"]
            }
        }

        for attempt in range(max_retries):
            try:
                with self.rate_limiter:
                    timeout = (self.connect_timeout, self.read_timeout)

                    response = self.session.post(
                        self.api_url,
                        json=payload,
                        timeout=timeout
                    )
                    response.raise_for_status()

                    result = response.json()
                    translated_text = result.get('response', '').strip()

                    # 验证翻译结果
                    if translated_text and len(translated_text) > 0:
                        # 对于generated_solution字段，检查<think>标签完整性
                        if field_name == "generated_solution":
                            if not self.check_think_tags_completeness(translated_text):
                                logger.warning(f"字段 {field_name} <think>标签不完整 (尝试 {attempt + 1})")
                                if attempt < max_retries - 1:
                                    continue
                        return translated_text
                    else:
                        logger.warning(f"字段 {field_name} 翻译结果为空 (尝试 {attempt + 1})")

            except Exception as e:
                logger.error(f"翻译字段 {field_name} 错误 (尝试 {attempt + 1}): {e}")
                if attempt < max_retries - 1:
                    time.sleep(2 ** attempt)

        logger.error(f"❌ 字段 {field_name} 翻译失败")
        return content  # 返回原文

    def translate_json_file(self, json_data: Dict) -> Optional[Dict]:
        """翻译JSON文件中的指定字段"""
        if not json_data:
            return None

        try:
            # 创建副本避免修改原数据
            translated_data = json_data.copy()

            # 需要翻译的字段
            fields_to_translate = ["expected_answer", "problem", "generated_solution"]

            for field_name in fields_to_translate:
                if field_name in translated_data:
                    original_content = translated_data[field_name]
                    if original_content:
                        logger.debug(f"翻译字段: {field_name}")
                        translated_content = self._translate_field(field_name, str(original_content))
                        translated_data[field_name] = translated_content

                        # 短暂休息
                        time.sleep(0.5)

            return translated_data

        except Exception as e:
            logger.error(f"翻译过程中发生异常: {e}")
            return None


class JsonChunksTranslator:
    """处理json_chunks目录下JSON文件翻译的主类，支持完整性检查和修复"""

    def __init__(self, chunks_dir: str, translator: OllamaTranslator):
        self.chunks_dir = chunks_dir
        self.translator = translator
        self.backup_dir = f"{chunks_dir}_backup"

    def get_json_files(self) -> List[str]:
        """获取json_chunks目录下的所有JSON文件"""
        if not os.path.exists(self.chunks_dir):
            logger.error(f"目录不存在: {self.chunks_dir}")
            return []

        json_files = glob.glob(os.path.join(self.chunks_dir, "*.json"))
        json_files.sort()  # 按文件名排序

        logger.info(f"发现 {len(json_files)} 个JSON文件")
        return json_files

    def backup_files(self, json_files: List[str]) -> bool:
        """备份原始文件"""
        try:
            if os.path.exists(self.backup_dir):
                logger.info(f"备份目录已存在: {self.backup_dir}")
            else:
                os.makedirs(self.backup_dir)
                logger.info(f"创建备份目录: {self.backup_dir}")

            # 备份所有文件
            backup_count = 0
            for file_path in json_files:
                filename = os.path.basename(file_path)
                backup_path = os.path.join(self.backup_dir, filename)

                if not os.path.exists(backup_path):
                    shutil.copy2(file_path, backup_path)
                    backup_count += 1

            logger.info(f"备份了 {backup_count} 个文件到 {self.backup_dir}")
            return True

        except Exception as e:
            logger.error(f"备份文件失败: {e}")
            return False

    def is_file_translated(self, file_path: str) -> bool:
        """检查文件是否已经翻译过（简单的中文字符检测）"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)

            # 检查主要字段是否包含中文
            fields_to_check = ["expected_answer", "problem", "generated_solution"]

            for field in fields_to_check:
                if field in data and data[field]:
                    content = str(data[field])
                    # 简单的中文字符检测
                    chinese_chars = len([c for c in content if '\u4e00' <= c <= '\u9fff'])
                    if chinese_chars > 10:  # 如果有超过10个中文字符，认为已翻译
                        return True

            return False

        except Exception as e:
            logger.debug(f"检查文件翻译状态失败 {file_path}: {e}")
            return False

    def translate_single_file(self, file_path: str) -> bool:
        """翻译单个JSON文件"""
        filename = os.path.basename(file_path)

        try:
            # 检查是否已翻译
            if self.is_file_translated(file_path):
                logger.debug(f"文件 {filename} 已翻译，跳过")
                return True

            # 读取原始数据
            with open(file_path, 'r', encoding='utf-8') as f:
                original_data = json.load(f)

            # 检查原始数据的<think>标签完整性
            generated_solution = original_data.get('generated_solution', '')
            if generated_solution and not self.translator.check_think_tags_completeness(generated_solution):
                logger.warning(f"文件 {filename} 原始数据<think>标签不完整，尝试翻译")

            # 调用翻译API
            translated_data = self.translator.translate_json_file(original_data)

            if translated_data:
                # 再次检查翻译完整性
                generated_solution = translated_data.get('generated_solution', '')
                if not self.translator.check_think_tags_completeness(generated_solution):
                    logger.error(f"翻译后文件 {filename} <think>标签仍不完整")
                    return False

                # 覆盖写入翻译后的数据
                with open(file_path, 'w', encoding='utf-8') as f:
                    json.dump(translated_data, f, ensure_ascii=False, indent=2)

                return True
            else:
                logger.error(f"翻译文件 {filename} 失败：API返回空结果")
                return False

        except Exception as e:
            logger.error(f"翻译文件 {file_path} 失败: {e}")
            return False

    def translate_all_files(self, max_workers: int = 3) -> bool:
        """翻译所有JSON文件，支持完整性检查和自动修复"""
        json_files = self.get_json_files()
        if not json_files:
            logger.error("没有找到JSON文件")
            return False

        # 备份原始文件
        self.backup_files(json_files)

        logger.info(f"开始翻译 {len(json_files)} 个JSON文件")
        logger.info(f"并发数: {max_workers}")

        failed_files = []
        incomplete_files = []
        completed_count = 0
        start_time = time.time()

        # 使用ThreadPoolExecutor进行并发翻译
        with tqdm(total=len(json_files), desc="🌍 翻译进度", unit="文件", colour='green') as pbar:
            with ThreadPoolExecutor(max_workers=max_workers) as executor:
                # 提交所有翻译任务
                future_to_file = {
                    executor.submit(self.translate_single_file, file_path): file_path
                    for file_path in json_files
                }

                # 处理完成的任务
                for future in as_completed(future_to_file):
                    file_path = future_to_file[future]
                    filename = os.path.basename(file_path)

                    try:
                        success = future.result()
                        if success:
                            completed_count += 1
                            pbar.set_postfix({
                                '成功': completed_count,
                                '失败': len(failed_files),
                                '不完整': len(incomplete_files)
                            })
                        else:
                            failed_files.append(file_path)

                    except Exception as e:
                        logger.error(f"处理文件 {filename} 时发生异常: {e}")
                        failed_files.append(file_path)

                    pbar.update(1)

        # 检查翻译完整性
        logger.info("检查翻译完整性...")
        for file_path in json_files:
            if file_path not in failed_files:
                try:
                    with open(file_path, 'r', encoding='utf-8') as f:
                        data = json.load(f)

                    generated_solution = data.get('generated_solution', '')
                    if generated_solution and not self.translator.check_think_tags_completeness(generated_solution):
                        incomplete_files.append(file_path)
                        logger.warning(f"文件 {os.path.basename(file_path)} 翻译不完整")

                except Exception as e:
                    logger.error(f"检查文件 {file_path} 完整性失败: {e}")

        # 显示最终结果
        total_files = len(json_files)
        success_files = total_files - len(failed_files) - len(incomplete_files)
        success_rate = (success_files / total_files * 100) if total_files > 0 else 0

        logger.info(f"\n翻译任务完成!")
        logger.info(f"总文件数: {total_files}")
        logger.info(f"成功翻译: {success_files}")
        logger.info(f"失败文件: {len(failed_files)}")
        logger.info(f"不完整文件: {len(incomplete_files)}")
        logger.info(f"成功率: {success_rate:.1f}%")
        logger.info(f"总耗时: {(time.time() - start_time):.2f} 秒")

        return len(failed_files) == 0 and len(incomplete_files) == 0


def main():
    """主函数"""
    # 配置进度条
    setup_progress_bars()

    # 配置参数
    chunks_dir = "/home/<USER>/WorkSpace/notebooks/json_chunks"
    base_url = OLLAMA_CONFIG["base_url"]
    max_concurrent = OLLAMA_CONFIG["max_concurrent"]
    max_workers = OLLAMA_CONFIG["max_workers"]

    logger.info("开始JSON文件翻译任务 (Ollama版)")
    logger.info("=" * 60)

    print(f"翻译配置:")
    print(f"- Ollama服务地址: {base_url}")
    print(f"- JSON目录: {chunks_dir}")
    print(f"- Ollama并发限制: {max_concurrent}")
    print(f"- 文件处理并发: {max_workers}")
    print(f"- 翻译字段: expected_answer, problem, generated_solution")
    print(f"- 支持功能: 长文本分块翻译、完整性检查、自动修复")

    # 初始化翻译器
    translator = OllamaTranslator(
        base_url=base_url,
        max_concurrent=max_concurrent
    )

    # 检查Ollama连接
    logger.info("🔍 正在检查Ollama服务状态...")
    if not translator.check_ollama_connection():
        logger.error("❌ 无法连接到Ollama服务")
        logger.error("\n🛠️ 请检查以下项目:")
        logger.error("1. Ollama服务是否正在运行在11434端口")
        logger.error("2. 是否有可用的模型")
        logger.error("3. 服务地址是否正确 (http://localhost:11434)")
        logger.error("4. 服务器资源是否充足 (CPU/内存)")
        logger.error("5. 是否有其他进程占用端口")
        return
    else:
        logger.info("✅ Ollama服务连接正常")

    # 模型选择
    logger.info("🤖 选择翻译模型...")
    selected_model = translator.select_model()
    if not selected_model:
        logger.error("❌ 未选择有效模型")
        return

    # 初始化JSON文件处理器
    processor = JsonChunksTranslator(chunks_dir, translator)

    # 获取文件列表
    json_files = processor.get_json_files()
    if not json_files:
        logger.error("没有找到JSON文件，请检查目录路径")
        return

    # 显示任务概览
    print(f"\n翻译任务概览:")
    print(f"- 选择的模型: {selected_model}")
    print(f"- 总文件数: {len(json_files)}")
    print(f"- 翻译字段: expected_answer, problem, generated_solution")
    print(f"- 文件并发数: {max_workers}")
    print(f"- 支持断点续存: 是（自动跳过已翻译文件）")
    print(f"- 自动备份: 是（备份到 {processor.backup_dir}）")
    print(f"- 长文本处理: 是（自动分块翻译超过{OLLAMA_CONFIG['chunk_threshold']}字符的文本）")
    print(f"- 完整性检查: 是（自动检查和修复<think>标签）")

    # 询问用户确认
    user_input = input(f"\n是否开始翻译？(y/n): ")
    if user_input.lower() != 'y':
        logger.info("用户取消翻译任务")
        return

    # 开始翻译
    start_time = time.time()
    success = processor.translate_all_files(max_workers=max_workers)

    # 显示最终结果
    total_time = time.time() - start_time
    logger.info(f"总耗时: {total_time:.2f} 秒")

    if success:
        logger.info("🎉 翻译任务全部成功完成!")

        # 显示翻译结果预览
        try:
            sample_file = json_files[0]
            with open(sample_file, 'r', encoding='utf-8') as f:
                sample_data = json.load(f)

            logger.info("\n翻译结果预览:")
            for field in ["expected_answer", "problem", "generated_solution"]:
                if field in sample_data:
                    translated = sample_data[field]
                    preview_text = str(translated)[:200] + "..." if len(str(translated)) > 200 else str(translated)
                    logger.info(f"\n{field} (翻译后):")
                    logger.info(f"{preview_text}")
        except Exception as e:
            logger.error(f"读取预览结果失败: {e}")
    else:
        logger.error("翻译任务未完全成功，请检查日志了解详情")

    logger.info("任务完成!")


if __name__ == "__main__":
    main()